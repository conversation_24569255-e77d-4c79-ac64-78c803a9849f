{"version": 3, "sources": ["../../../../../../node_modules/@xterm/addon-fit/lib/addon-fit.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.FitAddon=t():e.<PERSON>t<PERSON>on=t()}(self,(()=>(()=>{\"use strict\";var e={};return(()=>{var t=e;Object.defineProperty(t,\"__esModule\",{value:!0}),t.<PERSON><PERSON><PERSON>=void 0,t.<PERSON>tAddon=class{activate(e){this._terminal=e}dispose(){}fit(){const e=this.proposeDimensions();if(!e||!this._terminal||isNaN(e.cols)||isNaN(e.rows))return;const t=this._terminal._core;this._terminal.rows===e.rows&&this._terminal.cols===e.cols||(t._renderService.clear(),this._terminal.resize(e.cols,e.rows))}proposeDimensions(){if(!this._terminal)return;if(!this._terminal.element||!this._terminal.element.parentElement)return;const e=this._terminal._core,t=e._renderService.dimensions;if(0===t.css.cell.width||0===t.css.cell.height)return;const r=0===this._terminal.options.scrollback?0:e.viewport.scrollBarWidth,i=window.getComputedStyle(this._terminal.element.parentElement),o=parseInt(i.getPropertyValue(\"height\")),s=Math.max(0,parseInt(i.getPropertyValue(\"width\"))),n=window.getComputedStyle(this._terminal.element),l=o-(parseInt(n.getPropertyValue(\"padding-top\"))+parseInt(n.getPropertyValue(\"padding-bottom\"))),a=s-(parseInt(n.getPropertyValue(\"padding-right\"))+parseInt(n.getPropertyValue(\"padding-left\")))-r;return{cols:Math.max(2,Math.floor(a/t.css.cell.width)),rows:Math.max(1,Math.floor(l/t.css.cell.height))}}}})(),e})()));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,WAAS,EAAE,IAAE,EAAE,WAAS,EAAE;AAAA,IAAC,EAAE,MAAM,OAAK,MAAI;AAAC;AAAa,UAAI,IAAE,CAAC;AAAE,cAAO,MAAI;AAAC,YAAI,IAAE;AAAE,eAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,WAAS,QAAO,EAAE,WAAS,MAAK;AAAA,UAAC,SAASA,IAAE;AAAC,iBAAK,YAAUA;AAAA,UAAC;AAAA,UAAC,UAAS;AAAA,UAAC;AAAA,UAAC,MAAK;AAAC,kBAAMA,KAAE,KAAK,kBAAkB;AAAE,gBAAG,CAACA,MAAG,CAAC,KAAK,aAAW,MAAMA,GAAE,IAAI,KAAG,MAAMA,GAAE,IAAI,EAAE;AAAO,kBAAMC,KAAE,KAAK,UAAU;AAAM,iBAAK,UAAU,SAAOD,GAAE,QAAM,KAAK,UAAU,SAAOA,GAAE,SAAOC,GAAE,eAAe,MAAM,GAAE,KAAK,UAAU,OAAOD,GAAE,MAAKA,GAAE,IAAI;AAAA,UAAE;AAAA,UAAC,oBAAmB;AAAC,gBAAG,CAAC,KAAK,UAAU;AAAO,gBAAG,CAAC,KAAK,UAAU,WAAS,CAAC,KAAK,UAAU,QAAQ,cAAc;AAAO,kBAAMA,KAAE,KAAK,UAAU,OAAMC,KAAED,GAAE,eAAe;AAAW,gBAAG,MAAIC,GAAE,IAAI,KAAK,SAAO,MAAIA,GAAE,IAAI,KAAK,OAAO;AAAO,kBAAM,IAAE,MAAI,KAAK,UAAU,QAAQ,aAAW,IAAED,GAAE,SAAS,gBAAe,IAAE,OAAO,iBAAiB,KAAK,UAAU,QAAQ,aAAa,GAAE,IAAE,SAAS,EAAE,iBAAiB,QAAQ,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,SAAS,EAAE,iBAAiB,OAAO,CAAC,CAAC,GAAE,IAAE,OAAO,iBAAiB,KAAK,UAAU,OAAO,GAAE,IAAE,KAAG,SAAS,EAAE,iBAAiB,aAAa,CAAC,IAAE,SAAS,EAAE,iBAAiB,gBAAgB,CAAC,IAAG,IAAE,KAAG,SAAS,EAAE,iBAAiB,eAAe,CAAC,IAAE,SAAS,EAAE,iBAAiB,cAAc,CAAC,KAAG;AAAE,mBAAM,EAAC,MAAK,KAAK,IAAI,GAAE,KAAK,MAAM,IAAEC,GAAE,IAAI,KAAK,KAAK,CAAC,GAAE,MAAK,KAAK,IAAI,GAAE,KAAK,MAAM,IAAEA,GAAE,IAAI,KAAK,MAAM,CAAC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAG,GAAE;AAAA,IAAC,GAAG,CAAE;AAAA;AAAA;", "names": ["e", "t"]}