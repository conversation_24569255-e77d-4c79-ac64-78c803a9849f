{"version": 3, "sources": ["../../../../../../node_modules/@ngrx/signals/fesm2022/ngrx-signals.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { untracked, isSignal, computed, assertInInjectionContext, inject, Injector, effect, DestroyRef, signal, Injectable, linkedSignal } from '@angular/core';\nconst DEEP_SIGNAL = Symbol('DEEP_SIGNAL');\nfunction toDeepSignal(signal) {\n  return new Proxy(signal, {\n    has(target, prop) {\n      return !!this.get(target, prop, undefined);\n    },\n    get(target, prop) {\n      const value = untracked(target);\n      if (!isRecord(value) || !(prop in value)) {\n        if (isSignal(target[prop]) && target[prop][DEEP_SIGNAL]) {\n          delete target[prop];\n        }\n        return target[prop];\n      }\n      if (!isSignal(target[prop])) {\n        Object.defineProperty(target, prop, {\n          value: computed(() => target()[prop]),\n          configurable: true\n        });\n        target[prop][DEEP_SIGNAL] = true;\n      }\n      return toDeepSignal(target[prop]);\n    }\n  });\n}\nconst nonRecords = [WeakSet, WeakMap, Promise, Date, Error, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>View, Function];\nfunction isRecord(value) {\n  if (value === null || typeof value !== 'object' || isIterable(value)) {\n    return false;\n  }\n  let proto = Object.getPrototypeOf(value);\n  if (proto === Object.prototype) {\n    return true;\n  }\n  while (proto && proto !== Object.prototype) {\n    if (nonRecords.includes(proto.constructor)) {\n      return false;\n    }\n    proto = Object.getPrototypeOf(proto);\n  }\n  return proto === Object.prototype;\n}\nfunction isIterable(value) {\n  return typeof value?.[Symbol.iterator] === 'function';\n}\nfunction deepComputed(computation) {\n  return toDeepSignal(computed(computation));\n}\nfunction signalMethod(processingFn, config) {\n  if (!config?.injector) {\n    assertInInjectionContext(signalMethod);\n  }\n  const watchers = [];\n  const sourceInjector = config?.injector ?? inject(Injector);\n  const signalMethodFn = (input, config) => {\n    if (isSignal(input)) {\n      const callerInjector = getCallerInjector();\n      if (typeof ngDevMode !== 'undefined' && ngDevMode && config?.injector === undefined && callerInjector === undefined) {\n        console.warn('@ngrx/signals: The function returned by signalMethod was called', 'outside the injection context with a signal. This may lead to', 'a memory leak. Make sure to call it within the injection context', '(e.g. in a constructor or field initializer) or pass an injector', 'explicitly via the config parameter.\\n\\nFor more information, see:', 'https://ngrx.io/guide/signals/signal-method#automatic-cleanup');\n      }\n      const instanceInjector = config?.injector ?? callerInjector ?? sourceInjector;\n      const watcher = effect(() => {\n        const value = input();\n        untracked(() => processingFn(value));\n      }, {\n        injector: instanceInjector\n      });\n      watchers.push(watcher);\n      instanceInjector.get(DestroyRef).onDestroy(() => {\n        const ix = watchers.indexOf(watcher);\n        if (ix !== -1) {\n          watchers.splice(ix, 1);\n        }\n      });\n      return watcher;\n    } else {\n      processingFn(input);\n      return {\n        destroy: () => void true\n      };\n    }\n  };\n  signalMethodFn.destroy = () => watchers.forEach(watcher => watcher.destroy());\n  return signalMethodFn;\n}\nfunction getCallerInjector() {\n  try {\n    return inject(Injector);\n  } catch {\n    return undefined;\n  }\n}\nconst STATE_WATCHERS = new WeakMap();\nconst STATE_SOURCE = Symbol('STATE_SOURCE');\nfunction isWritableSignal(value) {\n  return isSignal(value) && 'set' in value && 'update' in value && typeof value.set === 'function' && typeof value.update === 'function';\n}\nfunction isWritableStateSource(stateSource) {\n  const signals = stateSource[STATE_SOURCE];\n  return Reflect.ownKeys(stateSource[STATE_SOURCE]).every(key => {\n    return isWritableSignal(signals[key]);\n  });\n}\nfunction patchState(stateSource, ...updaters) {\n  const currentState = untracked(() => getState(stateSource));\n  const newState = updaters.reduce((nextState, updater) => ({\n    ...nextState,\n    ...(typeof updater === 'function' ? updater(nextState) : updater)\n  }), currentState);\n  const signals = stateSource[STATE_SOURCE];\n  const stateKeys = Reflect.ownKeys(stateSource[STATE_SOURCE]);\n  for (const key of Reflect.ownKeys(newState)) {\n    if (stateKeys.includes(key)) {\n      const signalKey = key;\n      if (currentState[signalKey] !== newState[signalKey]) {\n        signals[signalKey].set(newState[signalKey]);\n      }\n    } else if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n      console.warn(`@ngrx/signals: patchState was called with an unknown state slice '${String(key)}'.`, 'Ensure that all state properties are explicitly defined in the initial state.', 'Updates to properties not present in the initial state will be ignored.');\n    }\n  }\n  notifyWatchers(stateSource);\n}\nfunction getState(stateSource) {\n  const signals = stateSource[STATE_SOURCE];\n  return Reflect.ownKeys(stateSource[STATE_SOURCE]).reduce((state, key) => {\n    const value = signals[key]();\n    return {\n      ...state,\n      [key]: value\n    };\n  }, {});\n}\nfunction watchState(stateSource, watcher, config) {\n  if (!config?.injector) {\n    assertInInjectionContext(watchState);\n  }\n  const injector = config?.injector ?? inject(Injector);\n  const destroyRef = injector.get(DestroyRef);\n  addWatcher(stateSource, watcher);\n  watcher(getState(stateSource));\n  const destroy = () => removeWatcher(stateSource, watcher);\n  destroyRef.onDestroy(destroy);\n  return {\n    destroy\n  };\n}\nfunction getWatchers(stateSource) {\n  return STATE_WATCHERS.get(stateSource[STATE_SOURCE]) || [];\n}\nfunction notifyWatchers(stateSource) {\n  const watchers = getWatchers(stateSource);\n  for (const watcher of watchers) {\n    const state = untracked(() => getState(stateSource));\n    watcher(state);\n  }\n}\nfunction addWatcher(stateSource, watcher) {\n  const watchers = getWatchers(stateSource);\n  STATE_WATCHERS.set(stateSource[STATE_SOURCE], [...watchers, watcher]);\n}\nfunction removeWatcher(stateSource, watcher) {\n  const watchers = getWatchers(stateSource);\n  STATE_WATCHERS.set(stateSource[STATE_SOURCE], watchers.filter(w => w !== watcher));\n}\nfunction signalState(initialState) {\n  const stateKeys = Reflect.ownKeys(initialState);\n  const stateSource = stateKeys.reduce((signalsDict, key) => ({\n    ...signalsDict,\n    [key]: signal(initialState[key])\n  }), {});\n  const signalState = computed(() => stateKeys.reduce((state, key) => ({\n    ...state,\n    [key]: stateSource[key]()\n  }), {}));\n  Object.defineProperty(signalState, STATE_SOURCE, {\n    value: stateSource\n  });\n  for (const key of stateKeys) {\n    Object.defineProperty(signalState, key, {\n      value: toDeepSignal(stateSource[key])\n    });\n  }\n  return signalState;\n}\nfunction signalStore(...args) {\n  const signalStoreArgs = [...args];\n  const config = typeof signalStoreArgs[0] === 'function' ? {} : signalStoreArgs.shift();\n  const features = signalStoreArgs;\n  class SignalStore {\n    constructor() {\n      const innerStore = features.reduce((store, feature) => feature(store), getInitialInnerStore());\n      const {\n        stateSignals,\n        props,\n        methods,\n        hooks\n      } = innerStore;\n      const storeMembers = {\n        ...stateSignals,\n        ...props,\n        ...methods\n      };\n      this[STATE_SOURCE] = innerStore[STATE_SOURCE];\n      for (const key of Reflect.ownKeys(storeMembers)) {\n        this[key] = storeMembers[key];\n      }\n      const {\n        onInit,\n        onDestroy\n      } = hooks;\n      if (onInit) {\n        onInit();\n      }\n      if (onDestroy) {\n        inject(DestroyRef).onDestroy(onDestroy);\n      }\n    }\n    /** @nocollapse */\n    static ɵfac = function SignalStore_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SignalStore)();\n    };\n    /** @nocollapse */\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SignalStore,\n      factory: SignalStore.ɵfac,\n      providedIn: config.providedIn || null\n    });\n  }\n  (() => {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SignalStore, [{\n      type: Injectable,\n      args: [{\n        providedIn: config.providedIn || null\n      }]\n    }], () => [], null);\n  })();\n  return SignalStore;\n}\nfunction getInitialInnerStore() {\n  return {\n    [STATE_SOURCE]: {},\n    stateSignals: {},\n    props: {},\n    methods: {},\n    hooks: {}\n  };\n}\nfunction signalStoreFeature(...args) {\n  const features = typeof args[0] === 'function' ? args : args.slice(1);\n  return inputStore => features.reduce((store, feature) => feature(store), inputStore);\n}\nfunction type() {\n  return undefined;\n}\nfunction assertUniqueStoreMembers(store, newMemberKeys) {\n  if (typeof ngDevMode === 'undefined' || !ngDevMode) {\n    return;\n  }\n  const storeMembers = {\n    ...store.stateSignals,\n    ...store.props,\n    ...store.methods\n  };\n  const overriddenKeys = Reflect.ownKeys(storeMembers).filter(memberKey => newMemberKeys.includes(memberKey));\n  if (overriddenKeys.length > 0) {\n    console.warn('@ngrx/signals: SignalStore members cannot be overridden.', 'Trying to override:', overriddenKeys.map(key => String(key)).join(', '));\n  }\n}\nfunction withProps(propsFactory) {\n  return store => {\n    const props = propsFactory({\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    });\n    assertUniqueStoreMembers(store, Reflect.ownKeys(props));\n    return {\n      ...store,\n      props: {\n        ...store.props,\n        ...props\n      }\n    };\n  };\n}\nfunction withComputed(computedFactory) {\n  return withProps(store => {\n    const computedResult = computedFactory(store);\n    const computedResultKeys = Reflect.ownKeys(computedResult);\n    return computedResultKeys.reduce((prev, key) => {\n      const signalOrComputation = computedResult[key];\n      return {\n        ...prev,\n        [key]: isSignal(signalOrComputation) ? signalOrComputation : computed(signalOrComputation)\n      };\n    }, {});\n  });\n}\n\n/**\n * @description\n * Allows passing properties, methods, or signals from a SignalStore\n * to a feature.\n *\n * @usageNotes\n * ```typescript\n * signalStore(\n *   withMethods((store) => ({\n *     load(id: number): Observable<Entity> {\n *       return of({ id, name: 'John' });\n *     },\n *   })),\n *   withFeature(\n *     // 👇 has full access to the store\n *     (store) => withEntityLoader((id) => firstValueFrom(store.load(id)))\n *   )\n * );\n * ```\n *\n * @param featureFactory function returning the actual feature\n */\nfunction withFeature(featureFactory) {\n  return store => {\n    const storeForFactory = {\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store['stateSignals'],\n      ...store['props'],\n      ...store['methods']\n    };\n    return featureFactory(storeForFactory)(store);\n  };\n}\nfunction withHooks(hooksOrFactory) {\n  return store => {\n    const storeMembers = {\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    };\n    const hooks = typeof hooksOrFactory === 'function' ? hooksOrFactory(storeMembers) : hooksOrFactory;\n    const createHook = name => {\n      const hook = hooks[name];\n      const currentHook = store.hooks[name];\n      return hook ? () => {\n        if (currentHook) {\n          currentHook();\n        }\n        hook(storeMembers);\n      } : currentHook;\n    };\n    return {\n      ...store,\n      hooks: {\n        onInit: createHook('onInit'),\n        onDestroy: createHook('onDestroy')\n      }\n    };\n  };\n}\n\n/**\n * @description\n *\n * Adds linked state slices to a SignalStore.\n *\n * @usageNotes\n *\n * ```typescript\n * const OptionsStore = signalStore(\n *   withState({ options: [1, 2, 3] }),\n *   withLinkedState(({ options }) => ({\n *     selectedOption: () => options()[0],\n *   }))\n * );\n * ```\n *\n * This returns a state of type `{ options: number[], selectedOption: number | undefined }`.\n * When the `options` signal changes, the `selectedOption` automatically updates.\n *\n * For advanced use cases, `linkedSignal` or any other `WritableSignal` instance can be used within `withLinkedState`:\n *\n * ```typescript\n * type Option = { id: number; label: string };\n *\n * const OptionsStore = signalStore(\n *   withState({ options: [] as Option[] }),\n *   withLinkedState(({ options }) => ({\n *     selectedOption: linkedSignal<Option[], Option>({\n *       source: options,\n *       computation: (newOptions, previous) => {\n *         const option = newOptions.find((o) => o.id === previous?.value.id);\n *         return option ?? newOptions[0];\n *       },\n *     })\n *   }))\n * )\n * ```\n *\n * @param linkedStateFactory A function that returns an object literal with properties containing an actual `linkedSignal` or the computation function.\n */\nfunction withLinkedState(linkedStateFactory) {\n  return store => {\n    const linkedState = linkedStateFactory({\n      ...store.stateSignals,\n      ...store.props\n    });\n    const stateKeys = Reflect.ownKeys(linkedState);\n    const stateSource = store[STATE_SOURCE];\n    const stateSignals = {};\n    for (const key of stateKeys) {\n      const signalOrComputationFn = linkedState[key];\n      stateSource[key] = isWritableSignal(signalOrComputationFn) ? signalOrComputationFn : linkedSignal(signalOrComputationFn);\n      stateSignals[key] = toDeepSignal(stateSource[key]);\n    }\n    return {\n      ...store,\n      stateSignals: {\n        ...store.stateSignals,\n        ...stateSignals\n      }\n    };\n  };\n}\nfunction withMethods(methodsFactory) {\n  return store => {\n    const methods = methodsFactory({\n      [STATE_SOURCE]: store[STATE_SOURCE],\n      ...store.stateSignals,\n      ...store.props,\n      ...store.methods\n    });\n    assertUniqueStoreMembers(store, Reflect.ownKeys(methods));\n    return {\n      ...store,\n      methods: {\n        ...store.methods,\n        ...methods\n      }\n    };\n  };\n}\nfunction withState(stateOrFactory) {\n  return store => {\n    const state = typeof stateOrFactory === 'function' ? stateOrFactory() : stateOrFactory;\n    const stateKeys = Reflect.ownKeys(state);\n    assertUniqueStoreMembers(store, stateKeys);\n    const stateSource = store[STATE_SOURCE];\n    const stateSignals = {};\n    for (const key of stateKeys) {\n      stateSource[key] = signal(state[key]);\n      stateSignals[key] = toDeepSignal(stateSource[key]);\n    }\n    return {\n      ...store,\n      stateSignals: {\n        ...store.stateSignals,\n        ...stateSignals\n      }\n    };\n  };\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { deepComputed, getState, isWritableStateSource, patchState, signalMethod, signalState, signalStore, signalStoreFeature, type, watchState, withComputed, withFeature, withHooks, withLinkedState, withMethods, withProps, withState };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,cAAc,OAAO,aAAa;AACxC,SAAS,aAAaA,SAAQ;AAC5B,SAAO,IAAI,MAAMA,SAAQ;AAAA,IACvB,IAAI,QAAQ,MAAM;AAChB,aAAO,CAAC,CAAC,KAAK,IAAI,QAAQ,MAAM,MAAS;AAAA,IAC3C;AAAA,IACA,IAAI,QAAQ,MAAM;AAChB,YAAM,QAAQ,UAAU,MAAM;AAC9B,UAAI,CAAC,SAAS,KAAK,KAAK,EAAE,QAAQ,QAAQ;AACxC,YAAI,SAAS,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,EAAE,WAAW,GAAG;AACvD,iBAAO,OAAO,IAAI;AAAA,QACpB;AACA,eAAO,OAAO,IAAI;AAAA,MACpB;AACA,UAAI,CAAC,SAAS,OAAO,IAAI,CAAC,GAAG;AAC3B,eAAO,eAAe,QAAQ,MAAM;AAAA,UAClC,OAAO,SAAS,MAAM,OAAO,EAAE,IAAI,CAAC;AAAA,UACpC,cAAc;AAAA,QAChB,CAAC;AACD,eAAO,IAAI,EAAE,WAAW,IAAI;AAAA,MAC9B;AACA,aAAO,aAAa,OAAO,IAAI,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AACA,IAAM,aAAa,CAAC,SAAS,SAAS,SAAS,MAAM,OAAO,QAAQ,aAAa,UAAU,QAAQ;AACnG,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,WAAW,KAAK,GAAG;AACpE,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,OAAO,eAAe,KAAK;AACvC,MAAI,UAAU,OAAO,WAAW;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,UAAU,OAAO,WAAW;AAC1C,QAAI,WAAW,SAAS,MAAM,WAAW,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,YAAQ,OAAO,eAAe,KAAK;AAAA,EACrC;AACA,SAAO,UAAU,OAAO;AAC1B;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAC7C;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,aAAa,SAAS,WAAW,CAAC;AAC3C;AACA,SAAS,aAAa,cAAc,QAAQ;AAC1C,MAAI,CAAC,QAAQ,UAAU;AACrB,6BAAyB,YAAY;AAAA,EACvC;AACA,QAAM,WAAW,CAAC;AAClB,QAAM,iBAAiB,QAAQ,YAAY,OAAO,QAAQ;AAC1D,QAAM,iBAAiB,CAAC,OAAOC,YAAW;AACxC,QAAI,SAAS,KAAK,GAAG;AACnB,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,OAAO,cAAc,eAAe,aAAaA,SAAQ,aAAa,UAAa,mBAAmB,QAAW;AACnH,gBAAQ,KAAK,mEAAmE,iEAAiE,oEAAoE,oEAAoE,sEAAsE,+DAA+D;AAAA,MACha;AACA,YAAM,mBAAmBA,SAAQ,YAAY,kBAAkB;AAC/D,YAAM,UAAU,OAAO,MAAM;AAC3B,cAAM,QAAQ,MAAM;AACpB,kBAAU,MAAM,aAAa,KAAK,CAAC;AAAA,MACrC,GAAG;AAAA,QACD,UAAU;AAAA,MACZ,CAAC;AACD,eAAS,KAAK,OAAO;AACrB,uBAAiB,IAAI,UAAU,EAAE,UAAU,MAAM;AAC/C,cAAM,KAAK,SAAS,QAAQ,OAAO;AACnC,YAAI,OAAO,IAAI;AACb,mBAAS,OAAO,IAAI,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,OAAO;AACL,mBAAa,KAAK;AAClB,aAAO;AAAA,QACL,SAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,iBAAe,UAAU,MAAM,SAAS,QAAQ,aAAW,QAAQ,QAAQ,CAAC;AAC5E,SAAO;AACT;AACA,SAAS,oBAAoB;AAC3B,MAAI;AACF,WAAO,OAAO,QAAQ;AAAA,EACxB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAM,eAAe,OAAO,cAAc;AAC1C,SAAS,iBAAiB,OAAO;AAC/B,SAAO,SAAS,KAAK,KAAK,SAAS,SAAS,YAAY,SAAS,OAAO,MAAM,QAAQ,cAAc,OAAO,MAAM,WAAW;AAC9H;AACA,SAAS,sBAAsB,aAAa;AAC1C,QAAM,UAAU,YAAY,YAAY;AACxC,SAAO,QAAQ,QAAQ,YAAY,YAAY,CAAC,EAAE,MAAM,SAAO;AAC7D,WAAO,iBAAiB,QAAQ,GAAG,CAAC;AAAA,EACtC,CAAC;AACH;AACA,SAAS,WAAW,gBAAgB,UAAU;AAC5C,QAAM,eAAe,UAAU,MAAM,SAAS,WAAW,CAAC;AAC1D,QAAM,WAAW,SAAS,OAAO,CAAC,WAAW,YAAa,kCACrD,YACC,OAAO,YAAY,aAAa,QAAQ,SAAS,IAAI,UACvD,YAAY;AAChB,QAAM,UAAU,YAAY,YAAY;AACxC,QAAM,YAAY,QAAQ,QAAQ,YAAY,YAAY,CAAC;AAC3D,aAAW,OAAO,QAAQ,QAAQ,QAAQ,GAAG;AAC3C,QAAI,UAAU,SAAS,GAAG,GAAG;AAC3B,YAAM,YAAY;AAClB,UAAI,aAAa,SAAS,MAAM,SAAS,SAAS,GAAG;AACnD,gBAAQ,SAAS,EAAE,IAAI,SAAS,SAAS,CAAC;AAAA,MAC5C;AAAA,IACF,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,cAAQ,KAAK,qEAAqE,OAAO,GAAG,CAAC,MAAM,iFAAiF,yEAAyE;AAAA,IAC/P;AAAA,EACF;AACA,iBAAe,WAAW;AAC5B;AACA,SAAS,SAAS,aAAa;AAC7B,QAAM,UAAU,YAAY,YAAY;AACxC,SAAO,QAAQ,QAAQ,YAAY,YAAY,CAAC,EAAE,OAAO,CAAC,OAAO,QAAQ;AACvE,UAAM,QAAQ,QAAQ,GAAG,EAAE;AAC3B,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,GAAG,GAAG;AAAA,IACT;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,WAAW,aAAa,SAAS,QAAQ;AAChD,MAAI,CAAC,QAAQ,UAAU;AACrB,6BAAyB,UAAU;AAAA,EACrC;AACA,QAAM,WAAW,QAAQ,YAAY,OAAO,QAAQ;AACpD,QAAM,aAAa,SAAS,IAAI,UAAU;AAC1C,aAAW,aAAa,OAAO;AAC/B,UAAQ,SAAS,WAAW,CAAC;AAC7B,QAAM,UAAU,MAAM,cAAc,aAAa,OAAO;AACxD,aAAW,UAAU,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACA,SAAS,YAAY,aAAa;AAChC,SAAO,eAAe,IAAI,YAAY,YAAY,CAAC,KAAK,CAAC;AAC3D;AACA,SAAS,eAAe,aAAa;AACnC,QAAM,WAAW,YAAY,WAAW;AACxC,aAAW,WAAW,UAAU;AAC9B,UAAM,QAAQ,UAAU,MAAM,SAAS,WAAW,CAAC;AACnD,YAAQ,KAAK;AAAA,EACf;AACF;AACA,SAAS,WAAW,aAAa,SAAS;AACxC,QAAM,WAAW,YAAY,WAAW;AACxC,iBAAe,IAAI,YAAY,YAAY,GAAG,CAAC,GAAG,UAAU,OAAO,CAAC;AACtE;AACA,SAAS,cAAc,aAAa,SAAS;AAC3C,QAAM,WAAW,YAAY,WAAW;AACxC,iBAAe,IAAI,YAAY,YAAY,GAAG,SAAS,OAAO,OAAK,MAAM,OAAO,CAAC;AACnF;AACA,SAAS,YAAY,cAAc;AACjC,QAAM,YAAY,QAAQ,QAAQ,YAAY;AAC9C,QAAM,cAAc,UAAU,OAAO,CAAC,aAAa,QAAS,iCACvD,cADuD;AAAA,IAE1D,CAAC,GAAG,GAAG,OAAO,aAAa,GAAG,CAAC;AAAA,EACjC,IAAI,CAAC,CAAC;AACN,QAAMC,eAAc,SAAS,MAAM,UAAU,OAAO,CAAC,OAAO,QAAS,iCAChE,QADgE;AAAA,IAEnE,CAAC,GAAG,GAAG,YAAY,GAAG,EAAE;AAAA,EAC1B,IAAI,CAAC,CAAC,CAAC;AACP,SAAO,eAAeA,cAAa,cAAc;AAAA,IAC/C,OAAO;AAAA,EACT,CAAC;AACD,aAAW,OAAO,WAAW;AAC3B,WAAO,eAAeA,cAAa,KAAK;AAAA,MACtC,OAAO,aAAa,YAAY,GAAG,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACA,SAAOA;AACT;AACA,SAAS,eAAe,MAAM;AAC5B,QAAM,kBAAkB,CAAC,GAAG,IAAI;AAChC,QAAM,SAAS,OAAO,gBAAgB,CAAC,MAAM,aAAa,CAAC,IAAI,gBAAgB,MAAM;AACrF,QAAM,WAAW;AAAA,EACjB,MAAM,YAAY;AAAA,IAChB,cAAc;AACZ,YAAM,aAAa,SAAS,OAAO,CAAC,OAAO,YAAY,QAAQ,KAAK,GAAG,qBAAqB,CAAC;AAC7F,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,iDAChB,eACA,QACA;AAEL,WAAK,YAAY,IAAI,WAAW,YAAY;AAC5C,iBAAW,OAAO,QAAQ,QAAQ,YAAY,GAAG;AAC/C,aAAK,GAAG,IAAI,aAAa,GAAG;AAAA,MAC9B;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AACA,UAAI,WAAW;AACb,eAAO,UAAU,EAAE,UAAU,SAAS;AAAA,MACxC;AAAA,IACF;AAAA;AAAA,IAEA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,aAAa;AAAA,IAChD;AAAA;AAAA,IAEA,OAAO,QAA0B,mBAAmB;AAAA,MAClD,OAAO;AAAA,MACP,SAAS,YAAY;AAAA,MACrB,YAAY,OAAO,cAAc;AAAA,IACnC,CAAC;AAAA,EACH;AACA,GAAC,MAAM;AACL,KAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,MACpF,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,YAAY,OAAO,cAAc;AAAA,MACnC,CAAC;AAAA,IACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AAAA,EACpB,GAAG;AACH,SAAO;AACT;AACA,SAAS,uBAAuB;AAC9B,SAAO;AAAA,IACL,CAAC,YAAY,GAAG,CAAC;AAAA,IACjB,cAAc,CAAC;AAAA,IACf,OAAO,CAAC;AAAA,IACR,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,EACV;AACF;AACA,SAAS,sBAAsB,MAAM;AACnC,QAAM,WAAW,OAAO,KAAK,CAAC,MAAM,aAAa,OAAO,KAAK,MAAM,CAAC;AACpE,SAAO,gBAAc,SAAS,OAAO,CAAC,OAAO,YAAY,QAAQ,KAAK,GAAG,UAAU;AACrF;AACA,SAAS,OAAO;AACd,SAAO;AACT;AACA,SAAS,yBAAyB,OAAO,eAAe;AACtD,MAAI,OAAO,cAAc,eAAe,CAAC,WAAW;AAClD;AAAA,EACF;AACA,QAAM,eAAe,iDAChB,MAAM,eACN,MAAM,QACN,MAAM;AAEX,QAAM,iBAAiB,QAAQ,QAAQ,YAAY,EAAE,OAAO,eAAa,cAAc,SAAS,SAAS,CAAC;AAC1G,MAAI,eAAe,SAAS,GAAG;AAC7B,YAAQ,KAAK,4DAA4D,uBAAuB,eAAe,IAAI,SAAO,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,EACnJ;AACF;AACA,SAAS,UAAU,cAAc;AAC/B,SAAO,WAAS;AACd,UAAM,QAAQ,aAAa;AAAA,MACzB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM,QACV;AACD,6BAAyB,OAAO,QAAQ,QAAQ,KAAK,CAAC;AACtD,WAAO,iCACF,QADE;AAAA,MAEL,OAAO,kCACF,MAAM,QACN;AAAA,IAEP;AAAA,EACF;AACF;AACA,SAAS,aAAa,iBAAiB;AACrC,SAAO,UAAU,WAAS;AACxB,UAAM,iBAAiB,gBAAgB,KAAK;AAC5C,UAAM,qBAAqB,QAAQ,QAAQ,cAAc;AACzD,WAAO,mBAAmB,OAAO,CAAC,MAAM,QAAQ;AAC9C,YAAM,sBAAsB,eAAe,GAAG;AAC9C,aAAO,iCACF,OADE;AAAA,QAEL,CAAC,GAAG,GAAG,SAAS,mBAAmB,IAAI,sBAAsB,SAAS,mBAAmB;AAAA,MAC3F;AAAA,IACF,GAAG,CAAC,CAAC;AAAA,EACP,CAAC;AACH;AAwBA,SAAS,YAAY,gBAAgB;AACnC,SAAO,WAAS;AACd,UAAM,kBAAkB;AAAA,MACtB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,cAAc,IACpB,MAAM,OAAO,IACb,MAAM,SAAS;AAEpB,WAAO,eAAe,eAAe,EAAE,KAAK;AAAA,EAC9C;AACF;AACA,SAAS,UAAU,gBAAgB;AACjC,SAAO,WAAS;AACd,UAAM,eAAe;AAAA,MACnB,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM;AAEX,UAAM,QAAQ,OAAO,mBAAmB,aAAa,eAAe,YAAY,IAAI;AACpF,UAAM,aAAa,UAAQ;AACzB,YAAM,OAAO,MAAM,IAAI;AACvB,YAAM,cAAc,MAAM,MAAM,IAAI;AACpC,aAAO,OAAO,MAAM;AAClB,YAAI,aAAa;AACf,sBAAY;AAAA,QACd;AACA,aAAK,YAAY;AAAA,MACnB,IAAI;AAAA,IACN;AACA,WAAO,iCACF,QADE;AAAA,MAEL,OAAO;AAAA,QACL,QAAQ,WAAW,QAAQ;AAAA,QAC3B,WAAW,WAAW,WAAW;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AA0CA,SAAS,gBAAgB,oBAAoB;AAC3C,SAAO,WAAS;AACd,UAAM,cAAc,mBAAmB,kCAClC,MAAM,eACN,MAAM,MACV;AACD,UAAM,YAAY,QAAQ,QAAQ,WAAW;AAC7C,UAAM,cAAc,MAAM,YAAY;AACtC,UAAM,eAAe,CAAC;AACtB,eAAW,OAAO,WAAW;AAC3B,YAAM,wBAAwB,YAAY,GAAG;AAC7C,kBAAY,GAAG,IAAI,iBAAiB,qBAAqB,IAAI,wBAAwB,aAAa,qBAAqB;AACvH,mBAAa,GAAG,IAAI,aAAa,YAAY,GAAG,CAAC;AAAA,IACnD;AACA,WAAO,iCACF,QADE;AAAA,MAEL,cAAc,kCACT,MAAM,eACN;AAAA,IAEP;AAAA,EACF;AACF;AACA,SAAS,YAAY,gBAAgB;AACnC,SAAO,WAAS;AACd,UAAM,UAAU,eAAe;AAAA,MAC7B,CAAC,YAAY,GAAG,MAAM,YAAY;AAAA,OAC/B,MAAM,eACN,MAAM,QACN,MAAM,QACV;AACD,6BAAyB,OAAO,QAAQ,QAAQ,OAAO,CAAC;AACxD,WAAO,iCACF,QADE;AAAA,MAEL,SAAS,kCACJ,MAAM,UACN;AAAA,IAEP;AAAA,EACF;AACF;AACA,SAAS,UAAU,gBAAgB;AACjC,SAAO,WAAS;AACd,UAAM,QAAQ,OAAO,mBAAmB,aAAa,eAAe,IAAI;AACxE,UAAM,YAAY,QAAQ,QAAQ,KAAK;AACvC,6BAAyB,OAAO,SAAS;AACzC,UAAM,cAAc,MAAM,YAAY;AACtC,UAAM,eAAe,CAAC;AACtB,eAAW,OAAO,WAAW;AAC3B,kBAAY,GAAG,IAAI,OAAO,MAAM,GAAG,CAAC;AACpC,mBAAa,GAAG,IAAI,aAAa,YAAY,GAAG,CAAC;AAAA,IACnD;AACA,WAAO,iCACF,QADE;AAAA,MAEL,cAAc,kCACT,MAAM,eACN;AAAA,IAEP;AAAA,EACF;AACF;", "names": ["signal", "config", "signalState"]}