{"version": 3, "sources": ["../../../../../../node_modules/ngx-infinite-scroll/fesm2022/ngx-infinite-scroll.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Input, Output, Directive, NgModule } from '@angular/core';\nimport { of, fromEvent } from 'rxjs';\nimport { mergeMap, map, tap, filter, throttleTime } from 'rxjs/operators';\nfunction resolveContainerElement(selector, scrollWindow, defaultElement, fromRoot) {\n  const hasWindow = window && !!window.document && window.document.documentElement;\n  let container = hasWindow && scrollWindow ? window : defaultElement;\n  if (selector) {\n    const containerIsString = selector && hasWindow && typeof selector === 'string';\n    container = containerIsString ? findElement(selector, defaultElement.nativeElement, fromRoot) : selector;\n    if (!container) {\n      throw new Error('ngx-infinite-scroll {resolveContainerElement()}: selector for');\n    }\n  }\n  return container;\n}\nfunction findElement(selector, customRoot, fromRoot) {\n  const rootEl = fromRoot ? window.document : customRoot;\n  return rootEl.querySelector(selector);\n}\nfunction inputPropChanged(prop) {\n  return prop && !prop.firstChange;\n}\nfunction hasWindowDefined() {\n  return typeof window !== 'undefined';\n}\nconst VerticalProps = {\n  clientHeight: \"clientHeight\",\n  offsetHeight: \"offsetHeight\",\n  scrollHeight: \"scrollHeight\",\n  pageYOffset: \"pageYOffset\",\n  offsetTop: \"offsetTop\",\n  scrollTop: \"scrollTop\",\n  top: \"top\"\n};\nconst HorizontalProps = {\n  clientHeight: \"clientWidth\",\n  offsetHeight: \"offsetWidth\",\n  scrollHeight: \"scrollWidth\",\n  pageYOffset: \"pageXOffset\",\n  offsetTop: \"offsetLeft\",\n  scrollTop: \"scrollLeft\",\n  top: \"left\"\n};\nclass AxisResolver {\n  constructor(vertical = true) {\n    this.vertical = vertical;\n    this.propsMap = vertical ? VerticalProps : HorizontalProps;\n  }\n  clientHeightKey() {\n    return this.propsMap.clientHeight;\n  }\n  offsetHeightKey() {\n    return this.propsMap.offsetHeight;\n  }\n  scrollHeightKey() {\n    return this.propsMap.scrollHeight;\n  }\n  pageYOffsetKey() {\n    return this.propsMap.pageYOffset;\n  }\n  offsetTopKey() {\n    return this.propsMap.offsetTop;\n  }\n  scrollTopKey() {\n    return this.propsMap.scrollTop;\n  }\n  topKey() {\n    return this.propsMap.top;\n  }\n}\nfunction shouldTriggerEvents(alwaysCallback, shouldFireScrollEvent, isTriggeredCurrentTotal) {\n  if (alwaysCallback && shouldFireScrollEvent) {\n    return true;\n  }\n  if (!isTriggeredCurrentTotal && shouldFireScrollEvent) {\n    return true;\n  }\n  return false;\n}\nfunction createResolver({\n  windowElement,\n  axis\n}) {\n  return createResolverWithContainer({\n    axis,\n    isWindow: isElementWindow(windowElement)\n  }, windowElement);\n}\nfunction createResolverWithContainer(resolver, windowElement) {\n  const container = resolver.isWindow || windowElement && !windowElement.nativeElement ? windowElement : windowElement.nativeElement;\n  return {\n    ...resolver,\n    container\n  };\n}\nfunction isElementWindow(windowElement) {\n  const isWindow = ['Window', 'global'].some(obj => Object.prototype.toString.call(windowElement).includes(obj));\n  return isWindow;\n}\nfunction getDocumentElement(isContainerWindow, windowElement) {\n  return isContainerWindow ? windowElement.document.documentElement : null;\n}\nfunction calculatePoints(element, resolver) {\n  const height = extractHeightForElement(resolver);\n  return resolver.isWindow ? calculatePointsForWindow(height, element, resolver) : calculatePointsForElement(height, element, resolver);\n}\nfunction calculatePointsForWindow(height, element, resolver) {\n  const {\n    axis,\n    container,\n    isWindow\n  } = resolver;\n  const {\n    offsetHeightKey,\n    clientHeightKey\n  } = extractHeightPropKeys(axis);\n  // scrolled until now / current y point\n  const scrolled = height + getElementPageYOffset(getDocumentElement(isWindow, container), axis, isWindow);\n  // total height / most bottom y point\n  const nativeElementHeight = getElementHeight(element.nativeElement, isWindow, offsetHeightKey, clientHeightKey);\n  const totalToScroll = getElementOffsetTop(element.nativeElement, axis, isWindow) + nativeElementHeight;\n  return {\n    height,\n    scrolled,\n    totalToScroll,\n    isWindow\n  };\n}\nfunction calculatePointsForElement(height, element, resolver) {\n  const {\n    axis,\n    container\n  } = resolver;\n  // perhaps use container.offsetTop instead of 'scrollTop'\n  const scrolled = container[axis.scrollTopKey()];\n  const totalToScroll = container[axis.scrollHeightKey()];\n  return {\n    height,\n    scrolled,\n    totalToScroll,\n    isWindow: false\n  };\n}\nfunction extractHeightPropKeys(axis) {\n  return {\n    offsetHeightKey: axis.offsetHeightKey(),\n    clientHeightKey: axis.clientHeightKey()\n  };\n}\nfunction extractHeightForElement({\n  container,\n  isWindow,\n  axis\n}) {\n  const {\n    offsetHeightKey,\n    clientHeightKey\n  } = extractHeightPropKeys(axis);\n  return getElementHeight(container, isWindow, offsetHeightKey, clientHeightKey);\n}\nfunction getElementHeight(elem, isWindow, offsetHeightKey, clientHeightKey) {\n  if (isNaN(elem[offsetHeightKey])) {\n    const docElem = getDocumentElement(isWindow, elem);\n    return docElem ? docElem[clientHeightKey] : 0;\n  } else {\n    return elem[offsetHeightKey];\n  }\n}\nfunction getElementOffsetTop(elem, axis, isWindow) {\n  const topKey = axis.topKey();\n  // elem = elem.nativeElement;\n  if (!elem.getBoundingClientRect) {\n    // || elem.css('none')) {\n    return;\n  }\n  return elem.getBoundingClientRect()[topKey] + getElementPageYOffset(elem, axis, isWindow);\n}\nfunction getElementPageYOffset(elem, axis, isWindow) {\n  const pageYOffset = axis.pageYOffsetKey();\n  const scrollTop = axis.scrollTopKey();\n  const offsetTop = axis.offsetTopKey();\n  if (isNaN(window.pageYOffset)) {\n    return getDocumentElement(isWindow, elem)[scrollTop];\n  } else if (elem.ownerDocument) {\n    return elem.ownerDocument.defaultView[pageYOffset];\n  } else {\n    return elem[offsetTop];\n  }\n}\nfunction shouldFireScrollEvent(container, distance = {\n  down: 0,\n  up: 0\n}, scrollingDown) {\n  let remaining;\n  let containerBreakpoint;\n  if (container.totalToScroll <= 0) {\n    return false;\n  }\n  const scrolledUntilNow = container.isWindow ? container.scrolled : container.height + container.scrolled;\n  if (scrollingDown) {\n    remaining = (container.totalToScroll - scrolledUntilNow) / container.totalToScroll;\n    const distanceDown = distance?.down ? distance.down : 0;\n    containerBreakpoint = distanceDown / 10;\n  } else {\n    const totalHiddenContentHeight = container.scrolled + (container.totalToScroll - scrolledUntilNow);\n    remaining = container.scrolled / totalHiddenContentHeight;\n    const distanceUp = distance?.up ? distance.up : 0;\n    containerBreakpoint = distanceUp / 10;\n  }\n  const shouldFireEvent = remaining <= containerBreakpoint;\n  return shouldFireEvent;\n}\nfunction isScrollingDownwards(lastScrollPosition, container) {\n  return lastScrollPosition < container.scrolled;\n}\nfunction getScrollStats(lastScrollPosition, container, distance) {\n  const scrollDown = isScrollingDownwards(lastScrollPosition, container);\n  return {\n    fire: shouldFireScrollEvent(container, distance, scrollDown),\n    scrollDown\n  };\n}\nfunction updateScrollPosition(position, scrollState) {\n  return scrollState.lastScrollPosition = position;\n}\nfunction updateTotalToScroll(totalToScroll, scrollState) {\n  if (scrollState.lastTotalToScroll !== totalToScroll) {\n    scrollState.lastTotalToScroll = scrollState.totalToScroll;\n    scrollState.totalToScroll = totalToScroll;\n  }\n}\nfunction isSameTotalToScroll(scrollState) {\n  return scrollState.totalToScroll === scrollState.lastTotalToScroll;\n}\nfunction updateTriggeredFlag(scroll, scrollState, triggered, isScrollingDown) {\n  if (isScrollingDown) {\n    scrollState.triggered.down = scroll;\n  } else {\n    scrollState.triggered.up = scroll;\n  }\n}\nfunction isTriggeredScroll(totalToScroll, scrollState, isScrollingDown) {\n  return isScrollingDown ? scrollState.triggered.down === totalToScroll : scrollState.triggered.up === totalToScroll;\n}\nfunction updateScrollState(scrollState, scrolledUntilNow, totalToScroll) {\n  updateScrollPosition(scrolledUntilNow, scrollState);\n  updateTotalToScroll(totalToScroll, scrollState);\n  // const isSameTotal = isSameTotalToScroll(scrollState);\n  // if (!isSameTotal) {\n  //   updateTriggeredFlag(scrollState, false, isScrollingDown);\n  // }\n}\nclass ScrollState {\n  constructor(attrs) {\n    this.lastScrollPosition = 0;\n    this.lastTotalToScroll = 0;\n    this.totalToScroll = 0;\n    this.triggered = {\n      down: 0,\n      up: 0\n    };\n    Object.assign(this, attrs);\n  }\n  updateScrollPosition(position) {\n    return this.lastScrollPosition = position;\n  }\n  updateTotalToScroll(totalToScroll) {\n    if (this.lastTotalToScroll !== totalToScroll) {\n      this.lastTotalToScroll = this.totalToScroll;\n      this.totalToScroll = totalToScroll;\n    }\n  }\n  updateScroll(scrolledUntilNow, totalToScroll) {\n    this.updateScrollPosition(scrolledUntilNow);\n    this.updateTotalToScroll(totalToScroll);\n  }\n  updateTriggeredFlag(scroll, isScrollingDown) {\n    if (isScrollingDown) {\n      this.triggered.down = scroll;\n    } else {\n      this.triggered.up = scroll;\n    }\n  }\n  isTriggeredScroll(totalToScroll, isScrollingDown) {\n    return isScrollingDown ? this.triggered.down === totalToScroll : this.triggered.up === totalToScroll;\n  }\n}\nfunction createScroller(config) {\n  const {\n    scrollContainer,\n    scrollWindow,\n    element,\n    fromRoot\n  } = config;\n  const resolver = createResolver({\n    axis: new AxisResolver(!config.horizontal),\n    windowElement: resolveContainerElement(scrollContainer, scrollWindow, element, fromRoot)\n  });\n  const scrollState = new ScrollState({\n    totalToScroll: calculatePoints(element, resolver).totalToScroll\n  });\n  const options = {\n    container: resolver.container,\n    throttle: config.throttle\n  };\n  const distance = {\n    up: config.upDistance,\n    down: config.downDistance\n  };\n  return attachScrollEvent(options).pipe(mergeMap(() => of(calculatePoints(element, resolver))), map(positionStats => toInfiniteScrollParams(scrollState.lastScrollPosition, positionStats, distance)), tap(({\n    stats\n  }) => scrollState.updateScroll(stats.scrolled, stats.totalToScroll)), filter(({\n    fire,\n    scrollDown,\n    stats: {\n      totalToScroll\n    }\n  }) => shouldTriggerEvents(config.alwaysCallback, fire, scrollState.isTriggeredScroll(totalToScroll, scrollDown))), tap(({\n    scrollDown,\n    stats: {\n      totalToScroll\n    }\n  }) => {\n    scrollState.updateTriggeredFlag(totalToScroll, scrollDown);\n  }), map(toInfiniteScrollAction));\n}\nfunction attachScrollEvent(options) {\n  let obs = fromEvent(options.container, 'scroll');\n  // For an unknown reason calling `sampleTime()` causes trouble for many users, even with `options.throttle = 0`.\n  // Let's avoid calling the function unless needed.\n  // Replacing with throttleTime seems to solve the problem\n  // See https://github.com/orizens/ngx-infinite-scroll/issues/198\n  if (options.throttle) {\n    obs = obs.pipe(throttleTime(options.throttle, undefined, {\n      leading: true,\n      trailing: true\n    }));\n  }\n  return obs;\n}\nfunction toInfiniteScrollParams(lastScrollPosition, stats, distance) {\n  const {\n    scrollDown,\n    fire\n  } = getScrollStats(lastScrollPosition, stats, distance);\n  return {\n    scrollDown,\n    fire,\n    stats\n  };\n}\nconst InfiniteScrollActions = {\n  DOWN: '[NGX_ISE] DOWN',\n  UP: '[NGX_ISE] UP'\n};\nfunction toInfiniteScrollAction(response) {\n  const {\n    scrollDown,\n    stats: {\n      scrolled: currentScrollPosition\n    }\n  } = response;\n  return {\n    type: scrollDown ? InfiniteScrollActions.DOWN : InfiniteScrollActions.UP,\n    payload: {\n      currentScrollPosition\n    }\n  };\n}\nclass InfiniteScrollDirective {\n  constructor(element, zone) {\n    this.element = element;\n    this.zone = zone;\n    this.scrolled = new EventEmitter();\n    this.scrolledUp = new EventEmitter();\n    this.infiniteScrollDistance = 2;\n    this.infiniteScrollUpDistance = 1.5;\n    this.infiniteScrollThrottle = 150;\n    this.infiniteScrollDisabled = false;\n    this.infiniteScrollContainer = null;\n    this.scrollWindow = true;\n    this.immediateCheck = false;\n    this.horizontal = false;\n    this.alwaysCallback = false;\n    this.fromRoot = false;\n  }\n  ngAfterViewInit() {\n    if (!this.infiniteScrollDisabled) {\n      this.setup();\n    }\n  }\n  ngOnChanges({\n    infiniteScrollContainer,\n    infiniteScrollDisabled,\n    infiniteScrollDistance\n  }) {\n    const containerChanged = inputPropChanged(infiniteScrollContainer);\n    const disabledChanged = inputPropChanged(infiniteScrollDisabled);\n    const distanceChanged = inputPropChanged(infiniteScrollDistance);\n    const shouldSetup = !disabledChanged && !this.infiniteScrollDisabled || disabledChanged && !infiniteScrollDisabled.currentValue || distanceChanged;\n    if (containerChanged || disabledChanged || distanceChanged) {\n      this.destroyScroller();\n      if (shouldSetup) {\n        this.setup();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroyScroller();\n  }\n  setup() {\n    if (!hasWindowDefined()) {\n      return;\n    }\n    this.zone.runOutsideAngular(() => {\n      this.disposeScroller = createScroller({\n        fromRoot: this.fromRoot,\n        alwaysCallback: this.alwaysCallback,\n        disable: this.infiniteScrollDisabled,\n        downDistance: this.infiniteScrollDistance,\n        element: this.element,\n        horizontal: this.horizontal,\n        scrollContainer: this.infiniteScrollContainer,\n        scrollWindow: this.scrollWindow,\n        throttle: this.infiniteScrollThrottle,\n        upDistance: this.infiniteScrollUpDistance\n      }).subscribe(payload => this.handleOnScroll(payload));\n    });\n  }\n  handleOnScroll({\n    type,\n    payload\n  }) {\n    const emitter = type === InfiniteScrollActions.DOWN ? this.scrolled : this.scrolledUp;\n    if (hasObservers(emitter)) {\n      this.zone.run(() => emitter.emit(payload));\n    }\n  }\n  destroyScroller() {\n    if (this.disposeScroller) {\n      this.disposeScroller.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function InfiniteScrollDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InfiniteScrollDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InfiniteScrollDirective,\n      selectors: [[\"\", \"infiniteScroll\", \"\"], [\"\", \"infinite-scroll\", \"\"], [\"\", \"data-infinite-scroll\", \"\"]],\n      inputs: {\n        infiniteScrollDistance: \"infiniteScrollDistance\",\n        infiniteScrollUpDistance: \"infiniteScrollUpDistance\",\n        infiniteScrollThrottle: \"infiniteScrollThrottle\",\n        infiniteScrollDisabled: \"infiniteScrollDisabled\",\n        infiniteScrollContainer: \"infiniteScrollContainer\",\n        scrollWindow: \"scrollWindow\",\n        immediateCheck: \"immediateCheck\",\n        horizontal: \"horizontal\",\n        alwaysCallback: \"alwaysCallback\",\n        fromRoot: \"fromRoot\"\n      },\n      outputs: {\n        scrolled: \"scrolled\",\n        scrolledUp: \"scrolledUp\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InfiniteScrollDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[infiniteScroll], [infinite-scroll], [data-infinite-scroll]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    scrolled: [{\n      type: Output\n    }],\n    scrolledUp: [{\n      type: Output\n    }],\n    infiniteScrollDistance: [{\n      type: Input\n    }],\n    infiniteScrollUpDistance: [{\n      type: Input\n    }],\n    infiniteScrollThrottle: [{\n      type: Input\n    }],\n    infiniteScrollDisabled: [{\n      type: Input\n    }],\n    infiniteScrollContainer: [{\n      type: Input\n    }],\n    scrollWindow: [{\n      type: Input\n    }],\n    immediateCheck: [{\n      type: Input\n    }],\n    horizontal: [{\n      type: Input\n    }],\n    alwaysCallback: [{\n      type: Input\n    }],\n    fromRoot: [{\n      type: Input\n    }]\n  });\n})();\nfunction hasObservers(emitter) {\n  // Note: The `observed` property is available only in RxJS@7.2.0, which means it's\n  // not available for users running the lower version.\n  return emitter.observed ?? emitter.observers.length > 0;\n}\n\n/**\n * @deprecated Import InfiniteScrollDirective instead\n */\nclass InfiniteScrollModule {\n  static {\n    this.ɵfac = function InfiniteScrollModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InfiniteScrollModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InfiniteScrollModule,\n      imports: [InfiniteScrollDirective],\n      exports: [InfiniteScrollDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InfiniteScrollModule, [{\n    type: NgModule,\n    args: [{\n      exports: [InfiniteScrollDirective],\n      imports: [InfiniteScrollDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-infinite-scroll\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InfiniteScrollDirective, InfiniteScrollModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAS,wBAAwB,UAAU,cAAc,gBAAgB,UAAU;AACjF,QAAM,YAAY,UAAU,CAAC,CAAC,OAAO,YAAY,OAAO,SAAS;AACjE,MAAI,YAAY,aAAa,eAAe,SAAS;AACrD,MAAI,UAAU;AACZ,UAAM,oBAAoB,YAAY,aAAa,OAAO,aAAa;AACvE,gBAAY,oBAAoB,YAAY,UAAU,eAAe,eAAe,QAAQ,IAAI;AAChG,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,+DAA+D;AAAA,IACjF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,UAAU,YAAY,UAAU;AACnD,QAAM,SAAS,WAAW,OAAO,WAAW;AAC5C,SAAO,OAAO,cAAc,QAAQ;AACtC;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,QAAQ,CAAC,KAAK;AACvB;AACA,SAAS,mBAAmB;AAC1B,SAAO,OAAO,WAAW;AAC3B;AACA,IAAM,gBAAgB;AAAA,EACpB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,KAAK;AACP;AACA,IAAM,kBAAkB;AAAA,EACtB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,KAAK;AACP;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,WAAW,MAAM;AAC3B,SAAK,WAAW;AAChB,SAAK,WAAW,WAAW,gBAAgB;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,eAAe;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,eAAe;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,SAAS;AACP,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AACA,SAAS,oBAAoB,gBAAgBA,wBAAuB,yBAAyB;AAC3F,MAAI,kBAAkBA,wBAAuB;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,CAAC,2BAA2BA,wBAAuB;AACrD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AACF,GAAG;AACD,SAAO,4BAA4B;AAAA,IACjC;AAAA,IACA,UAAU,gBAAgB,aAAa;AAAA,EACzC,GAAG,aAAa;AAClB;AACA,SAAS,4BAA4B,UAAU,eAAe;AAC5D,QAAM,YAAY,SAAS,YAAY,iBAAiB,CAAC,cAAc,gBAAgB,gBAAgB,cAAc;AACrH,SAAO,iCACF,WADE;AAAA,IAEL;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,eAAe;AACtC,QAAM,WAAW,CAAC,UAAU,QAAQ,EAAE,KAAK,SAAO,OAAO,UAAU,SAAS,KAAK,aAAa,EAAE,SAAS,GAAG,CAAC;AAC7G,SAAO;AACT;AACA,SAAS,mBAAmB,mBAAmB,eAAe;AAC5D,SAAO,oBAAoB,cAAc,SAAS,kBAAkB;AACtE;AACA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,SAAS,wBAAwB,QAAQ;AAC/C,SAAO,SAAS,WAAW,yBAAyB,QAAQ,SAAS,QAAQ,IAAI,0BAA0B,QAAQ,SAAS,QAAQ;AACtI;AACA,SAAS,yBAAyB,QAAQ,SAAS,UAAU;AAC3D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,IAAI;AAE9B,QAAM,WAAW,SAAS,sBAAsB,mBAAmB,UAAU,SAAS,GAAG,MAAM,QAAQ;AAEvG,QAAM,sBAAsB,iBAAiB,QAAQ,eAAe,UAAU,iBAAiB,eAAe;AAC9G,QAAM,gBAAgB,oBAAoB,QAAQ,eAAe,MAAM,QAAQ,IAAI;AACnF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,QAAQ,SAAS,UAAU;AAC5D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,WAAW,UAAU,KAAK,aAAa,CAAC;AAC9C,QAAM,gBAAgB,UAAU,KAAK,gBAAgB,CAAC;AACtD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ;AACF;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO;AAAA,IACL,iBAAiB,KAAK,gBAAgB;AAAA,IACtC,iBAAiB,KAAK,gBAAgB;AAAA,EACxC;AACF;AACA,SAAS,wBAAwB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,IAAI;AAC9B,SAAO,iBAAiB,WAAW,UAAU,iBAAiB,eAAe;AAC/E;AACA,SAAS,iBAAiB,MAAM,UAAU,iBAAiB,iBAAiB;AAC1E,MAAI,MAAM,KAAK,eAAe,CAAC,GAAG;AAChC,UAAM,UAAU,mBAAmB,UAAU,IAAI;AACjD,WAAO,UAAU,QAAQ,eAAe,IAAI;AAAA,EAC9C,OAAO;AACL,WAAO,KAAK,eAAe;AAAA,EAC7B;AACF;AACA,SAAS,oBAAoB,MAAM,MAAM,UAAU;AACjD,QAAM,SAAS,KAAK,OAAO;AAE3B,MAAI,CAAC,KAAK,uBAAuB;AAE/B;AAAA,EACF;AACA,SAAO,KAAK,sBAAsB,EAAE,MAAM,IAAI,sBAAsB,MAAM,MAAM,QAAQ;AAC1F;AACA,SAAS,sBAAsB,MAAM,MAAM,UAAU;AACnD,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,YAAY,KAAK,aAAa;AACpC,MAAI,MAAM,OAAO,WAAW,GAAG;AAC7B,WAAO,mBAAmB,UAAU,IAAI,EAAE,SAAS;AAAA,EACrD,WAAW,KAAK,eAAe;AAC7B,WAAO,KAAK,cAAc,YAAY,WAAW;AAAA,EACnD,OAAO;AACL,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AACA,SAAS,sBAAsB,WAAW,WAAW;AAAA,EACnD,MAAM;AAAA,EACN,IAAI;AACN,GAAG,eAAe;AAChB,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,iBAAiB,GAAG;AAChC,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,UAAU,WAAW,UAAU,WAAW,UAAU,SAAS,UAAU;AAChG,MAAI,eAAe;AACjB,iBAAa,UAAU,gBAAgB,oBAAoB,UAAU;AACrE,UAAM,eAAe,UAAU,OAAO,SAAS,OAAO;AACtD,0BAAsB,eAAe;AAAA,EACvC,OAAO;AACL,UAAM,2BAA2B,UAAU,YAAY,UAAU,gBAAgB;AACjF,gBAAY,UAAU,WAAW;AACjC,UAAM,aAAa,UAAU,KAAK,SAAS,KAAK;AAChD,0BAAsB,aAAa;AAAA,EACrC;AACA,QAAM,kBAAkB,aAAa;AACrC,SAAO;AACT;AACA,SAAS,qBAAqB,oBAAoB,WAAW;AAC3D,SAAO,qBAAqB,UAAU;AACxC;AACA,SAAS,eAAe,oBAAoB,WAAW,UAAU;AAC/D,QAAM,aAAa,qBAAqB,oBAAoB,SAAS;AACrE,SAAO;AAAA,IACL,MAAM,sBAAsB,WAAW,UAAU,UAAU;AAAA,IAC3D;AAAA,EACF;AACF;AA+BA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,OAAO;AACjB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,YAAY;AAAA,MACf,MAAM;AAAA,MACN,IAAI;AAAA,IACN;AACA,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAAA,EACA,qBAAqB,UAAU;AAC7B,WAAO,KAAK,qBAAqB;AAAA,EACnC;AAAA,EACA,oBAAoB,eAAe;AACjC,QAAI,KAAK,sBAAsB,eAAe;AAC5C,WAAK,oBAAoB,KAAK;AAC9B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa,kBAAkB,eAAe;AAC5C,SAAK,qBAAqB,gBAAgB;AAC1C,SAAK,oBAAoB,aAAa;AAAA,EACxC;AAAA,EACA,oBAAoB,QAAQ,iBAAiB;AAC3C,QAAI,iBAAiB;AACnB,WAAK,UAAU,OAAO;AAAA,IACxB,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,IACtB;AAAA,EACF;AAAA,EACA,kBAAkB,eAAe,iBAAiB;AAChD,WAAO,kBAAkB,KAAK,UAAU,SAAS,gBAAgB,KAAK,UAAU,OAAO;AAAA,EACzF;AACF;AACA,SAAS,eAAe,QAAQ;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,eAAe;AAAA,IAC9B,MAAM,IAAI,aAAa,CAAC,OAAO,UAAU;AAAA,IACzC,eAAe,wBAAwB,iBAAiB,cAAc,SAAS,QAAQ;AAAA,EACzF,CAAC;AACD,QAAM,cAAc,IAAI,YAAY;AAAA,IAClC,eAAe,gBAAgB,SAAS,QAAQ,EAAE;AAAA,EACpD,CAAC;AACD,QAAM,UAAU;AAAA,IACd,WAAW,SAAS;AAAA,IACpB,UAAU,OAAO;AAAA,EACnB;AACA,QAAM,WAAW;AAAA,IACf,IAAI,OAAO;AAAA,IACX,MAAM,OAAO;AAAA,EACf;AACA,SAAO,kBAAkB,OAAO,EAAE,KAAK,SAAS,MAAM,GAAG,gBAAgB,SAAS,QAAQ,CAAC,CAAC,GAAG,IAAI,mBAAiB,uBAAuB,YAAY,oBAAoB,eAAe,QAAQ,CAAC,GAAG,IAAI,CAAC;AAAA,IACzM;AAAA,EACF,MAAM,YAAY,aAAa,MAAM,UAAU,MAAM,aAAa,CAAC,GAAG,OAAO,CAAC;AAAA,IAC5E;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF,MAAM,oBAAoB,OAAO,gBAAgB,MAAM,YAAY,kBAAkB,eAAe,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC;AAAA,IACtH;AAAA,IACA,OAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF,MAAM;AACJ,gBAAY,oBAAoB,eAAe,UAAU;AAAA,EAC3D,CAAC,GAAG,IAAI,sBAAsB,CAAC;AACjC;AACA,SAAS,kBAAkB,SAAS;AAClC,MAAI,MAAM,UAAU,QAAQ,WAAW,QAAQ;AAK/C,MAAI,QAAQ,UAAU;AACpB,UAAM,IAAI,KAAK,aAAa,QAAQ,UAAU,QAAW;AAAA,MACvD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,oBAAoB,OAAO,UAAU;AACnE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe,oBAAoB,OAAO,QAAQ;AACtD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,MAAM;AAAA,EACN,IAAI;AACN;AACA,SAAS,uBAAuB,UAAU;AACxC,QAAM;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,MAAM,aAAa,sBAAsB,OAAO,sBAAsB;AAAA,IACtE,SAAS;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,SAAS,MAAM;AACzB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,yBAAyB;AAC9B,SAAK,2BAA2B;AAChC,SAAK,yBAAyB;AAC9B,SAAK,yBAAyB;AAC9B,SAAK,0BAA0B;AAC/B,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,mBAAmB,iBAAiB,uBAAuB;AACjE,UAAM,kBAAkB,iBAAiB,sBAAsB;AAC/D,UAAM,kBAAkB,iBAAiB,sBAAsB;AAC/D,UAAM,cAAc,CAAC,mBAAmB,CAAC,KAAK,0BAA0B,mBAAmB,CAAC,uBAAuB,gBAAgB;AACnI,QAAI,oBAAoB,mBAAmB,iBAAiB;AAC1D,WAAK,gBAAgB;AACrB,UAAI,aAAa;AACf,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,iBAAiB,GAAG;AACvB;AAAA,IACF;AACA,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,kBAAkB,eAAe;AAAA,QACpC,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,QACrB,SAAS,KAAK;AAAA,QACd,cAAc,KAAK;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,YAAY,KAAK;AAAA,QACjB,iBAAiB,KAAK;AAAA,QACtB,cAAc,KAAK;AAAA,QACnB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB,CAAC,EAAE,UAAU,aAAW,KAAK,eAAe,OAAO,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,SAAS,sBAAsB,OAAO,KAAK,WAAW,KAAK;AAC3E,QAAI,aAAa,OAAO,GAAG;AACzB,WAAK,KAAK,IAAI,MAAM,QAAQ,KAAK,OAAO,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,YAAY;AAAA,IACnC;AAAA,EACF;AA6BF;AA3BI,yBAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAChI;AAGA,yBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,wBAAwB,EAAE,CAAC;AAAA,EACrG,QAAQ;AAAA,IACN,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yBAAyB;AAAA,IACzB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AApGL,IAAM,0BAAN;AAAA,CAuGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,aAAa,SAAS;AAG7B,SAAO,QAAQ,YAAY,QAAQ,UAAU,SAAS;AACxD;AAKA,IAAM,wBAAN,MAAM,sBAAqB;AAgB3B;AAdI,sBAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AAGA,sBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,uBAAuB;AAAA,EACjC,SAAS,CAAC,uBAAuB;AACnC,CAAC;AAGD,sBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,uBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,uBAAuB;AAAA,MACjC,SAAS,CAAC,uBAAuB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["shouldFireScrollEvent"]}