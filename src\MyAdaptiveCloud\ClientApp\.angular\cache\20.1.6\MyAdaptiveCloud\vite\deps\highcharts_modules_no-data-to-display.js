import {
  __commonJS
} from "./chunk-N6ESDQJH.js";

// node_modules/highcharts/modules/no-data-to-display.js
var require_no_data_to_display = __commonJS({
  "node_modules/highcharts/modules/no-data-to-display.js"(exports, module) {
    !/**
    * Highcharts JS v11.4.7 (2024-08-14)
    *
    * Plugin for displaying a message when there is no data visible in chart.
    *
    * (c) 2010-2024 Highsoft AS
    * Author: Oystein <PERSON>g
    *
    * License: www.highcharts.com/license
    */
    function(t) {
      "object" == typeof module && module.exports ? (t.default = t, module.exports = t) : "function" == typeof define && define.amd ? define("highcharts/modules/no-data-to-display", ["highcharts"], function(a) {
        return t(a), t.Highcharts = a, t;
      }) : t("undefined" != typeof Highcharts ? Highcharts : void 0);
    }(function(t) {
      "use strict";
      var a = t ? t._modules : {};
      function o(a2, o2, e, n) {
        a2.hasOwnProperty(o2) || (a2[o2] = n.apply(null, e), "function" == typeof CustomEvent && t.win.dispatchEvent(new CustomEvent("HighchartsModuleLoaded", { detail: { path: o2, module: a2[o2] } })));
      }
      o(a, "Extensions/NoDataToDisplay/NoDataDefaults.js", [], function() {
        return { lang: { noData: "No data to display" }, noData: { attr: { zIndex: 1 }, position: { x: 0, y: 0, align: "center", verticalAlign: "middle" }, style: { fontWeight: "bold", fontSize: "0.8em", color: "#666666" } } };
      }), o(a, "Extensions/NoDataToDisplay/NoDataToDisplay.js", [a["Core/Renderer/HTML/AST.js"], a["Extensions/NoDataToDisplay/NoDataDefaults.js"], a["Core/Utilities.js"]], function(t2, a2, o2) {
        let { addEvent: e, extend: n, merge: s } = o2;
        function i() {
          let t3 = this.series || [], a3 = t3.length;
          for (; a3--; ) if (t3[a3].hasData() && !t3[a3].options.isInternal) return true;
          return this.loadingShown;
        }
        function l() {
          this.noDataLabel && (this.noDataLabel = this.noDataLabel.destroy());
        }
        function r(a3) {
          let o3 = this.options, e2 = a3 || o3 && o3.lang.noData || "", s2 = o3 && (o3.noData || {});
          this.renderer && (this.noDataLabel || (this.noDataLabel = this.renderer.label(e2, 0, 0, void 0, void 0, void 0, s2.useHTML, void 0, "no-data").add()), this.styledMode || this.noDataLabel.attr(t2.filterUserAttributes(s2.attr || {})).css(s2.style || {}), this.noDataLabel.align(n(this.noDataLabel.getBBox(), s2.position || {}), false, "plotBox"));
        }
        function d() {
          this.hasData() ? this.hideNoData() : this.showNoData();
        }
        return { compose: function(t3, o3) {
          let n2 = t3.prototype;
          n2.showNoData || (n2.hasData = i, n2.hideNoData = l, n2.showNoData = r, e(t3, "render", d), s(true, o3, a2));
        } };
      }), o(a, "masters/modules/no-data-to-display.src.js", [a["Core/Globals.js"], a["Extensions/NoDataToDisplay/NoDataToDisplay.js"]], function(t2, a2) {
        return a2.compose(t2.Chart, t2.defaultOptions), t2;
      });
    });
  }
});
export default require_no_data_to_display();
//# sourceMappingURL=highcharts_modules_no-data-to-display.js.map
