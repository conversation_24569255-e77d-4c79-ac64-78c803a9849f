{"version": 3, "sources": ["../../../../../../node_modules/highcharts-angular/fesm2022/highcharts-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, NgModule } from '@angular/core';\nclass HighchartsChartComponent {\n  constructor(el, _zone // #75\n  ) {\n    this.el = el;\n    this._zone = _zone;\n    this.updateChange = new EventEmitter(true);\n    this.chartInstance = new EventEmitter(); // #26\n  }\n  ngOnChanges(changes) {\n    const update = changes.update?.currentValue;\n    if (changes.options || update) {\n      this.wrappedUpdateOrCreateChart();\n      if (update) {\n        this.updateChange.emit(false); // clear the flag after update\n      }\n    }\n  }\n  wrappedUpdateOrCreateChart() {\n    if (this.runOutsideAngular) {\n      this._zone.runOutsideAngular(() => {\n        this.updateOrCreateChart();\n      });\n    } else {\n      this.updateOrCreateChart();\n    }\n  }\n  updateOrCreateChart() {\n    if (this.chart?.update) {\n      this.chart.update(this.options, true, this.oneToOne || false);\n    } else {\n      this.chart = this.Highcharts[this.constructorType || 'chart'](this.el.nativeElement, this.options, this.callbackFunction || null);\n      // emit chart instance on init\n      this.chartInstance.emit(this.chart);\n    }\n  }\n  ngOnDestroy() {\n    if (this.chart) {\n      // #56\n      this.chart.destroy();\n      this.chart = null;\n      // emit chart instance on destroy\n      this.chartInstance.emit(this.chart);\n    }\n  }\n  static {\n    this.ɵfac = function HighchartsChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HighchartsChartComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: HighchartsChartComponent,\n      selectors: [[\"highcharts-chart\"]],\n      inputs: {\n        Highcharts: \"Highcharts\",\n        constructorType: \"constructorType\",\n        callbackFunction: \"callbackFunction\",\n        oneToOne: \"oneToOne\",\n        runOutsideAngular: \"runOutsideAngular\",\n        options: \"options\",\n        update: \"update\"\n      },\n      outputs: {\n        updateChange: \"updateChange\",\n        chartInstance: \"chartInstance\"\n      },\n      standalone: false,\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 0,\n      vars: 0,\n      template: function HighchartsChartComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighchartsChartComponent, [{\n    type: Component,\n    args: [{\n      selector: 'highcharts-chart',\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    Highcharts: [{\n      type: Input\n    }],\n    constructorType: [{\n      type: Input\n    }],\n    callbackFunction: [{\n      type: Input\n    }],\n    oneToOne: [{\n      type: Input\n    }],\n    runOutsideAngular: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    update: [{\n      type: Input\n    }],\n    updateChange: [{\n      type: Output\n    }],\n    chartInstance: [{\n      type: Output\n    }]\n  });\n})();\nclass HighchartsChartModule {\n  static {\n    this.ɵfac = function HighchartsChartModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HighchartsChartModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HighchartsChartModule,\n      declarations: [HighchartsChartComponent],\n      exports: [HighchartsChartComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighchartsChartModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [HighchartsChartComponent],\n      exports: [HighchartsChartComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of highcharts-angular\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HighchartsChartComponent, HighchartsChartModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,IAAI,OACd;AACA,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,eAAe,IAAI,aAAa,IAAI;AACzC,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,SAAS,QAAQ,QAAQ;AAC/B,QAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAK,2BAA2B;AAChC,UAAI,QAAQ;AACV,aAAK,aAAa,KAAK,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,mBAAmB;AAC1B,WAAK,MAAM,kBAAkB,MAAM;AACjC,aAAK,oBAAoB;AAAA,MAC3B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,OAAO,QAAQ;AACtB,WAAK,MAAM,OAAO,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK;AAAA,IAC9D,OAAO;AACL,WAAK,QAAQ,KAAK,WAAW,KAAK,mBAAmB,OAAO,EAAE,KAAK,GAAG,eAAe,KAAK,SAAS,KAAK,oBAAoB,IAAI;AAEhI,WAAK,cAAc,KAAK,KAAK,KAAK;AAAA,IACpC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,OAAO;AAEd,WAAK,MAAM,QAAQ;AACnB,WAAK,QAAQ;AAEb,WAAK,cAAc,KAAK,KAAK,KAAK;AAAA,IACpC;AAAA,EACF;AA+BF;AA7BI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AACjI;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,QAAQ;AAAA,IACN,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAAA,EAAC;AAAA,EAC/D,eAAe;AACjB,CAAC;AAxEL,IAAM,2BAAN;AAAA,CA2EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,uBAAsB;AAgB5B;AAdI,uBAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,wBAAuB;AAC1D;AAGA,uBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,cAAc,CAAC,wBAAwB;AAAA,EACvC,SAAS,CAAC,wBAAwB;AACpC,CAAC;AAGD,uBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,wBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,wBAAwB;AAAA,MACvC,SAAS,CAAC,wBAAwB;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}