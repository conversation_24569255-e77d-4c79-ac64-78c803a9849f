{"version": 3, "sources": ["../../../../../../node_modules/@ngrx/signals/fesm2022/ngrx-signals-rxjs-interop.mjs"], "sourcesContent": ["import { assertInInjectionContext, inject, Injector, DestroyRef, isSignal, effect, untracked } from '@angular/core';\nimport { Subject, noop, isObservable } from 'rxjs';\n\nfunction rxMethod(generator, config) {\n    if (!config?.injector) {\n        assertInInjectionContext(rxMethod);\n    }\n    const sourceInjector = config?.injector ?? inject(Injector);\n    const source$ = new Subject();\n    const sourceSub = generator(source$).subscribe();\n    sourceInjector.get(DestroyRef).onDestroy(() => sourceSub.unsubscribe());\n    const rxMethodFn = (input, config) => {\n        if (isStatic(input)) {\n            source$.next(input);\n            return { destroy: noop };\n        }\n        const callerInjector = getCallerInjector();\n        if (typeof ngDevMode !== 'undefined' &&\n            ngDevMode &&\n            config?.injector === undefined &&\n            callerInjector === undefined) {\n            console.warn('@ngrx/signals/rxjs-interop: The reactive method was called outside', 'the injection context with a signal or observable. This may lead to', 'a memory leak. Make sure to call it within the injection context', '(e.g. in a constructor or field initializer) or pass an injector', 'explicitly via the config parameter.\\n\\nFor more information, see:', 'https://ngrx.io/guide/signals/rxjs-integration#reactive-methods-and-injector-hierarchies');\n        }\n        const instanceInjector = config?.injector ?? callerInjector ?? sourceInjector;\n        if (isSignal(input)) {\n            const watcher = effect(() => {\n                const value = input();\n                untracked(() => source$.next(value));\n            }, { injector: instanceInjector });\n            sourceSub.add({ unsubscribe: () => watcher.destroy() });\n            return watcher;\n        }\n        const instanceSub = input.subscribe((value) => source$.next(value));\n        sourceSub.add(instanceSub);\n        if (instanceInjector !== sourceInjector) {\n            instanceInjector\n                .get(DestroyRef)\n                .onDestroy(() => instanceSub.unsubscribe());\n        }\n        return { destroy: () => instanceSub.unsubscribe() };\n    };\n    rxMethodFn.destroy = sourceSub.unsubscribe.bind(sourceSub);\n    return rxMethodFn;\n}\nfunction isStatic(value) {\n    return !isSignal(value) && !isObservable(value);\n}\nfunction getCallerInjector() {\n    try {\n        return inject(Injector);\n    }\n    catch {\n        return undefined;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { rxMethod };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,SAAS,WAAW,QAAQ;AACjC,MAAI,CAAC,QAAQ,UAAU;AACnB,6BAAyB,QAAQ;AAAA,EACrC;AACA,QAAM,iBAAiB,QAAQ,YAAY,OAAO,QAAQ;AAC1D,QAAM,UAAU,IAAI,QAAQ;AAC5B,QAAM,YAAY,UAAU,OAAO,EAAE,UAAU;AAC/C,iBAAe,IAAI,UAAU,EAAE,UAAU,MAAM,UAAU,YAAY,CAAC;AACtE,QAAM,aAAa,CAAC,OAAOA,YAAW;AAClC,QAAI,SAAS,KAAK,GAAG;AACjB,cAAQ,KAAK,KAAK;AAClB,aAAO,EAAE,SAAS,KAAK;AAAA,IAC3B;AACA,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,OAAO,cAAc,eACrB,aACAA,SAAQ,aAAa,UACrB,mBAAmB,QAAW;AAC9B,cAAQ,KAAK,sEAAsE,uEAAuE,oEAAoE,oEAAoE,sEAAsE,0FAA0F;AAAA,IACtc;AACA,UAAM,mBAAmBA,SAAQ,YAAY,kBAAkB;AAC/D,QAAI,SAAS,KAAK,GAAG;AACjB,YAAM,UAAU,OAAO,MAAM;AACzB,cAAM,QAAQ,MAAM;AACpB,kBAAU,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,MACvC,GAAG,EAAE,UAAU,iBAAiB,CAAC;AACjC,gBAAU,IAAI,EAAE,aAAa,MAAM,QAAQ,QAAQ,EAAE,CAAC;AACtD,aAAO;AAAA,IACX;AACA,UAAM,cAAc,MAAM,UAAU,CAAC,UAAU,QAAQ,KAAK,KAAK,CAAC;AAClE,cAAU,IAAI,WAAW;AACzB,QAAI,qBAAqB,gBAAgB;AACrC,uBACK,IAAI,UAAU,EACd,UAAU,MAAM,YAAY,YAAY,CAAC;AAAA,IAClD;AACA,WAAO,EAAE,SAAS,MAAM,YAAY,YAAY,EAAE;AAAA,EACtD;AACA,aAAW,UAAU,UAAU,YAAY,KAAK,SAAS;AACzD,SAAO;AACX;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,CAAC,SAAS,KAAK,KAAK,CAAC,aAAa,KAAK;AAClD;AACA,SAAS,oBAAoB;AACzB,MAAI;AACA,WAAO,OAAO,QAAQ;AAAA,EAC1B,QACM;AACF,WAAO;AAAA,EACX;AACJ;", "names": ["config"]}