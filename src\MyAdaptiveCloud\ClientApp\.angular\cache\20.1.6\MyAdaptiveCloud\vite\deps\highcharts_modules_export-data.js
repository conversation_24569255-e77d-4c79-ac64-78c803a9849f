import {
  __commonJS
} from "./chunk-N6ESDQJH.js";

// node_modules/highcharts/modules/export-data.js
var require_export_data = __commonJS({
  "node_modules/highcharts/modules/export-data.js"(exports, module) {
    !/**
    * Highcharts JS v11.4.7 (2024-08-14)
    *
    * Exporting module
    *
    * (c) 2010-2024 Torstein Honsi
    *
    * License: www.highcharts.com/license
    */
    function(t) {
      "object" == typeof module && module.exports ? (t.default = t, module.exports = t) : "function" == typeof define && define.amd ? define("highcharts/modules/export-data", ["highcharts", "highcharts/modules/exporting"], function(e) {
        return t(e), t.Highcharts = e, t;
      }) : t("undefined" != typeof Highcharts ? Highcharts : void 0);
    }(function(t) {
      "use strict";
      var e = t ? t._modules : {};
      function o(e2, o2, a, n) {
        e2.hasOwnProperty(o2) || (e2[o2] = n.apply(null, a), "function" == typeof CustomEvent && t.win.dispatchEvent(new CustomEvent("HighchartsModuleLoaded", { detail: { path: o2, module: e2[o2] } })));
      }
      o(e, "Extensions/DownloadURL.js", [e["Core/Globals.js"]], function(t2) {
        let { isSafari: e2, win: o2, win: { document: a } } = t2, n = o2.URL || o2.webkitURL || o2;
        function i(t3) {
          let e3 = t3.replace(/filename=.*;/, "").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);
          if (e3 && e3.length > 3 && o2.atob && o2.ArrayBuffer && o2.Uint8Array && o2.Blob && n.createObjectURL) {
            let t4 = o2.atob(e3[3]), a2 = new o2.ArrayBuffer(t4.length), i2 = new o2.Uint8Array(a2);
            for (let e4 = 0; e4 < i2.length; ++e4) i2[e4] = t4.charCodeAt(e4);
            return n.createObjectURL(new o2.Blob([i2], { type: e3[1] }));
          }
        }
        return { dataURLtoBlob: i, downloadURL: function(t3, n2) {
          let r = o2.navigator, s = a.createElement("a");
          if ("string" != typeof t3 && !(t3 instanceof String) && r.msSaveOrOpenBlob) {
            r.msSaveOrOpenBlob(t3, n2);
            return;
          }
          if (t3 = "" + t3, r.userAgent.length > 1e3) throw Error("Input too long");
          let l = /Edge\/\d+/.test(r.userAgent);
          if ((e2 && "string" == typeof t3 && 0 === t3.indexOf("data:application/pdf") || l || t3.length > 2e6) && !(t3 = i(t3) || "")) throw Error("Failed to convert to blob");
          if (void 0 !== s.download) s.href = t3, s.download = n2, a.body.appendChild(s), s.click(), a.body.removeChild(s);
          else try {
            if (!o2.open(t3, "chart")) throw Error("Failed to open window");
          } catch {
            o2.location.href = t3;
          }
        } };
      }), o(e, "Extensions/ExportData/ExportDataDefaults.js", [], function() {
        return { exporting: { csv: { annotations: { itemDelimiter: "; ", join: false }, columnHeaderFormatter: null, dateFormat: "%Y-%m-%d %H:%M:%S", decimalPoint: null, itemDelimiter: null, lineDelimiter: "\n" }, showTable: false, useMultiLevelHeaders: true, useRowspanHeaders: true, showExportInProgress: true }, lang: { downloadCSV: "Download CSV", downloadXLS: "Download XLS", exportData: { annotationHeader: "Annotations", categoryHeader: "Category", categoryDatetimeHeader: "DateTime" }, viewData: "View data table", hideData: "Hide data table", exportInProgress: "Exporting..." } };
      }), o(e, "Extensions/ExportData/ExportData.js", [e["Core/Renderer/HTML/AST.js"], e["Core/Defaults.js"], e["Extensions/DownloadURL.js"], e["Extensions/ExportData/ExportDataDefaults.js"], e["Core/Globals.js"], e["Core/Utilities.js"]], function(t2, e2, o2, a, n, i) {
        let { getOptions: r, setOptions: s } = e2, { downloadURL: l } = o2, { doc: h, win: c } = n, { addEvent: d, defined: p, extend: u, find: m, fireEvent: g, isNumber: x, pick: f } = i;
        function b(t3) {
          let e3 = !!this.options.exporting?.showExportInProgress, o3 = c.requestAnimationFrame || setTimeout;
          o3(() => {
            e3 && this.showLoading(this.options.lang.exportInProgress), o3(() => {
              try {
                t3.call(this);
              } finally {
                e3 && this.hideLoading();
              }
            });
          });
        }
        function y() {
          b.call(this, () => {
            let t3 = this.getCSV(true);
            l(A(t3, "text/csv") || "data:text/csv,\uFEFF" + encodeURIComponent(t3), this.getFilename() + ".csv");
          });
        }
        function w() {
          b.call(this, () => {
            let t3 = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Ark1</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><style>td{border:none;font-family: Calibri, sans-serif;} .number{mso-number-format:"0.00";} .text{ mso-number-format:"@";}</style><meta name=ProgId content=Excel.Sheet><meta charset=UTF-8></head><body>' + this.getTable(true) + "</body></html>";
            l(A(t3, "application/vnd.ms-excel") || "data:application/vnd.ms-excel;base64," + c.btoa(unescape(encodeURIComponent(t3))), this.getFilename() + ".xls");
          });
        }
        function D(t3) {
          let e3 = "", o3 = this.getDataRows(), a2 = this.options.exporting.csv, n2 = f(a2.decimalPoint, "," !== a2.itemDelimiter && t3 ? 1.1.toLocaleString()[1] : "."), i2 = f(a2.itemDelimiter, "," === n2 ? ";" : ","), r2 = a2.lineDelimiter;
          return o3.forEach((t4, a3) => {
            let s2 = "", l2 = t4.length;
            for (; l2--; ) "string" == typeof (s2 = t4[l2]) && (s2 = `"${s2}"`), "number" == typeof s2 && "." !== n2 && (s2 = s2.toString().replace(".", n2)), t4[l2] = s2;
            t4.length = o3.length ? o3[0].length : 0, e3 += t4.join(i2), a3 < o3.length - 1 && (e3 += r2);
          }), e3;
        }
        function T(t3) {
          let e3, o3;
          let a2 = this.hasParallelCoordinates, n2 = this.time, i2 = this.options.exporting && this.options.exporting.csv || {}, r2 = this.xAxis, s2 = {}, l2 = [], h2 = [], c2 = [], d2 = this.options.lang.exportData, u2 = d2.categoryHeader, b2 = d2.categoryDatetimeHeader, y2 = function(e4, o4, a3) {
            if (i2.columnHeaderFormatter) {
              let t4 = i2.columnHeaderFormatter(e4, o4, a3);
              if (false !== t4) return t4;
            }
            return e4 ? e4.bindAxes ? t3 ? { columnTitle: a3 > 1 ? o4 : e4.name, topLevelColumnTitle: e4.name } : e4.name + (a3 > 1 ? " (" + o4 + ")" : "") : e4.options.title && e4.options.title.text || (e4.dateTime ? b2 : u2) : u2;
          }, w2 = function(t4, e4, o4) {
            let a3 = {}, n3 = {};
            return e4.forEach(function(e5) {
              let i3 = (t4.keyToAxis && t4.keyToAxis[e5] || e5) + "Axis", r3 = x(o4) ? t4.chart[i3][o4] : t4[i3];
              a3[e5] = r3 && r3.categories || [], n3[e5] = r3 && r3.dateTime;
            }), { categoryMap: a3, dateTimeValueAxisMap: n3 };
          }, D2 = function(t4, e4) {
            let o4 = t4.pointArrayMap || ["y"];
            return t4.data.some((t5) => void 0 !== t5.y && t5.name) && e4 && !e4.categories && "name" !== t4.exportKey ? ["x", ...o4] : o4;
          }, T2 = [], v2, E2, L2, S2 = 0, C2, A2;
          for (C2 in this.series.forEach(function(e4) {
            let o4 = e4.options.keys, l3 = e4.xAxis, d3 = o4 || D2(e4, l3), p2 = d3.length, u3 = !e4.requireSorting && {}, g2 = r2.indexOf(l3), x2 = w2(e4, d3), b3, v3;
            if (false !== e4.options.includeInDataExport && !e4.options.isInternal && false !== e4.visible) {
              for (m(T2, function(t4) {
                return t4[0] === g2;
              }) || T2.push([g2, S2]), v3 = 0; v3 < p2; ) L2 = y2(e4, d3[v3], d3.length), c2.push(L2.columnTitle || L2), t3 && h2.push(L2.topLevelColumnTitle || L2), v3++;
              b3 = { chart: e4.chart, autoIncrement: e4.autoIncrement, options: e4.options, pointArrayMap: e4.pointArrayMap, index: e4.index }, e4.options.data.forEach(function(t4, o5) {
                let r3, h3, c3;
                let m2 = { series: b3 };
                a2 && (x2 = w2(e4, d3, o5)), e4.pointClass.prototype.applyOptions.apply(m2, [t4]);
                let y3 = e4.data[o5] && e4.data[o5].name;
                if (r3 = (m2.x ?? "") + "," + y3, v3 = 0, (!l3 || "name" === e4.exportKey || !a2 && l3 && l3.hasNames && y3) && (r3 = y3), u3 && (u3[r3] && (r3 += "|" + o5), u3[r3] = true), s2[r3]) {
                  let t5 = `${r3},${s2[r3].pointers[e4.index]}`, o6 = r3;
                  s2[r3].pointers[e4.index] && (s2[t5] || (s2[t5] = [], s2[t5].xValues = [], s2[t5].pointers = []), r3 = t5), s2[o6].pointers[e4.index] += 1;
                } else {
                  s2[r3] = [], s2[r3].xValues = [];
                  let t5 = [];
                  for (let o6 = 0; o6 < e4.chart.series.length; o6++) t5[o6] = 0;
                  s2[r3].pointers = t5, s2[r3].pointers[e4.index] = 1;
                }
                for (s2[r3].x = m2.x, s2[r3].name = y3, s2[r3].xValues[g2] = m2.x; v3 < p2; ) c3 = m2[h3 = d3[v3]], s2[r3][S2 + v3] = f(x2.categoryMap[h3][c3], x2.dateTimeValueAxisMap[h3] ? n2.dateFormat(i2.dateFormat, c3) : null, c3), v3++;
              }), S2 += v3;
            }
          }), s2) Object.hasOwnProperty.call(s2, C2) && l2.push(s2[C2]);
          for (E2 = t3 ? [h2, c2] : [c2], S2 = T2.length; S2--; ) e3 = T2[S2][0], o3 = T2[S2][1], v2 = r2[e3], l2.sort(function(t4, o4) {
            return t4.xValues[e3] - o4.xValues[e3];
          }), A2 = y2(v2), E2[0].splice(o3, 0, A2), t3 && E2[1] && E2[1].splice(o3, 0, A2), l2.forEach(function(t4) {
            let e4 = t4.name;
            v2 && !p(e4) && (v2.dateTime ? (t4.x instanceof Date && (t4.x = t4.x.getTime()), e4 = n2.dateFormat(i2.dateFormat, t4.x)) : e4 = v2.categories ? f(v2.names[t4.x], v2.categories[t4.x], t4.x) : t4.x), t4.splice(o3, 0, e4);
          });
          return g(this, "exportData", { dataRows: E2 = E2.concat(l2) }), E2;
        }
        function v(t3) {
          let e3 = (t4) => {
            if (!t4.tagName || "#text" === t4.tagName) return t4.textContent || "";
            let o3 = t4.attributes, a2 = `<${t4.tagName}`;
            return o3 && Object.keys(o3).forEach((t5) => {
              let e4 = o3[t5];
              a2 += ` ${t5}="${e4}"`;
            }), a2 += ">" + (t4.textContent || ""), (t4.children || []).forEach((t5) => {
              a2 += e3(t5);
            }), a2 += `</${t4.tagName}>`;
          };
          return e3(this.getTableAST(t3));
        }
        function E(t3) {
          let e3 = 0, o3 = [], a2 = this.options, n2 = t3 ? 1.1.toLocaleString()[1] : ".", i2 = f(a2.exporting.useMultiLevelHeaders, true), r2 = this.getDataRows(i2), s2 = i2 ? r2.shift() : null, l2 = r2.shift(), h2 = function(t4, e4) {
            let o4 = t4.length;
            if (e4.length !== o4) return false;
            for (; o4--; ) if (t4[o4] !== e4[o4]) return false;
            return true;
          }, c2 = function(t4, e4, o4, a3) {
            let i3 = f(a3, ""), r3 = "highcharts-text" + (e4 ? " " + e4 : "");
            return "number" == typeof i3 ? (i3 = i3.toString(), "," === n2 && (i3 = i3.replace(".", n2)), r3 = "highcharts-number") : a3 || (r3 = "highcharts-empty"), { tagName: t4, attributes: o4 = u({ class: r3 }, o4), textContent: i3 };
          };
          false !== a2.exporting.tableCaption && o3.push({ tagName: "caption", attributes: { class: "highcharts-table-caption" }, textContent: f(a2.exporting.tableCaption, a2.title.text ? a2.title.text : "Chart") });
          for (let t4 = 0, o4 = r2.length; t4 < o4; ++t4) r2[t4].length > e3 && (e3 = r2[t4].length);
          o3.push(function(t4, e4, o4) {
            let n3 = [], r3 = 0, s3 = o4 || e4 && e4.length, l3, d3 = 0, p3;
            if (i2 && t4 && e4 && !h2(t4, e4)) {
              let o5 = [];
              for (; r3 < s3; ++r3) if ((l3 = t4[r3]) === t4[r3 + 1]) ++d3;
              else if (d3) o5.push(c2("th", "highcharts-table-topheading", { scope: "col", colspan: d3 + 1 }, l3)), d3 = 0;
              else {
                l3 === e4[r3] ? a2.exporting.useRowspanHeaders ? (p3 = 2, delete e4[r3]) : (p3 = 1, e4[r3] = "") : p3 = 1;
                let t5 = c2("th", "highcharts-table-topheading", { scope: "col" }, l3);
                p3 > 1 && t5.attributes && (t5.attributes.valign = "top", t5.attributes.rowspan = p3), o5.push(t5);
              }
              n3.push({ tagName: "tr", children: o5 });
            }
            if (e4) {
              let t5 = [];
              for (r3 = 0, s3 = e4.length; r3 < s3; ++r3) void 0 !== e4[r3] && t5.push(c2("th", null, { scope: "col" }, e4[r3]));
              n3.push({ tagName: "tr", children: t5 });
            }
            return { tagName: "thead", children: n3 };
          }(s2, l2, Math.max(e3, l2.length)));
          let d2 = [];
          r2.forEach(function(t4) {
            let o4 = [];
            for (let a3 = 0; a3 < e3; a3++) o4.push(c2(a3 ? "td" : "th", null, a3 ? {} : { scope: "row" }, t4[a3]));
            d2.push({ tagName: "tr", children: o4 });
          }), o3.push({ tagName: "tbody", children: d2 });
          let p2 = { tree: { tagName: "table", id: `highcharts-data-table-${this.index}`, children: o3 } };
          return g(this, "aftergetTableAST", p2), p2.tree;
        }
        function L() {
          this.toggleDataTable(false);
        }
        function S(e3) {
          let o3 = (e3 = f(e3, !this.isDataTableVisible)) && !this.dataTableDiv;
          if (o3 && (this.dataTableDiv = h.createElement("div"), this.dataTableDiv.className = "highcharts-data-table", this.renderTo.parentNode.insertBefore(this.dataTableDiv, this.renderTo.nextSibling)), this.dataTableDiv) {
            let a3 = this.dataTableDiv.style, n3 = a3.display;
            a3.display = e3 ? "block" : "none", e3 ? (this.dataTableDiv.innerHTML = t2.emptyHTML, new t2([this.getTableAST()]).addToDOM(this.dataTableDiv), g(this, "afterViewData", { element: this.dataTableDiv, wasHidden: o3 || n3 !== a3.display })) : g(this, "afterHideData");
          }
          this.isDataTableVisible = e3;
          let a2 = this.exportDivElements, n2 = this.options.exporting, i2 = n2 && n2.buttons && n2.buttons.contextButton.menuItems, r2 = this.options.lang;
          if (n2 && n2.menuItemDefinitions && r2 && r2.viewData && r2.hideData && i2 && a2) {
            let e4 = a2[i2.indexOf("viewData")];
            e4 && t2.setElementHTML(e4, this.isDataTableVisible ? r2.hideData : r2.viewData);
          }
        }
        function C() {
          this.toggleDataTable(true);
        }
        function A(t3, e3) {
          let o3 = c.navigator, a2 = c.URL || c.webkitURL || c;
          try {
            if (o3.msSaveOrOpenBlob && c.MSBlobBuilder) {
              let e4 = new c.MSBlobBuilder();
              return e4.append(t3), e4.getBlob("image/svg+xml");
            }
            return a2.createObjectURL(new c.Blob(["\uFEFF" + t3], { type: e3 }));
          } catch (t4) {
          }
        }
        function R() {
          let t3 = this, e3 = t3.dataTableDiv, o3 = (t4, e4) => t4.children[e4].textContent, a2 = (t4, e4) => (a3, n2) => {
            let i2, r2;
            return i2 = o3(e4 ? a3 : n2, t4), r2 = o3(e4 ? n2 : a3, t4), "" === i2 || "" === r2 || isNaN(i2) || isNaN(r2) ? i2.toString().localeCompare(r2) : i2 - r2;
          };
          if (e3 && t3.options.exporting && t3.options.exporting.allowTableSorting) {
            let o4 = e3.querySelector("thead tr");
            o4 && o4.childNodes.forEach((o5) => {
              let n2 = o5.closest("table");
              o5.addEventListener("click", function() {
                let i2 = [...e3.querySelectorAll("tr:not(thead tr)")], r2 = [...o5.parentNode.children];
                i2.sort(a2(r2.indexOf(o5), t3.ascendingOrderInTable = !t3.ascendingOrderInTable)).forEach((t4) => {
                  n2.appendChild(t4);
                }), r2.forEach((t4) => {
                  ["highcharts-sort-ascending", "highcharts-sort-descending"].forEach((e4) => {
                    t4.classList.contains(e4) && t4.classList.remove(e4);
                  });
                }), o5.classList.add(t3.ascendingOrderInTable ? "highcharts-sort-ascending" : "highcharts-sort-descending");
              });
            });
          }
        }
        function k() {
          this.options && this.options.exporting && this.options.exporting.showTable && !this.options.chart.forExport && this.viewData();
        }
        function H() {
          this.dataTableDiv?.remove();
        }
        return { compose: function(t3, e3) {
          let o3 = t3.prototype;
          if (!o3.getCSV) {
            let n2 = r().exporting;
            d(t3, "afterViewData", R), d(t3, "render", k), d(t3, "destroy", H), o3.downloadCSV = y, o3.downloadXLS = w, o3.getCSV = D, o3.getDataRows = T, o3.getTable = v, o3.getTableAST = E, o3.hideData = L, o3.toggleDataTable = S, o3.viewData = C, n2 && (u(n2.menuItemDefinitions, { downloadCSV: { textKey: "downloadCSV", onclick: function() {
              this.downloadCSV();
            } }, downloadXLS: { textKey: "downloadXLS", onclick: function() {
              this.downloadXLS();
            } }, viewData: { textKey: "viewData", onclick: function() {
              b.call(this, this.toggleDataTable);
            } } }), n2.buttons && n2.buttons.contextButton.menuItems && n2.buttons.contextButton.menuItems.push("separator", "downloadCSV", "downloadXLS", "viewData")), s(a);
            let { arearange: i2, gantt: l2, map: h2, mapbubble: c2, treemap: p2, xrange: m2 } = e3.types;
            i2 && (i2.prototype.keyToAxis = { low: "y", high: "y" }), l2 && (l2.prototype.exportKey = "name", l2.prototype.keyToAxis = { start: "x", end: "x" }), h2 && (h2.prototype.exportKey = "name"), c2 && (c2.prototype.exportKey = "name"), p2 && (p2.prototype.exportKey = "name"), m2 && (m2.prototype.keyToAxis = { x2: "x" });
          }
        } };
      }), o(e, "masters/modules/export-data.src.js", [e["Core/Globals.js"], e["Extensions/DownloadURL.js"], e["Extensions/ExportData/ExportData.js"]], function(t2, e2, o2) {
        return t2.dataURLtoBlob = t2.dataURLtoBlob || e2.dataURLtoBlob, t2.downloadURL = t2.downloadURL || e2.downloadURL, o2.compose(t2.Chart, t2.Series), t2;
      });
    });
  }
});
export default require_export_data();
//# sourceMappingURL=highcharts_modules_export-data.js.map
