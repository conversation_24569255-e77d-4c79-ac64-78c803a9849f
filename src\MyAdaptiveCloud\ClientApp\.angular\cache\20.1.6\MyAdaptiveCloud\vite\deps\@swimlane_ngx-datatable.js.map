{"version": 3, "sources": ["../../../../../../node_modules/@swimlane/ngx-datatable/fesm2022/swimlane-ngx-datatable.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, EventEmitter, TemplateRef, Output, ContentChild, Input, inject, Injectable, booleanAttribute, numberAttribute, Renderer2, ElementRef, HostBinding, ChangeDetectionStrategy, Component, ChangeDetectorRef, HostListener, KeyValueDiffers, InjectionToken, ViewContainerRef, Injector, signal, IterableDiffers, ViewChild, computed, DOCUMENT, ContentChildren, NgZone, input, effect, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { Subject, fromEvent, takeUntil as takeUntil$1 } from 'rxjs';\nimport { NgTemplateOutlet, NgStyle, NgClass } from '@angular/common';\nimport { takeUntil } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  cellContext: a0\n});\nfunction DataTableBodyCellComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 1)(1, \"input\", 3);\n    i0.ɵɵlistener(\"click\", function DataTableBodyCellComponent_Conditional_1_Template_input_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"checked\", ctx_r1.isSelected);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 6);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 7);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 8);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function DataTableBodyCellComponent_Conditional_2_Conditional_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTreeAction());\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵconditionalCreate(2, DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_2_Template, 1, 0, \"i\", 6);\n    i0.ɵɵconditionalCreate(3, DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_3_Template, 1, 0, \"i\", 7);\n    i0.ɵɵconditionalCreate(4, DataTableBodyCellComponent_Conditional_2_Conditional_0_Conditional_4_Template, 1, 0, \"i\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.treeStatus === \"disabled\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.treeStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r1.treeStatus === \"loading\" ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.treeStatus === \"collapsed\" ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.treeStatus === \"expanded\" || ctx_r1.treeStatus === \"disabled\" ? 4 : -1);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction DataTableBodyCellComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableBodyCellComponent_Conditional_2_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.column.treeToggleTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, ctx_r1.cellContext));\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableBodyCellComponent_Conditional_2_Conditional_0_Template, 5, 5, \"button\", 4)(1, DataTableBodyCellComponent_Conditional_2_Conditional_1_Template, 1, 4, null, 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(!ctx_r1.column.treeToggleTemplate ? 0 : 1);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"title\", ctx_r1.sanitizedValue)(\"innerHTML\", ctx_r1.value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"title\", ctx_r1.sanitizedValue);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.value);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableBodyCellComponent_Conditional_3_Conditional_0_Template, 1, 2, \"span\", 9)(1, DataTableBodyCellComponent_Conditional_3_Conditional_1_Template, 2, 2, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.column.bindAsUnsafeHtml ? 0 : 1);\n  }\n}\nfunction DataTableBodyCellComponent_Conditional_4_ng_template_0_Template(rf, ctx) {}\nfunction DataTableBodyCellComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableBodyCellComponent_Conditional_4_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.column.cellTemplate)(\"ngTemplateOutletContext\", ctx_r1.cellContext);\n  }\n}\nconst _forTrack0 = ($index, $item) => $item.type;\nconst _forTrack1 = ($index, $item) => $item.$$id;\nfunction DataTableBodyRowComponent_For_1_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-body-cell\", 2);\n    i0.ɵɵlistener(\"activate\", function DataTableBodyRowComponent_For_1_Conditional_0_For_2_Template_datatable_body_cell_activate_0_listener($event) {\n      const ɵ$index_5_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onActivate($event, ɵ$index_5_r2));\n    })(\"treeAction\", function DataTableBodyRowComponent_For_1_Conditional_0_For_2_Template_datatable_body_cell_treeAction_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTreeAction());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"row\", ctx_r2.row)(\"group\", ctx_r2.group)(\"expanded\", ctx_r2.expanded)(\"isSelected\", ctx_r2.isSelected)(\"rowIndex\", ctx_r2.rowIndex)(\"column\", column_r4)(\"rowHeight\", ctx_r2.rowHeight)(\"displayCheck\", ctx_r2.displayCheck)(\"disabled\", ctx_r2.disabled)(\"treeStatus\", ctx_r2.treeStatus);\n  }\n}\nfunction DataTableBodyRowComponent_For_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵrepeaterCreate(1, DataTableBodyRowComponent_For_1_Conditional_0_For_2_Template, 1, 10, \"datatable-body-cell\", 1, _forTrack1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const colGroup_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"datatable-row-\", colGroup_r5.type, \" datatable-row-group\"));\n    i0.ɵɵstyleProp(\"width\", ctx_r2._columnGroupWidths[colGroup_r5.type], \"px\");\n    i0.ɵɵclassProp(\"row-disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(colGroup_r5.columns);\n  }\n}\nfunction DataTableBodyRowComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableBodyRowComponent_For_1_Conditional_0_Template, 3, 7, \"div\", 0);\n  }\n  if (rf & 2) {\n    const colGroup_r5 = ctx.$implicit;\n    i0.ɵɵconditional(colGroup_r5.columns.length ? 0 : -1);\n  }\n}\nfunction DatatableRowDefComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.rowDef.rowDefInternal.rowTemplate)(\"ngTemplateOutletContext\", ctx_r0.rowContext);\n  }\n}\nconst _c2 = [\"select\"];\nfunction DataTableRowWrapperComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 6)(2, \"input\", 7, 0);\n    i0.ɵɵlistener(\"change\", function DataTableRowWrapperComponent_Conditional_0_Conditional_2_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const select_r2 = i0.ɵɵreference(3);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCheckboxChange(select_r2.checked, ctx_r2.row));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r2.selectedGroupRows().length === ctx_r2.row.value.length);\n  }\n}\nfunction DataTableRowWrapperComponent_Conditional_0_ng_template_3_Template(rf, ctx) {}\nfunction DataTableRowWrapperComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵconditionalCreate(2, DataTableRowWrapperComponent_Conditional_0_Conditional_2_Template, 4, 1, \"div\");\n    i0.ɵɵtemplate(3, DataTableRowWrapperComponent_Conditional_0_ng_template_3_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r2.groupHeaderRowHeight, \"px\")(\"width\", ctx_r2.innerWidth, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r2.groupHeader.checkboxable ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.groupHeader.template)(\"ngTemplateOutletContext\", ctx_r2.context);\n  }\n}\nfunction DataTableRowWrapperComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction DataTableRowWrapperComponent_Conditional_2_ng_template_1_Template(rf, ctx) {}\nfunction DataTableRowWrapperComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, DataTableRowWrapperComponent_Conditional_2_ng_template_1_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r2.detailRowHeight, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.rowDetail.template)(\"ngTemplateOutletContext\", ctx_r2.context);\n  }\n}\nconst _c3 = a0 => ({\n  index: a0\n});\nfunction DataTableSummaryRowComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"datatable-body-row\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerWidth\", ctx_r0.innerWidth)(\"columns\", ctx_r0._internalColumns)(\"rowHeight\", ctx_r0.rowHeight)(\"row\", ctx_r0.summaryRow)(\"rowIndex\", i0.ɵɵpureFunction1(5, _c3, -1));\n  }\n}\nconst _c4 = () => [];\nfunction DataTableGhostLoaderComponent_For_2_For_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 5);\n  }\n}\nfunction DataTableGhostLoaderComponent_For_2_For_2_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction DataTableGhostLoaderComponent_For_2_For_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableGhostLoaderComponent_For_2_For_2_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    const col_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngTemplateOutlet\", col_r1.ghostCellTemplate);\n  }\n}\nfunction DataTableGhostLoaderComponent_For_2_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵconditionalCreate(1, DataTableGhostLoaderComponent_For_2_For_2_Conditional_1_Template, 1, 0, \"div\", 5)(2, DataTableGhostLoaderComponent_For_2_For_2_Conditional_2_Template, 1, 1, null, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", col_r1.width, \"px\");\n    i0.ɵɵclassProp(\"datatable-body-cell\", ctx_r1.cellMode);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!col_r1.ghostCellTemplate ? 1 : 2);\n  }\n}\nfunction DataTableGhostLoaderComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵrepeaterCreate(1, DataTableGhostLoaderComponent_For_2_For_2_Template, 3, 5, \"div\", 3, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r1.rowHeight, \"px\");\n    i0.ɵɵclassProp(\"datatable-body-row\", ctx_r1.cellMode);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.columns);\n  }\n}\nconst _c5 = [[[\"\", \"loading-indicator\", \"\"]], [[\"\", \"empty-content\", \"\"]]];\nconst _c6 = [\"[loading-indicator]\", \"[empty-content]\"];\nconst _c7 = (a0, a1) => ({\n  index: a0,\n  indexInGroup: a1\n});\nconst _c8 = (a0, a1, a2, a3) => ({\n  template: a0,\n  rowTemplate: a1,\n  row: a2,\n  index: a3\n});\nconst _c9 = (a0, a1, a2) => ({\n  row: a0,\n  index: a1,\n  disabled: a2\n});\nconst _c10 = (a0, a1, a2, a3, a4) => ({\n  row: a0,\n  groupedRows: a1,\n  index: a2,\n  indexInGroup: a3,\n  disabled: a4\n});\nfunction DataTableBodyComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 5);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataTableBodyComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ghost-loader\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"columns\", ctx_r0.columns)(\"pageSize\", ctx_r0.pageSize)(\"rowHeight\", ctx_r0.rowHeight)(\"ghostBodyHeight\", ctx_r0.bodyHeight);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"datatable-summary-row\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rowHeight\", ctx_r0.summaryHeight)(\"innerWidth\", ctx_r0.innerWidth)(\"rows\", ctx_r0.rows)(\"columns\", ctx_r0.columns);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-body-row\", 10, 1);\n    i0.ɵɵlistener(\"treeAction\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_treeAction_0_listener() {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onTreeAction(row_r4));\n    })(\"activate\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_activate_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onActivate($event, index_r5));\n    })(\"drop\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_drop_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const rowElement_r6 = i0.ɵɵreference(1);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.drop($event, row_r4, rowElement_r6));\n    })(\"dragover\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_dragover_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.dragOver($event, row_r4));\n    })(\"dragenter\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_dragenter_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const rowElement_r6 = i0.ɵɵreference(1);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.dragEnter($event, row_r4, rowElement_r6));\n    })(\"dragleave\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_dragleave_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const rowElement_r6 = i0.ɵɵreference(1);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.dragLeave($event, row_r4, rowElement_r6));\n    })(\"dragstart\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_dragstart_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const rowElement_r6 = i0.ɵɵreference(1);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.drag($event, row_r4, rowElement_r6));\n    })(\"dragend\", function DataTableBodyComponent_Conditional_2_ng_template_2_Template_datatable_body_row_dragend_0_listener($event) {\n      const row_r4 = i0.ɵɵrestoreView(_r3).row;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.dragEnd($event, row_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r4 = ctx.row;\n    const index_r5 = ctx.index;\n    const indexInGroup_r7 = ctx.indexInGroup;\n    const groupedRows_r8 = ctx.groupedRows;\n    const disabled_r9 = ctx.disabled;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", disabled_r9)(\"isSelected\", ctx_r0.getRowSelected(row_r4))(\"innerWidth\", ctx_r0.innerWidth)(\"columns\", ctx_r0.columns)(\"rowHeight\", ctx_r0.getRowHeight(row_r4))(\"row\", row_r4)(\"group\", groupedRows_r8)(\"rowIndex\", i0.ɵɵpureFunction2(14, _c7, index_r5, indexInGroup_r7))(\"expanded\", ctx_r0.getRowExpanded(row_r4))(\"rowClass\", ctx_r0.rowClass)(\"displayCheck\", ctx_r0.displayCheck)(\"treeStatus\", row_r4 == null ? null : row_r4.treeStatus)(\"draggable\", ctx_r0.rowDraggable)(\"verticalScrollVisible\", ctx_r0.verticalScrollVisible);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ghost-loader\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"columns\", ctx_r0.columns)(\"pageSize\", 1)(\"rowHeight\", ctx_r0.rowHeight);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 14);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const disabled_r11 = i0.ɵɵreadContextLet(0);\n    const ctx_r11 = i0.ɵɵnextContext();\n    const group_r13 = ctx_r11.$implicit;\n    const ɵ$index_22_r14 = ctx_r11.$index;\n    i0.ɵɵnextContext();\n    const bodyRow_r15 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rowDefInternal\", i0.ɵɵpureFunction4(2, _c8, ctx_r0.rowDefTemplate, bodyRow_r15, group_r13, ɵ$index_22_r14))(\"rowDefInternalDisabled\", disabled_r11);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 15);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const disabled_r11 = i0.ɵɵreadContextLet(0);\n    const ctx_r11 = i0.ɵɵnextContext();\n    const group_r13 = ctx_r11.$implicit;\n    const ɵ$index_22_r14 = ctx_r11.$index;\n    i0.ɵɵnextContext();\n    const bodyRow_r15 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", bodyRow_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c9, group_r13, ctx_r0.indexes().first + ɵ$index_22_r14, disabled_r11));\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_3_Conditional_0_Template, 1, 6, \"ng-container\", 15);\n  }\n  if (rf & 2) {\n    const group_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(ctx_r0.isRow(group_r13) ? 0 : -1);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_4_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 15);\n  }\n  if (rf & 2) {\n    const row_r16 = ctx.$implicit;\n    const $index_r17 = ctx.$index;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    const group_r13 = ctx_r11.$implicit;\n    const ɵ$index_22_r14 = ctx_r11.$index;\n    i0.ɵɵnextContext();\n    const bodyRow_r15 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    const disabled_r18 = ctx_r0.disableRowCheck && ctx_r0.disableRowCheck(row_r16);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", bodyRow_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c10, row_r16, group_r13 == null ? null : group_r13.value, ctx_r0.indexes().first + ɵ$index_22_r14, $index_r17, disabled_r18));\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_4_For_1_Template, 1, 8, \"ng-container\", 15, i0.ɵɵcomponentInstance().rowTrackingFn, true);\n  }\n  if (rf & 2) {\n    const group_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵrepeater(group_r13.value);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵdeclareLet(0);\n    i0.ɵɵelementStart(1, \"datatable-row-wrapper\", 13);\n    i0.ɵɵlistener(\"rowContextmenu\", function DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Template_datatable_row_wrapper_rowContextmenu_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.rowContextmenu.emit($event));\n    });\n    i0.ɵɵconditionalCreate(2, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_2_Template, 1, 7, \"ng-container\")(3, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_3_Template, 1, 1);\n    i0.ɵɵconditionalCreate(4, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Conditional_4_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    const group_r13 = ctx_r11.$implicit;\n    const ɵ$index_22_r14 = ctx_r11.$index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const disabled_r19 = i0.ɵɵstoreLet(ctx_r0.isRow(group_r13) && ctx_r0.disableRowCheck && ctx_r0.disableRowCheck(group_r13));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r0.groupedRows ? ctx_r0.columnGroupWidths.total : undefined);\n    i0.ɵɵproperty(\"groupedRows\", ctx_r0.groupedRows)(\"innerWidth\", ctx_r0.innerWidth)(\"rowDetail\", ctx_r0.rowDetail)(\"groupHeader\", ctx_r0.groupHeader)(\"offsetX\", ctx_r0.offsetX)(\"detailRowHeight\", ctx_r0.getDetailRowHeight(group_r13 && group_r13[ɵ$index_22_r14], ɵ$index_22_r14))(\"groupHeaderRowHeight\", ctx_r0.getGroupHeaderRowHeight(group_r13 && group_r13[ɵ$index_22_r14], ɵ$index_22_r14))(\"row\", group_r13)(\"disabled\", disabled_r19)(\"expanded\", ctx_r0.getRowExpanded(group_r13))(\"rowIndex\", ctx_r0.indexes().first + ɵ$index_22_r14)(\"selected\", ctx_r0.selected);\n    i0.ɵɵattribute(\"hidden\", ctx_r0.ghostLoadingIndicator && (!ctx_r0.rowCount || !ctx_r0.virtualization || !ctx_r0.scrollbarV) ? true : null);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.rowDefTemplate ? 2 : 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.isGroup(group_r13) ? 4 : -1);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_For_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableBodyComponent_Conditional_2_For_6_Conditional_0_Template, 1, 3, \"ghost-loader\", 11)(1, DataTableBodyComponent_Conditional_2_For_6_Conditional_1_Template, 5, 18, \"datatable-row-wrapper\", 12);\n  }\n  if (rf & 2) {\n    const group_r13 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(!group_r13 && ctx_r0.ghostLoadingIndicator ? 0 : group_r13 ? 1 : -1);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"datatable-summary-row\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rowHeight\", ctx_r0.summaryHeight)(\"innerWidth\", ctx_r0.innerWidth)(\"rows\", ctx_r0.rows)(\"columns\", ctx_r0.columns);\n  }\n}\nfunction DataTableBodyComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-scroller\", 6);\n    i0.ɵɵlistener(\"scroll\", function DataTableBodyComponent_Conditional_2_Template_datatable_scroller_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onBodyScroll($event));\n    });\n    i0.ɵɵconditionalCreate(1, DataTableBodyComponent_Conditional_2_Conditional_1_Template, 1, 4, \"datatable-summary-row\", 7);\n    i0.ɵɵtemplate(2, DataTableBodyComponent_Conditional_2_ng_template_2_Template, 2, 17, \"ng-template\", 8, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵrepeaterCreate(5, DataTableBodyComponent_Conditional_2_For_6_Template, 2, 1, null, null, i0.ɵɵcomponentInstance().rowTrackingFn, true);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵconditionalCreate(7, DataTableBodyComponent_Conditional_2_Conditional_7_Template, 1, 4, \"datatable-summary-row\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"scrollbarV\", ctx_r0.scrollbarV)(\"scrollbarH\", ctx_r0.scrollbarH)(\"scrollHeight\", ctx_r0.scrollHeight())(\"scrollWidth\", ctx_r0.columnGroupWidths == null ? null : ctx_r0.columnGroupWidths.total);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.summaryRow && ctx_r0.summaryPosition === \"top\" ? 1 : -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", ctx_r0.renderOffset());\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.rowsToRender());\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.summaryRow && ctx_r0.summaryPosition === \"bottom\" ? 7 : -1);\n  }\n}\nfunction DataTableBodyComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-scroller\", 16);\n    i0.ɵɵlistener(\"scroll\", function DataTableBodyComponent_Conditional_3_Template_datatable_scroller_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onBodyScroll($event));\n    });\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r0.scrollbarH ? (ctx_r0.columnGroupWidths == null ? null : ctx_r0.columnGroupWidths.total) + \"px\" : \"100%\");\n    i0.ɵɵproperty(\"scrollbarV\", ctx_r0.scrollbarV)(\"scrollbarH\", ctx_r0.scrollbarH)(\"scrollHeight\", ctx_r0.scrollHeight());\n  }\n}\nfunction DataTableHeaderCellComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction DataTableHeaderCellComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableHeaderCellComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.targetMarkerTemplate)(\"ngTemplateOutletContext\", ctx_r0.targetMarkerContext);\n  }\n}\nfunction DataTableHeaderCellComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 2)(1, \"input\", 6);\n    i0.ɵɵlistener(\"change\", function DataTableHeaderCellComponent_Conditional_2_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.select.emit());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.allRowsSelected);\n  }\n}\nfunction DataTableHeaderCellComponent_Conditional_3_ng_template_0_Template(rf, ctx) {}\nfunction DataTableHeaderCellComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableHeaderCellComponent_Conditional_3_ng_template_0_Template, 0, 0, \"ng-template\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.column.headerTemplate)(\"ngTemplateOutletContext\", ctx_r0.cellContext);\n  }\n}\nfunction DataTableHeaderCellComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 3)(1, \"span\", 7);\n    i0.ɵɵlistener(\"click\", function DataTableHeaderCellComponent_Conditional_4_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSort());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.name, \" \");\n  }\n}\nfunction DataTableHeaderCellComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵlistener(\"mousedown\", function DataTableHeaderCellComponent_Conditional_6_Template_span_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMousedown($event));\n    })(\"touchstart\", function DataTableHeaderCellComponent_Conditional_6_Template_span_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMousedown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DataTableHeaderComponent_For_2_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-header-cell\", 3);\n    i0.ɵɵlistener(\"resize\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_resize_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onColumnResized($event));\n    })(\"resizing\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_resizing_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onColumnResizing($event));\n    })(\"longPressStart\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_longPressStart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLongPressStart($event));\n    })(\"longPressEnd\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_longPressEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLongPressEnd($event));\n    })(\"sort\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_sort_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSort($event));\n    })(\"select\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_select_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.select.emit($event));\n    })(\"columnContextmenu\", function DataTableHeaderComponent_For_2_Conditional_0_For_2_Template_datatable_header_cell_columnContextmenu_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.columnContextmenu.emit($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pressModel\", column_r3)(\"pressEnabled\", ctx_r1.reorderable && column_r3.draggable)(\"dragX\", ctx_r1.reorderable && column_r3.draggable && column_r3.dragging)(\"dragY\", false)(\"dragModel\", column_r3)(\"dragEventTarget\", ctx_r1.dragEventTarget)(\"headerHeight\", ctx_r1.headerHeight)(\"isTarget\", column_r3.isTarget)(\"targetMarkerTemplate\", ctx_r1.targetMarkerTemplate)(\"targetMarkerContext\", column_r3.targetMarkerContext)(\"column\", column_r3)(\"sortType\", ctx_r1.sortType)(\"sorts\", ctx_r1.sorts)(\"selectionType\", ctx_r1.selectionType)(\"sortAscendingIcon\", ctx_r1.sortAscendingIcon)(\"sortDescendingIcon\", ctx_r1.sortDescendingIcon)(\"sortUnsetIcon\", ctx_r1.sortUnsetIcon)(\"allRowsSelected\", ctx_r1.allRowsSelected)(\"enableClearingSortState\", ctx_r1.enableClearingSortState);\n  }\n}\nfunction DataTableHeaderComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵrepeaterCreate(1, DataTableHeaderComponent_For_2_Conditional_0_For_2_Template, 1, 19, \"datatable-header-cell\", 2, _forTrack1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const colGroup_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"datatable-row-\" + colGroup_r4.type)(\"ngStyle\", ctx_r1._styleByGroup[colGroup_r4.type]);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(colGroup_r4.columns);\n  }\n}\nfunction DataTableHeaderComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, DataTableHeaderComponent_For_2_Conditional_0_Template, 3, 2, \"div\", 1);\n  }\n  if (rf & 2) {\n    const colGroup_r4 = ctx.$implicit;\n    i0.ɵɵconditional(colGroup_r4.columns.length ? 0 : -1);\n  }\n}\nconst _forTrack2 = ($index, $item) => $item.number;\nfunction DataTablePagerComponent_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵdomElementStart(0, \"li\", 3)(1, \"a\", 1);\n    i0.ɵɵdomListener(\"click\", function DataTablePagerComponent_For_8_Template_a_click_1_listener() {\n      const pg_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectPage(pg_r2.number));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵdomElementEnd()();\n  }\n  if (rf & 2) {\n    const pg_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", pg_r2.number === ctx_r2.page);\n    i0.ɵɵattribute(\"aria-label\", (ctx_r2.messages.ariaPageNMessage ?? \"page\") + \" \" + pg_r2.number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", pg_r2.text, \" \");\n  }\n}\nconst _c11 = a0 => ({\n  \"selected-count\": a0\n});\nconst _c12 = (a0, a1, a2, a3, a4) => ({\n  rowCount: a0,\n  pageSize: a1,\n  selectedCount: a2,\n  curPage: a3,\n  offset: a4\n});\nfunction DataTableFooterComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction DataTableFooterComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DataTableFooterComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c12, ctx_r0.rowCount, ctx_r0.pageSize, ctx_r0.selectedCount, ctx_r0.curPage, ctx_r0.offset));\n  }\n}\nfunction DataTableFooterComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.selectedCount == null ? null : ctx_r0.selectedCount.toLocaleString(), \" \", ctx_r0.selectedMessage, \" / \");\n  }\n}\nfunction DataTableFooterComponent_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-pager\", 4);\n    i0.ɵɵlistener(\"change\", function DataTableFooterComponent_Conditional_2_Conditional_3_Template_datatable_pager_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.page.emit($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pagerLeftArrowIcon\", ctx_r0.pagerLeftArrowIcon)(\"pagerRightArrowIcon\", ctx_r0.pagerRightArrowIcon)(\"pagerPreviousIcon\", ctx_r0.pagerPreviousIcon)(\"pagerNextIcon\", ctx_r0.pagerNextIcon)(\"page\", ctx_r0.curPage)(\"size\", ctx_r0.pageSize)(\"count\", ctx_r0.rowCount);\n  }\n}\nfunction DataTableFooterComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵconditionalCreate(1, DataTableFooterComponent_Conditional_2_Conditional_1_Template, 2, 2, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵconditionalCreate(3, DataTableFooterComponent_Conditional_2_Conditional_3_Template, 1, 7, \"datatable-pager\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.selectedMessage ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.rowCount == null ? null : ctx_r0.rowCount.toLocaleString(), \" \", ctx_r0.totalMessage, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isVisible ? 3 : -1);\n  }\n}\nfunction DatatableComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-header\", 5);\n    i0.ɵɵlistener(\"sort\", function DatatableComponent_Conditional_2_Template_datatable_header_sort_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnSort($event));\n    })(\"resize\", function DatatableComponent_Conditional_2_Template_datatable_header_resize_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnResize($event));\n    })(\"resizing\", function DatatableComponent_Conditional_2_Template_datatable_header_resizing_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnResizing($event));\n    })(\"reorder\", function DatatableComponent_Conditional_2_Template_datatable_header_reorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    })(\"select\", function DatatableComponent_Conditional_2_Template_datatable_header_select_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHeaderSelect());\n    })(\"columnContextmenu\", function DatatableComponent_Conditional_2_Template_datatable_header_columnContextmenu_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnContextmenu($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"sorts\", ctx_r1.sorts)(\"sortType\", ctx_r1.sortType)(\"scrollbarH\", ctx_r1.scrollbarH)(\"innerWidth\", ctx_r1._innerWidth)(\"offsetX\", ctx_r1._offsetX)(\"dealsWithGroup\", ctx_r1.groupedRows !== undefined)(\"columns\", ctx_r1._internalColumns)(\"headerHeight\", ctx_r1.headerHeight)(\"reorderable\", ctx_r1.reorderable)(\"targetMarkerTemplate\", ctx_r1.targetMarkerTemplate)(\"sortAscendingIcon\", ctx_r1.cssClasses.sortAscending)(\"sortDescendingIcon\", ctx_r1.cssClasses.sortDescending)(\"sortUnsetIcon\", ctx_r1.cssClasses.sortUnset)(\"allRowsSelected\", ctx_r1.allRowsSelected)(\"selectionType\", ctx_r1.selectionType)(\"verticalScrollVisible\", ctx_r1.verticalScrollVisible)(\"enableClearingSortState\", ctx_r1.enableClearingSortState);\n  }\n}\nfunction DatatableComponent_ProjectionFallback_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"datatable-progress\");\n  }\n}\nfunction DatatableComponent_ProjectionFallback_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.messages.emptyMessage ?? \"No data to display\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DatatableComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"datatable-footer\", 8);\n    i0.ɵɵlistener(\"page\", function DatatableComponent_Conditional_8_Template_datatable_footer_page_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFooterPage($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rowCount\", ctx_r1.groupedRows !== undefined ? ctx_r1._internalRows.length : ctx_r1.rowCount)(\"pageSize\", ctx_r1.pageSize)(\"offset\", ctx_r1.offset)(\"footerHeight\", ctx_r1.footerHeight)(\"footerTemplate\", ctx_r1.footer)(\"totalMessage\", ctx_r1.messages.totalMessage ?? \"total\")(\"pagerLeftArrowIcon\", ctx_r1.cssClasses.pagerLeftArrow)(\"pagerRightArrowIcon\", ctx_r1.cssClasses.pagerRightArrow)(\"pagerPreviousIcon\", ctx_r1.cssClasses.pagerPrevious)(\"selectedCount\", ctx_r1.selected.length)(\"selectedMessage\", !!ctx_r1.selectionType && (ctx_r1.messages.selectedMessage ?? \"selected\"))(\"pagerNextIcon\", ctx_r1.cssClasses.pagerNext);\n  }\n}\nclass DataTableFooterTemplateDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DataTableFooterTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableFooterTemplateDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableFooterTemplateDirective,\n      selectors: [[\"\", \"ngx-datatable-footer-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableFooterTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-footer-template]'\n    }]\n  }], null, null);\n})();\nclass DatatableGroupHeaderTemplateDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DatatableGroupHeaderTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableGroupHeaderTemplateDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableGroupHeaderTemplateDirective,\n      selectors: [[\"\", \"ngx-datatable-group-header-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableGroupHeaderTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-group-header-template]'\n    }]\n  }], null, null);\n})();\nclass DatatableGroupHeaderDirective {\n  constructor() {\n    /**\n     * Row height is required when virtual scroll is enabled.\n     */\n    this.rowHeight = 0;\n    /**\n     * Show checkbox at group header to select all rows of the group.\n     */\n    this.checkboxable = false;\n    /**\n     * Track toggling of group visibility\n     */\n    this.toggle = new EventEmitter();\n  }\n  get template() {\n    return this._templateInput || this._templateQuery;\n  }\n  /**\n   * Toggle the expansion of a group\n   */\n  toggleExpandGroup(group) {\n    this.toggle.emit({\n      type: 'group',\n      value: group\n    });\n  }\n  /**\n   * Expand all groups\n   */\n  expandAllGroups() {\n    this.toggle.emit({\n      type: 'all',\n      value: true\n    });\n  }\n  /**\n   * Collapse all groups\n   */\n  collapseAllGroups() {\n    this.toggle.emit({\n      type: 'all',\n      value: false\n    });\n  }\n  static {\n    this.ɵfac = function DatatableGroupHeaderDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableGroupHeaderDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableGroupHeaderDirective,\n      selectors: [[\"ngx-datatable-group-header\"]],\n      contentQueries: function DatatableGroupHeaderDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DatatableGroupHeaderTemplateDirective, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n        }\n      },\n      inputs: {\n        rowHeight: \"rowHeight\",\n        checkboxable: \"checkboxable\",\n        _templateInput: [0, \"template\", \"_templateInput\"]\n      },\n      outputs: {\n        toggle: \"toggle\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableGroupHeaderDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ngx-datatable-group-header'\n    }]\n  }], null, {\n    rowHeight: [{\n      type: Input\n    }],\n    checkboxable: [{\n      type: Input\n    }],\n    _templateInput: [{\n      type: Input,\n      args: ['template']\n    }],\n    _templateQuery: [{\n      type: ContentChild,\n      args: [DatatableGroupHeaderTemplateDirective, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    toggle: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Always returns the empty string ''\n */\nfunction emptyStringGetter() {\n  return '';\n}\n/**\n * Returns the appropriate getter function for this kind of prop.\n * If prop == null, returns the emptyStringGetter.\n */\nfunction getterForProp(prop) {\n  // TODO requires better typing which will also involve adjust TableColum. So postponing it.\n  if (prop == null) {\n    return emptyStringGetter;\n  }\n  if (typeof prop === 'number') {\n    return numericIndexGetter;\n  } else {\n    // deep or simple\n    if (prop.indexOf('.') !== -1) {\n      return deepValueGetter;\n    } else {\n      return shallowValueGetter;\n    }\n  }\n}\n/**\n * Returns the value at this numeric index.\n * @param row array of values\n * @param index numeric index\n * @returns any or '' if invalid index\n */\nfunction numericIndexGetter(row, index) {\n  if (row == null) {\n    return '';\n  }\n  // mimic behavior of deepValueGetter\n  if (!row || index == null) {\n    return row;\n  }\n  const value = row[index];\n  if (value == null) {\n    return '';\n  }\n  return value;\n}\n/**\n * Returns the value of a field.\n * (more efficient than deepValueGetter)\n * @param obj object containing the field\n * @param fieldName field name string\n */\nfunction shallowValueGetter(obj, fieldName) {\n  if (obj == null) {\n    return '';\n  }\n  if (!obj || !fieldName) {\n    return obj;\n  }\n  const value = obj[fieldName];\n  if (value == null) {\n    return '';\n  }\n  return value;\n}\n/**\n * Returns a deep object given a string. zoo['animal.type']\n */\nfunction deepValueGetter(obj, path) {\n  if (obj == null) {\n    return '';\n  }\n  if (!obj || !path) {\n    return obj;\n  }\n  // check if path matches a root-level field\n  // { \"a.b.c\": 123 }\n  let current = obj[path];\n  if (current !== undefined) {\n    return current;\n  }\n  current = obj;\n  const split = path.split('.');\n  if (split.length) {\n    for (let i = 0; i < split.length; i++) {\n      current = current[split[i]];\n      // if found undefined, return empty string\n      if (current === undefined || current === null) {\n        return '';\n      }\n    }\n  }\n  return current;\n}\nfunction optionalGetterForProp(prop) {\n  return prop ? row => getterForProp(prop)(row, prop) : undefined;\n}\n/**\n * This functions rearrange items by their parents\n * Also sets the level value to each of the items\n *\n * Note: Expecting each item has a property called parentId\n * Note: This algorithm will fail if a list has two or more items with same ID\n * NOTE: This algorithm will fail if there is a deadlock of relationship\n *\n * For example,\n *\n * Input\n *\n * id -> parent\n * 1  -> 0\n * 2  -> 0\n * 3  -> 1\n * 4  -> 1\n * 5  -> 2\n * 7  -> 8\n * 6  -> 3\n *\n *\n * Output\n * id -> level\n * 1      -> 0\n * --3    -> 1\n * ----6  -> 2\n * --4    -> 1\n * 2      -> 0\n * --5    -> 1\n * 7     -> 8\n *\n *\n * @param rows\n *\n */\nfunction groupRowsByParents(rows, from, to) {\n  if (from && to) {\n    const treeRows = rows.filter(row => !!row).map(row => new TreeNode(row));\n    const uniqIDs = new Map(treeRows.map(node => [to(node.row), node]));\n    const rootNodes = treeRows.reduce((root, node) => {\n      const fromValue = from(node.row);\n      const parent = uniqIDs.get(fromValue);\n      if (parent) {\n        node.row.level = parent.row.level + 1; // TODO: should be reflected by type, that level is defined\n        node.parent = parent;\n        parent.children.push(node);\n      } else {\n        node.row.level = 0;\n        root.push(node);\n      }\n      return root;\n    }, []);\n    return rootNodes.flatMap(child => child.flatten());\n  } else {\n    return rows;\n  }\n}\nclass TreeNode {\n  constructor(row) {\n    this.row = row;\n    this.children = [];\n  }\n  flatten() {\n    if (this.row.treeStatus === 'expanded') {\n      return [this.row, ...this.children.flatMap(child => child.flatten())];\n    } else {\n      return [this.row];\n    }\n  }\n}\nclass DataTableColumnHeaderDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DataTableColumnHeaderDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableColumnHeaderDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableColumnHeaderDirective,\n      selectors: [[\"\", \"ngx-datatable-header-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableColumnHeaderDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-header-template]'\n    }]\n  }], null, null);\n})();\nclass DataTableColumnCellDirective {\n  constructor() {\n    this.template = inject(TemplateRef);\n  }\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DataTableColumnCellDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableColumnCellDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableColumnCellDirective,\n      selectors: [[\"\", \"ngx-datatable-cell-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableColumnCellDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-cell-template]'\n    }]\n  }], null, null);\n})();\nclass DataTableColumnCellTreeToggle {\n  constructor() {\n    this.template = inject(TemplateRef);\n  }\n  static {\n    this.ɵfac = function DataTableColumnCellTreeToggle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableColumnCellTreeToggle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableColumnCellTreeToggle,\n      selectors: [[\"\", \"ngx-datatable-tree-toggle\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableColumnCellTreeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-tree-toggle]'\n    }]\n  }], null, null);\n})();\n\n/**\n * service to make DatatableComponent aware of changes to\n * input bindings of DataTableColumnDirective\n */\nclass ColumnChangesService {\n  constructor() {\n    this.columnInputChanges = new Subject();\n  }\n  get columnInputChanges$() {\n    return this.columnInputChanges.asObservable();\n  }\n  onInputChange() {\n    this.columnInputChanges.next(undefined);\n  }\n  static {\n    this.ɵfac = function ColumnChangesService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ColumnChangesService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ColumnChangesService,\n      factory: ColumnChangesService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColumnChangesService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DataTableColumnGhostCellDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DataTableColumnGhostCellDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableColumnGhostCellDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableColumnGhostCellDirective,\n      selectors: [[\"\", \"ngx-datatable-ghost-cell-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableColumnGhostCellDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-ghost-cell-template]'\n    }]\n  }], null, null);\n})();\nclass DataTableColumnDirective {\n  constructor() {\n    this.columnChangesService = inject(ColumnChangesService);\n    this.isFirstChange = true;\n  }\n  get cellTemplate() {\n    return this._cellTemplateInput || this._cellTemplateQuery;\n  }\n  get headerTemplate() {\n    return this._headerTemplateInput || this._headerTemplateQuery;\n  }\n  get treeToggleTemplate() {\n    return this._treeToggleTemplateInput || this._treeToggleTemplateQuery;\n  }\n  get ghostCellTemplate() {\n    return this._ghostCellTemplateInput || this._ghostCellTemplateQuery;\n  }\n  ngOnChanges() {\n    if (this.isFirstChange) {\n      this.isFirstChange = false;\n    } else {\n      this.columnChangesService.onInputChange();\n    }\n  }\n  static {\n    this.ɵfac = function DataTableColumnDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableColumnDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableColumnDirective,\n      selectors: [[\"ngx-datatable-column\"]],\n      contentQueries: function DataTableColumnDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DataTableColumnCellDirective, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, DataTableColumnHeaderDirective, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, DataTableColumnCellTreeToggle, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, DataTableColumnGhostCellDirective, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._cellTemplateQuery = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headerTemplateQuery = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._treeToggleTemplateQuery = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._ghostCellTemplateQuery = _t.first);\n        }\n      },\n      inputs: {\n        name: \"name\",\n        prop: \"prop\",\n        bindAsUnsafeHtml: [2, \"bindAsUnsafeHtml\", \"bindAsUnsafeHtml\", booleanAttribute],\n        frozenLeft: [2, \"frozenLeft\", \"frozenLeft\", booleanAttribute],\n        frozenRight: [2, \"frozenRight\", \"frozenRight\", booleanAttribute],\n        flexGrow: [2, \"flexGrow\", \"flexGrow\", numberAttribute],\n        resizeable: [2, \"resizeable\", \"resizeable\", booleanAttribute],\n        comparator: \"comparator\",\n        pipe: \"pipe\",\n        sortable: [2, \"sortable\", \"sortable\", booleanAttribute],\n        draggable: [2, \"draggable\", \"draggable\", booleanAttribute],\n        canAutoResize: [2, \"canAutoResize\", \"canAutoResize\", booleanAttribute],\n        minWidth: [2, \"minWidth\", \"minWidth\", numberAttribute],\n        width: [2, \"width\", \"width\", numberAttribute],\n        maxWidth: [2, \"maxWidth\", \"maxWidth\", numberAttribute],\n        checkboxable: [2, \"checkboxable\", \"checkboxable\", booleanAttribute],\n        headerCheckboxable: [2, \"headerCheckboxable\", \"headerCheckboxable\", booleanAttribute],\n        headerClass: \"headerClass\",\n        cellClass: \"cellClass\",\n        isTreeColumn: [2, \"isTreeColumn\", \"isTreeColumn\", booleanAttribute],\n        treeLevelIndent: \"treeLevelIndent\",\n        summaryFunc: \"summaryFunc\",\n        summaryTemplate: \"summaryTemplate\",\n        _cellTemplateInput: [0, \"cellTemplate\", \"_cellTemplateInput\"],\n        _headerTemplateInput: [0, \"headerTemplate\", \"_headerTemplateInput\"],\n        _treeToggleTemplateInput: [0, \"treeToggleTemplate\", \"_treeToggleTemplateInput\"],\n        _ghostCellTemplateInput: [0, \"ghostCellTemplate\", \"_ghostCellTemplateInput\"]\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableColumnDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ngx-datatable-column'\n    }]\n  }], null, {\n    name: [{\n      type: Input\n    }],\n    prop: [{\n      type: Input\n    }],\n    bindAsUnsafeHtml: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    frozenLeft: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    frozenRight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    flexGrow: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    resizeable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    comparator: [{\n      type: Input\n    }],\n    pipe: [{\n      type: Input\n    }],\n    sortable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    draggable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    canAutoResize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    minWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    width: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    maxWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    checkboxable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    headerCheckboxable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    headerClass: [{\n      type: Input\n    }],\n    cellClass: [{\n      type: Input\n    }],\n    isTreeColumn: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    treeLevelIndent: [{\n      type: Input\n    }],\n    summaryFunc: [{\n      type: Input\n    }],\n    summaryTemplate: [{\n      type: Input\n    }],\n    _cellTemplateInput: [{\n      type: Input,\n      args: ['cellTemplate']\n    }],\n    _cellTemplateQuery: [{\n      type: ContentChild,\n      args: [DataTableColumnCellDirective, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _headerTemplateInput: [{\n      type: Input,\n      args: ['headerTemplate']\n    }],\n    _headerTemplateQuery: [{\n      type: ContentChild,\n      args: [DataTableColumnHeaderDirective, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _treeToggleTemplateInput: [{\n      type: Input,\n      args: ['treeToggleTemplate']\n    }],\n    _treeToggleTemplateQuery: [{\n      type: ContentChild,\n      args: [DataTableColumnCellTreeToggle, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _ghostCellTemplateInput: [{\n      type: Input,\n      args: ['ghostCellTemplate']\n    }],\n    _ghostCellTemplateQuery: [{\n      type: ContentChild,\n      args: [DataTableColumnGhostCellDirective, {\n        read: TemplateRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass DatatableRowDetailTemplateDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DatatableRowDetailTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableRowDetailTemplateDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableRowDetailTemplateDirective,\n      selectors: [[\"\", \"ngx-datatable-row-detail-template\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableRowDetailTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-row-detail-template]'\n    }]\n  }], null, null);\n})();\nclass DatatableRowDetailDirective {\n  constructor() {\n    /**\n     * The detail row height is required especially\n     * when virtual scroll is enabled.\n     */\n    this.rowHeight = 0;\n    /**\n     * Row detail row visbility was toggled.\n     */\n    this.toggle = new EventEmitter();\n  }\n  get template() {\n    return this._templateInput || this._templateQuery;\n  }\n  /**\n   * Toggle the expansion of the row\n   */\n  toggleExpandRow(row) {\n    this.toggle.emit({\n      type: 'row',\n      value: row\n    });\n  }\n  /**\n   * API method to expand all the rows.\n   */\n  expandAllRows() {\n    this.toggle.emit({\n      type: 'all',\n      value: true\n    });\n  }\n  /**\n   * API method to collapse all the rows.\n   */\n  collapseAllRows() {\n    this.toggle.emit({\n      type: 'all',\n      value: false\n    });\n  }\n  static {\n    this.ɵfac = function DatatableRowDetailDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableRowDetailDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableRowDetailDirective,\n      selectors: [[\"ngx-datatable-row-detail\"]],\n      contentQueries: function DatatableRowDetailDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DatatableRowDetailTemplateDirective, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n        }\n      },\n      inputs: {\n        rowHeight: \"rowHeight\",\n        _templateInput: [0, \"template\", \"_templateInput\"]\n      },\n      outputs: {\n        toggle: \"toggle\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableRowDetailDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ngx-datatable-row-detail'\n    }]\n  }], null, {\n    rowHeight: [{\n      type: Input\n    }],\n    _templateInput: [{\n      type: Input,\n      args: ['template']\n    }],\n    _templateQuery: [{\n      type: ContentChild,\n      args: [DatatableRowDetailTemplateDirective, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    toggle: [{\n      type: Output\n    }]\n  });\n})();\nclass DatatableFooterDirective {\n  get template() {\n    return this._templateInput || this._templateQuery;\n  }\n  static {\n    this.ɵfac = function DatatableFooterDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableFooterDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableFooterDirective,\n      selectors: [[\"ngx-datatable-footer\"]],\n      contentQueries: function DatatableFooterDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DataTableFooterTemplateDirective, 5, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n        }\n      },\n      inputs: {\n        _templateInput: [0, \"template\", \"_templateInput\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableFooterDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ngx-datatable-footer'\n    }]\n  }], null, {\n    _templateInput: [{\n      type: Input,\n      args: ['template']\n    }],\n    _templateQuery: [{\n      type: ContentChild,\n      args: [DataTableFooterTemplateDirective, {\n        read: TemplateRef\n      }]\n    }]\n  });\n})();\nclass ScrollerComponent {\n  constructor() {\n    this.renderer = inject(Renderer2);\n    this.scroll = new EventEmitter();\n    this.scrollYPos = 0;\n    this.scrollXPos = 0;\n    this.prevScrollYPos = 0;\n    this.prevScrollXPos = 0;\n    this.element = inject(ElementRef).nativeElement;\n    this._scrollEventListener = null;\n  }\n  ngOnInit() {\n    // manual bind so we don't always listen\n    if (this.scrollbarV || this.scrollbarH) {\n      const renderer = this.renderer;\n      this.parentElement = renderer.parentNode(this.element);\n      this._scrollEventListener = this.onScrolled.bind(this);\n      this.parentElement?.addEventListener('scroll', this._scrollEventListener);\n    }\n  }\n  ngOnDestroy() {\n    if (this._scrollEventListener) {\n      this.parentElement?.removeEventListener('scroll', this._scrollEventListener);\n      this._scrollEventListener = null;\n    }\n  }\n  setOffset(offsetY) {\n    if (this.parentElement) {\n      this.parentElement.scrollTop = offsetY;\n    }\n  }\n  onScrolled(event) {\n    const dom = event.currentTarget;\n    requestAnimationFrame(() => {\n      this.scrollYPos = dom.scrollTop;\n      this.scrollXPos = dom.scrollLeft;\n      this.updateOffset();\n    });\n  }\n  updateOffset() {\n    let direction;\n    if (this.scrollYPos < this.prevScrollYPos) {\n      direction = 'down';\n    } else {\n      direction = 'up';\n    }\n    this.scroll.emit({\n      direction,\n      scrollYPos: this.scrollYPos,\n      scrollXPos: this.scrollXPos\n    });\n    this.prevScrollYPos = this.scrollYPos;\n    this.prevScrollXPos = this.scrollXPos;\n  }\n  static {\n    this.ɵfac = function ScrollerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollerComponent,\n      selectors: [[\"datatable-scroller\"]],\n      hostAttrs: [1, \"datatable-scroll\"],\n      hostVars: 4,\n      hostBindings: function ScrollerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.scrollHeight, \"px\")(\"width\", ctx.scrollWidth, \"px\");\n        }\n      },\n      inputs: {\n        scrollbarV: \"scrollbarV\",\n        scrollbarH: \"scrollbarH\",\n        scrollHeight: \"scrollHeight\",\n        scrollWidth: \"scrollWidth\"\n      },\n      outputs: {\n        scroll: \"scroll\"\n      },\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function ScrollerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-scroller',\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'datatable-scroll'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    scrollbarV: [{\n      type: Input\n    }],\n    scrollbarH: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: HostBinding,\n      args: ['style.height.px']\n    }, {\n      type: Input\n    }],\n    scrollWidth: [{\n      type: HostBinding,\n      args: ['style.width.px']\n    }, {\n      type: Input\n    }],\n    scroll: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Returns the columns by pin.\n */\nfunction columnsByPin(cols) {\n  const ret = {\n    left: [],\n    center: [],\n    right: []\n  };\n  if (cols) {\n    for (const col of cols) {\n      if (col.frozenLeft) {\n        ret.left.push(col);\n      } else if (col.frozenRight) {\n        ret.right.push(col);\n      } else {\n        ret.center.push(col);\n      }\n    }\n  }\n  return ret;\n}\n/**\n * Returns the widths of all group sets of a column\n */\nfunction columnGroupWidths(groups, all) {\n  return {\n    left: columnTotalWidth(groups.left),\n    center: columnTotalWidth(groups.center),\n    right: columnTotalWidth(groups.right),\n    total: Math.floor(columnTotalWidth(all))\n  };\n}\n/**\n * Calculates the total width of all columns\n */\nfunction columnTotalWidth(columns) {\n  return columns?.reduce((total, column) => total + column.width, 0) ?? 0;\n}\nfunction columnsByPinArr(val) {\n  const colsByPin = columnsByPin(val);\n  return [{\n    type: 'left',\n    columns: colsByPin.left\n  }, {\n    type: 'center',\n    columns: colsByPin.center\n  }, {\n    type: 'right',\n    columns: colsByPin.right\n  }];\n}\n\n/**\n * This object contains the cache of the various row heights that are present inside\n * the data table.   Its based on Fenwick tree data structure that helps with\n * querying sums that have time complexity of log n.\n *\n * Fenwick Tree Credits: http://petr-mitrichev.blogspot.com/2013/05/fenwick-tree-range-updates.html\n * https://github.com/mikolalysenko/fenwick-tree\n *\n */\nclass RowHeightCache {\n  constructor() {\n    /**\n     * Tree Array stores the cumulative information of the row heights to perform efficient\n     * range queries and updates.  Currently the tree is initialized to the base row\n     * height instead of the detail row height.\n     */\n    this.treeArray = [];\n  }\n  /**\n   * Clear the Tree array.\n   */\n  clearCache() {\n    this.treeArray = [];\n  }\n  /**\n   * Initialize the Fenwick tree with row Heights.\n   *\n   * @param rows The array of rows which contain the expanded status.\n   * @param rowHeight The row height.\n   * @param detailRowHeight The detail row height.\n   */\n  initCache(details) {\n    const {\n      rows,\n      rowHeight,\n      detailRowHeight,\n      externalVirtual,\n      indexOffset,\n      rowCount,\n      rowExpansions\n    } = details;\n    const isFn = typeof rowHeight === 'function';\n    const isDetailFn = typeof detailRowHeight === 'function';\n    if (!isFn && isNaN(rowHeight)) {\n      throw new Error(`Row Height cache initialization failed. Please ensure that 'rowHeight' is a\n        valid number or function value: (${rowHeight}) when 'scrollbarV' is enabled.`);\n    }\n    // Add this additional guard in case detailRowHeight is set to 'auto' as it wont work.\n    if (!isDetailFn && isNaN(detailRowHeight)) {\n      throw new Error(`Row Height cache initialization failed. Please ensure that 'detailRowHeight' is a\n        valid number or function value: (${detailRowHeight}) when 'scrollbarV' is enabled.`);\n    }\n    const n = externalVirtual ? rowCount : rows.length;\n    this.treeArray = new Array(n);\n    for (let i = 0; i < n; ++i) {\n      this.treeArray[i] = 0;\n    }\n    for (let i = 0; i < n; ++i) {\n      const row = rows[i];\n      let currentRowHeight = rowHeight;\n      if (isFn) {\n        currentRowHeight = rowHeight(row);\n      }\n      // Add the detail row height to the already expanded rows.\n      // This is useful for the table that goes through a filter or sort.\n      const expanded = rowExpansions.has(row);\n      if (row && expanded) {\n        if (isDetailFn) {\n          const index = indexOffset + i;\n          currentRowHeight += detailRowHeight(row, index);\n        } else {\n          currentRowHeight += detailRowHeight;\n        }\n      }\n      this.update(i, currentRowHeight);\n    }\n  }\n  /**\n   * Given the ScrollY position i.e. sum, provide the rowIndex\n   * that is present in the current view port.  Below handles edge cases.\n   */\n  getRowIndex(scrollY) {\n    if (scrollY === 0) {\n      return 0;\n    }\n    return this.calcRowIndex(scrollY);\n  }\n  /**\n   * When a row is expanded or rowHeight is changed, update the height.  This can\n   * be utilized in future when Angular Data table supports dynamic row heights.\n   */\n  update(atRowIndex, byRowHeight) {\n    if (!this.treeArray.length) {\n      throw new Error(`Update at index ${atRowIndex} with value ${byRowHeight} failed:\n        Row Height cache not initialized.`);\n    }\n    const n = this.treeArray.length;\n    atRowIndex |= 0;\n    while (atRowIndex < n) {\n      this.treeArray[atRowIndex] += byRowHeight;\n      atRowIndex |= atRowIndex + 1;\n    }\n  }\n  /**\n   * Range Sum query from 1 to the rowIndex\n   */\n  query(atIndex) {\n    if (!this.treeArray.length) {\n      throw new Error(`query at index ${atIndex} failed: Fenwick tree array not initialized.`);\n    }\n    let sum = 0;\n    atIndex |= 0;\n    while (atIndex >= 0) {\n      sum += this.treeArray[atIndex];\n      atIndex = (atIndex & atIndex + 1) - 1;\n    }\n    return sum;\n  }\n  /**\n   * Find the total height between 2 row indexes\n   */\n  queryBetween(atIndexA, atIndexB) {\n    return this.query(atIndexB) - this.query(atIndexA - 1);\n  }\n  /**\n   * Given the ScrollY position i.e. sum, provide the rowIndex\n   * that is present in the current view port.\n   */\n  calcRowIndex(sum) {\n    if (!this.treeArray.length) {\n      return 0;\n    }\n    let pos = -1;\n    const dataLength = this.treeArray.length;\n    // Get the highest bit for the block size.\n    const highestBit = Math.pow(2, dataLength.toString(2).length - 1);\n    for (let blockSize = highestBit; blockSize !== 0; blockSize >>= 1) {\n      const nextPos = pos + blockSize;\n      if (nextPos < dataLength && sum >= this.treeArray[nextPos]) {\n        sum -= this.treeArray[nextPos];\n        pos = nextPos;\n      }\n    }\n    return pos + 1;\n  }\n}\nvar Keys;\n(function (Keys) {\n  Keys[\"up\"] = \"ArrowUp\";\n  Keys[\"down\"] = \"ArrowDown\";\n  Keys[\"return\"] = \"Enter\";\n  Keys[\"escape\"] = \"Escape\";\n  Keys[\"left\"] = \"ArrowLeft\";\n  Keys[\"right\"] = \"ArrowRight\";\n})(Keys || (Keys = {}));\nvar SortDirection;\n(function (SortDirection) {\n  SortDirection[\"asc\"] = \"asc\";\n  SortDirection[\"desc\"] = \"desc\";\n})(SortDirection || (SortDirection = {}));\nvar SortType;\n(function (SortType) {\n  SortType[\"single\"] = \"single\";\n  SortType[\"multi\"] = \"multi\";\n})(SortType || (SortType = {}));\nvar ColumnMode;\n(function (ColumnMode) {\n  ColumnMode[\"standard\"] = \"standard\";\n  ColumnMode[\"flex\"] = \"flex\";\n  ColumnMode[\"force\"] = \"force\";\n})(ColumnMode || (ColumnMode = {}));\nvar ContextmenuType;\n(function (ContextmenuType) {\n  ContextmenuType[\"header\"] = \"header\";\n  ContextmenuType[\"body\"] = \"body\";\n})(ContextmenuType || (ContextmenuType = {}));\nvar SelectionType;\n(function (SelectionType) {\n  SelectionType[\"single\"] = \"single\";\n  SelectionType[\"multi\"] = \"multi\";\n  SelectionType[\"multiClick\"] = \"multiClick\";\n  SelectionType[\"cell\"] = \"cell\";\n  SelectionType[\"checkbox\"] = \"checkbox\";\n})(SelectionType || (SelectionType = {}));\nclass DataTableBodyCellComponent {\n  set disabled(value) {\n    this.cellContext.disabled = value;\n    this._disabled = value;\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set group(group) {\n    this._group = group;\n    this.cellContext.group = group;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get group() {\n    return this._group;\n  }\n  set rowHeight(val) {\n    this._rowHeight = val;\n    this.cellContext.rowHeight = val;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get rowHeight() {\n    return this._rowHeight;\n  }\n  set isSelected(val) {\n    this._isSelected = val;\n    this.cellContext.isSelected = val;\n    this.cd.markForCheck();\n  }\n  get isSelected() {\n    return this._isSelected;\n  }\n  set expanded(val) {\n    this._expanded = val;\n    this.cellContext.expanded = val;\n    this.cd.markForCheck();\n  }\n  get expanded() {\n    return this._expanded;\n  }\n  set rowIndex(val) {\n    this._rowIndex = val;\n    this.cellContext.rowIndex = val?.index;\n    this.cellContext.rowInGroupIndex = val?.indexInGroup;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get rowIndex() {\n    return this._rowIndex;\n  }\n  set column(column) {\n    this._column = column;\n    this.cellContext.column = column;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get column() {\n    return this._column;\n  }\n  set row(row) {\n    this._row = row;\n    this.cellContext.row = row;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get row() {\n    return this._row;\n  }\n  set sorts(val) {\n    this._sorts = val;\n    this.sortDir = this.calcSortDir(val);\n  }\n  get sorts() {\n    return this._sorts;\n  }\n  set treeStatus(status) {\n    if (status !== 'collapsed' && status !== 'expanded' && status !== 'loading' && status !== 'disabled') {\n      this._treeStatus = 'collapsed';\n    } else {\n      this._treeStatus = status;\n    }\n    this.cellContext.treeStatus = this._treeStatus;\n    this.checkValueUpdates();\n    this.cd.markForCheck();\n  }\n  get treeStatus() {\n    return this._treeStatus;\n  }\n  get columnCssClasses() {\n    let cls = 'datatable-body-cell';\n    if (this.column.cellClass) {\n      if (typeof this.column.cellClass === 'string') {\n        cls += ' ' + this.column.cellClass;\n      } else if (typeof this.column.cellClass === 'function') {\n        const res = this.column.cellClass({\n          row: this.row,\n          group: this.group,\n          column: this.column,\n          value: this.value,\n          rowHeight: this.rowHeight\n        });\n        if (typeof res === 'string') {\n          cls += ' ' + res;\n        } else if (typeof res === 'object') {\n          const keys = Object.keys(res);\n          for (const k of keys) {\n            if (res[k] === true) {\n              cls += ` ${k}`;\n            }\n          }\n        }\n      }\n    }\n    if (!this.sortDir) {\n      cls += ' sort-active';\n    }\n    if (this.isFocused && !this._disabled) {\n      cls += ' active';\n    }\n    if (this.sortDir === SortDirection.asc) {\n      cls += ' sort-asc';\n    }\n    if (this.sortDir === SortDirection.desc) {\n      cls += ' sort-desc';\n    }\n    if (this._disabled) {\n      cls += ' row-disabled';\n    }\n    return cls;\n  }\n  get width() {\n    return this.column.width;\n  }\n  get minWidth() {\n    return this.column.minWidth;\n  }\n  get maxWidth() {\n    return this.column.maxWidth;\n  }\n  get height() {\n    const height = this.rowHeight;\n    if (isNaN(height)) {\n      return height;\n    }\n    return height + 'px';\n  }\n  constructor() {\n    this.cd = inject(ChangeDetectorRef);\n    this.activate = new EventEmitter();\n    this.treeAction = new EventEmitter();\n    this.isFocused = false;\n    this._element = inject(ElementRef).nativeElement;\n    this.cellContext = {\n      onCheckboxChangeFn: event => this.onCheckboxChange(event),\n      activateFn: event => this.activate.emit(event),\n      row: this.row,\n      group: this.group,\n      value: this.value,\n      column: this.column,\n      rowHeight: this.rowHeight,\n      isSelected: this.isSelected,\n      rowIndex: this.rowIndex?.index,\n      rowInGroupIndex: this.rowIndex?.indexInGroup,\n      treeStatus: this.treeStatus,\n      disabled: this._disabled,\n      onTreeAction: () => this.onTreeAction()\n    };\n  }\n  ngDoCheck() {\n    this.checkValueUpdates();\n  }\n  checkValueUpdates() {\n    let value = '';\n    if (!this.row || !this.column || this.column.prop == undefined) {\n      value = '';\n    } else {\n      const val = this.column.$$valueGetter(this.row, this.column.prop);\n      const userPipe = this.column.pipe;\n      if (userPipe) {\n        value = userPipe.transform(val);\n      } else if (value !== undefined) {\n        value = val;\n      }\n    }\n    if (this.value !== value) {\n      this.value = value;\n      this.cellContext.value = value;\n      this.cellContext.disabled = this._disabled;\n      this.sanitizedValue = value !== null && value !== undefined ? this.stripHtml(value) : value;\n      this.cd.markForCheck();\n    }\n  }\n  onFocus() {\n    this.isFocused = true;\n  }\n  onBlur() {\n    this.isFocused = false;\n  }\n  onClick(event) {\n    this.activate.emit({\n      type: 'click',\n      event,\n      row: this.row,\n      group: this.group,\n      rowHeight: this.rowHeight,\n      column: this.column,\n      value: this.value,\n      cellElement: this._element\n    });\n  }\n  onDblClick(event) {\n    this.activate.emit({\n      type: 'dblclick',\n      event,\n      row: this.row,\n      group: this.group,\n      rowHeight: this.rowHeight,\n      column: this.column,\n      value: this.value,\n      cellElement: this._element\n    });\n  }\n  onKeyDown(event) {\n    const key = event.key;\n    const isTargetCell = event.target === this._element;\n    const isAction = key === Keys.return || key === Keys.down || key === Keys.up || key === Keys.left || key === Keys.right;\n    if (isAction && isTargetCell) {\n      event.preventDefault();\n      event.stopPropagation();\n      this.activate.emit({\n        type: 'keydown',\n        event,\n        row: this.row,\n        group: this.group,\n        rowHeight: this.rowHeight,\n        column: this.column,\n        value: this.value,\n        cellElement: this._element\n      });\n    }\n  }\n  onCheckboxChange(event) {\n    this.activate.emit({\n      type: 'checkbox',\n      event,\n      row: this.row,\n      group: this.group,\n      rowHeight: this.rowHeight,\n      column: this.column,\n      value: this.value,\n      cellElement: this._element,\n      treeStatus: 'collapsed'\n    });\n  }\n  calcSortDir(sorts) {\n    if (!sorts) {\n      return undefined;\n    }\n    const sort = sorts.find(s => s.prop === this.column.prop);\n    return sort?.dir;\n  }\n  stripHtml(html) {\n    if (!html.replace) {\n      return html;\n    }\n    return html.replace(/<\\/?[^>]+(>|$)/g, '');\n  }\n  onTreeAction() {\n    this.treeAction.emit(this.row);\n  }\n  calcLeftMargin(column, row) {\n    const levelIndent = column.treeLevelIndent != null ? column.treeLevelIndent : 50;\n    return column.isTreeColumn ? row.level * levelIndent : 0;\n  }\n  static {\n    this.ɵfac = function DataTableBodyCellComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableBodyCellComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableBodyCellComponent,\n      selectors: [[\"datatable-body-cell\"]],\n      hostVars: 10,\n      hostBindings: function DataTableBodyCellComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function DataTableBodyCellComponent_focus_HostBindingHandler() {\n            return ctx.onFocus();\n          })(\"blur\", function DataTableBodyCellComponent_blur_HostBindingHandler() {\n            return ctx.onBlur();\n          })(\"click\", function DataTableBodyCellComponent_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"dblclick\", function DataTableBodyCellComponent_dblclick_HostBindingHandler($event) {\n            return ctx.onDblClick($event);\n          })(\"keydown\", function DataTableBodyCellComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.columnCssClasses);\n          i0.ɵɵstyleProp(\"width\", ctx.width, \"px\")(\"min-width\", ctx.minWidth, \"px\")(\"max-width\", ctx.maxWidth, \"px\")(\"height\", ctx.height);\n        }\n      },\n      inputs: {\n        displayCheck: \"displayCheck\",\n        disabled: \"disabled\",\n        group: \"group\",\n        rowHeight: \"rowHeight\",\n        isSelected: \"isSelected\",\n        expanded: \"expanded\",\n        rowIndex: \"rowIndex\",\n        column: \"column\",\n        row: \"row\",\n        sorts: \"sorts\",\n        treeStatus: \"treeStatus\"\n      },\n      outputs: {\n        activate: \"activate\",\n        treeAction: \"treeAction\"\n      },\n      decls: 5,\n      vars: 5,\n      consts: [[1, \"datatable-body-cell-label\"], [1, \"datatable-checkbox\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"checkbox\", 3, \"click\", \"disabled\", \"checked\"], [1, \"datatable-tree-button\", 3, \"disabled\"], [1, \"datatable-tree-button\", 3, \"click\", \"disabled\"], [1, \"icon\", \"datatable-icon-collapse\"], [1, \"icon\", \"datatable-icon-up\"], [1, \"icon\", \"datatable-icon-down\"], [3, \"title\", \"innerHTML\"], [3, \"title\"]],\n      template: function DataTableBodyCellComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵconditionalCreate(1, DataTableBodyCellComponent_Conditional_1_Template, 2, 2, \"label\", 1);\n          i0.ɵɵconditionalCreate(2, DataTableBodyCellComponent_Conditional_2_Template, 2, 1);\n          i0.ɵɵconditionalCreate(3, DataTableBodyCellComponent_Conditional_3_Template, 2, 1)(4, DataTableBodyCellComponent_Conditional_4_Template, 1, 2, null, 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"margin-left\", ctx.calcLeftMargin(ctx.column, ctx.row), \"px\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.column.checkboxable && (!ctx.displayCheck || ctx.displayCheck(ctx.row, ctx.column, ctx.value)) ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.column.isTreeColumn ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.column.cellTemplate ? 3 : 4);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      styles: [\"[_nghost-%COMP%]{overflow-x:hidden;vertical-align:top;display:inline-block;line-height:1.625}[_nghost-%COMP%]:focus{outline:none}ngx-datatable.fixed-row   [_nghost-%COMP%]{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableBodyCellComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-body-cell',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"datatable-body-cell-label\" [style.margin-left.px]=\"calcLeftMargin(column, row)\">\n      @if (column.checkboxable && (!displayCheck || displayCheck(row, column, value))) {\n      <label class=\"datatable-checkbox\">\n        <input\n          type=\"checkbox\"\n          [disabled]=\"disabled\"\n          [checked]=\"isSelected\"\n          (click)=\"onCheckboxChange($event)\"\n        />\n      </label>\n      } @if (column.isTreeColumn) { @if (!column.treeToggleTemplate) {\n      <button\n        class=\"datatable-tree-button\"\n        [disabled]=\"treeStatus === 'disabled'\"\n        (click)=\"onTreeAction()\"\n        [attr.aria-label]=\"treeStatus\"\n      >\n        <span>\n          @if (treeStatus === 'loading') {\n          <i class=\"icon datatable-icon-collapse\"></i>\n          } @if (treeStatus === 'collapsed') {\n          <i class=\"icon datatable-icon-up\"></i>\n          } @if (treeStatus === 'expanded' || treeStatus === 'disabled') {\n          <i class=\"icon datatable-icon-down\"></i>\n          }\n        </span>\n      </button>\n      } @else {\n      <ng-template\n        [ngTemplateOutlet]=\"column.treeToggleTemplate\"\n        [ngTemplateOutletContext]=\"{ cellContext: cellContext }\"\n      >\n      </ng-template>\n      } } @if (!column.cellTemplate) { @if (column.bindAsUnsafeHtml) {\n      <span [title]=\"sanitizedValue\" [innerHTML]=\"value\"> </span>\n      } @else {\n      <span [title]=\"sanitizedValue\">{{ value }}</span>\n      } } @else {\n      <ng-template [ngTemplateOutlet]=\"column.cellTemplate\" [ngTemplateOutletContext]=\"cellContext\">\n      </ng-template>\n      }\n    </div>\n  `,\n      imports: [NgTemplateOutlet],\n      styles: [\":host{overflow-x:hidden;vertical-align:top;display:inline-block;line-height:1.625}:host:focus{outline:none}:host-context(ngx-datatable.fixed-row) :host{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}\\n\"]\n    }]\n  }], () => [], {\n    displayCheck: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    group: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    isSelected: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    rowIndex: [{\n      type: Input\n    }],\n    column: [{\n      type: Input\n    }],\n    row: [{\n      type: Input\n    }],\n    sorts: [{\n      type: Input\n    }],\n    treeStatus: [{\n      type: Input\n    }],\n    activate: [{\n      type: Output\n    }],\n    treeAction: [{\n      type: Output\n    }],\n    columnCssClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    width: [{\n      type: HostBinding,\n      args: ['style.width.px']\n    }],\n    minWidth: [{\n      type: HostBinding,\n      args: ['style.minWidth.px']\n    }],\n    maxWidth: [{\n      type: HostBinding,\n      args: ['style.maxWidth.px']\n    }],\n    height: [{\n      type: HostBinding,\n      args: ['style.height']\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus']\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onDblClick: [{\n      type: HostListener,\n      args: ['dblclick', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass DataTableBodyRowComponent {\n  constructor() {\n    this.cd = inject(ChangeDetectorRef);\n    this.treeStatus = 'collapsed';\n    this.verticalScrollVisible = false;\n    this.activate = new EventEmitter();\n    this.treeAction = new EventEmitter();\n    this._element = inject(ElementRef).nativeElement;\n    this._rowDiffer = inject(KeyValueDiffers).find({}).create();\n  }\n  set columns(val) {\n    this._columns = val;\n    this.recalculateColumns(val);\n  }\n  get columns() {\n    return this._columns;\n  }\n  set innerWidth(val) {\n    if (this._columns) {\n      const colByPin = columnsByPin(this._columns);\n      this._columnGroupWidths = columnGroupWidths(colByPin, this._columns);\n    }\n    this._innerWidth = val;\n    this.recalculateColumns();\n  }\n  get innerWidth() {\n    return this._innerWidth;\n  }\n  get cssClass() {\n    let cls = 'datatable-body-row';\n    if (this.isSelected) {\n      cls += ' active';\n    }\n    if (this.innerRowIndex % 2 !== 0) {\n      cls += ' datatable-row-odd';\n    }\n    if (this.innerRowIndex % 2 === 0) {\n      cls += ' datatable-row-even';\n    }\n    if (this.disabled) {\n      cls += ' row-disabled';\n    }\n    if (this.rowClass) {\n      const res = this.rowClass(this.row);\n      if (typeof res === 'string') {\n        cls += ` ${res}`;\n      } else if (typeof res === 'object') {\n        const keys = Object.keys(res);\n        for (const k of keys) {\n          if (res[k] === true) {\n            cls += ` ${k}`;\n          }\n        }\n      }\n    }\n    return cls;\n  }\n  get columnsTotalWidths() {\n    return this._columnGroupWidths.total;\n  }\n  ngOnChanges(changes) {\n    if (changes.verticalScrollVisible) {\n      this.recalculateColumns();\n    }\n  }\n  ngDoCheck() {\n    if (this._rowDiffer.diff(this.row)) {\n      this.cd.markForCheck();\n    }\n  }\n  onActivate(event, index) {\n    this.activate.emit({\n      ...event,\n      rowElement: this._element,\n      cellIndex: index\n    });\n  }\n  onKeyDown(event) {\n    const key = event.key;\n    const isTargetRow = event.target === this._element;\n    const isAction = key === Keys.return || key === Keys.down || key === Keys.up || key === Keys.left || key === Keys.right;\n    const isCtrlA = event.key === 'a' && (event.ctrlKey || event.metaKey);\n    if (isAction && isTargetRow || isCtrlA) {\n      event.preventDefault();\n      event.stopPropagation();\n      this.activate.emit({\n        type: 'keydown',\n        event,\n        row: this.row,\n        rowElement: this._element\n      });\n    }\n  }\n  onMouseenter(event) {\n    this.activate.emit({\n      type: 'mouseenter',\n      event,\n      row: this.row,\n      rowElement: this._element\n    });\n  }\n  recalculateColumns(val = this.columns) {\n    this._columns = val;\n    const colsByPin = columnsByPin(this._columns);\n    this._columnsByPin = columnsByPinArr(this._columns);\n    this._columnGroupWidths = columnGroupWidths(colsByPin, this._columns);\n  }\n  onTreeAction() {\n    this.treeAction.emit();\n  }\n  /** Returns the row index, or if in a group, the index within a group. */\n  get innerRowIndex() {\n    return this.rowIndex?.indexInGroup ?? this.rowIndex?.index ?? 0;\n  }\n  static {\n    this.ɵfac = function DataTableBodyRowComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableBodyRowComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableBodyRowComponent,\n      selectors: [[\"datatable-body-row\"]],\n      hostVars: 6,\n      hostBindings: function DataTableBodyRowComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function DataTableBodyRowComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          })(\"mouseenter\", function DataTableBodyRowComponent_mouseenter_HostBindingHandler($event) {\n            return ctx.onMouseenter($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.cssClass);\n          i0.ɵɵstyleProp(\"height\", ctx.rowHeight, \"px\")(\"width\", ctx.columnsTotalWidths, \"px\");\n        }\n      },\n      inputs: {\n        columns: \"columns\",\n        innerWidth: \"innerWidth\",\n        expanded: \"expanded\",\n        rowClass: \"rowClass\",\n        row: \"row\",\n        group: \"group\",\n        isSelected: \"isSelected\",\n        rowIndex: \"rowIndex\",\n        displayCheck: \"displayCheck\",\n        treeStatus: \"treeStatus\",\n        verticalScrollVisible: \"verticalScrollVisible\",\n        disabled: \"disabled\",\n        rowHeight: \"rowHeight\"\n      },\n      outputs: {\n        activate: \"activate\",\n        treeAction: \"treeAction\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[3, \"class\", \"width\", \"row-disabled\"], [\"role\", \"cell\", \"tabindex\", \"-1\", 3, \"row\", \"group\", \"expanded\", \"isSelected\", \"rowIndex\", \"column\", \"rowHeight\", \"displayCheck\", \"disabled\", \"treeStatus\"], [\"role\", \"cell\", \"tabindex\", \"-1\", 3, \"activate\", \"treeAction\", \"row\", \"group\", \"expanded\", \"isSelected\", \"rowIndex\", \"column\", \"rowHeight\", \"displayCheck\", \"disabled\", \"treeStatus\"]],\n      template: function DataTableBodyRowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, DataTableBodyRowComponent_For_1_Template, 1, 1, null, null, _forTrack0);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx._columnsByPin);\n        }\n      },\n      dependencies: [DataTableBodyCellComponent],\n      styles: [\"[_nghost-%COMP%]{display:flex;outline:none}ngx-datatable.fixed-row   [_nghost-%COMP%]{white-space:nowrap}.datatable-row-group[_ngcontent-%COMP%]{display:flex;position:relative}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableBodyRowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-body-row',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @for (colGroup of _columnsByPin; track colGroup.type) { @if (colGroup.columns.length) {\n    <div\n      class=\"datatable-row-{{ colGroup.type }} datatable-row-group\"\n      [style.width.px]=\"_columnGroupWidths[colGroup.type]\"\n      [class.row-disabled]=\"disabled\"\n    >\n      @for (column of colGroup.columns; track column.$$id; let ii = $index) {\n      <datatable-body-cell\n        role=\"cell\"\n        tabindex=\"-1\"\n        [row]=\"row\"\n        [group]=\"group\"\n        [expanded]=\"expanded\"\n        [isSelected]=\"isSelected\"\n        [rowIndex]=\"rowIndex\"\n        [column]=\"column\"\n        [rowHeight]=\"rowHeight\"\n        [displayCheck]=\"displayCheck\"\n        [disabled]=\"disabled\"\n        [treeStatus]=\"treeStatus\"\n        (activate)=\"onActivate($event, ii)\"\n        (treeAction)=\"onTreeAction()\"\n      >\n      </datatable-body-cell>\n      }\n    </div>\n    } }\n  `,\n      imports: [DataTableBodyCellComponent],\n      styles: [\":host{display:flex;outline:none}:host-context(ngx-datatable.fixed-row) :host{white-space:nowrap}.datatable-row-group{display:flex;position:relative}\\n\"]\n    }]\n  }], null, {\n    columns: [{\n      type: Input\n    }],\n    innerWidth: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    rowClass: [{\n      type: Input\n    }],\n    row: [{\n      type: Input\n    }],\n    group: [{\n      type: Input\n    }],\n    isSelected: [{\n      type: Input\n    }],\n    rowIndex: [{\n      type: Input\n    }],\n    displayCheck: [{\n      type: Input\n    }],\n    treeStatus: [{\n      type: Input\n    }],\n    verticalScrollVisible: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    cssClass: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    rowHeight: [{\n      type: HostBinding,\n      args: ['style.height.px']\n    }, {\n      type: Input\n    }],\n    columnsTotalWidths: [{\n      type: HostBinding,\n      args: ['style.width.px']\n    }],\n    activate: [{\n      type: Output\n    }],\n    treeAction: [{\n      type: Output\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }],\n    onMouseenter: [{\n      type: HostListener,\n      args: ['mouseenter', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Extracts the position (x, y coordinates) from a MouseEvent or TouchEvent.\n *\n * @param {MouseEvent | TouchEvent} event - The event object from which to extract the position. Can be either a MouseEvent or a TouchEvent.\n * @return {{ x: number, y: number }} An object containing the x and y coordinates of the event relative to the viewport.\n */\nfunction getPositionFromEvent(event) {\n  return event instanceof MouseEvent ? event : event.changedTouches[0];\n}\n\n/**\n * Draggable Directive for Angular2\n *\n * Inspiration:\n *   https://github.com/AngularClass/angular2-examples/blob/master/rx-draggable/directives/draggable.ts\n *   http://stackoverflow.com/questions/35662530/how-to-implement-drag-and-drop-in-angular2\n *\n */\nclass DraggableDirective {\n  constructor() {\n    this.dragX = true;\n    this.dragY = true;\n    this.dragStart = new EventEmitter();\n    this.dragging = new EventEmitter();\n    this.dragEnd = new EventEmitter();\n    this.element = inject(ElementRef).nativeElement;\n    this.isDragging = false;\n  }\n  ngOnChanges(changes) {\n    if (changes.dragEventTarget && changes.dragEventTarget.currentValue && this.dragModel.dragging) {\n      this.onMousedown(changes.dragEventTarget.currentValue);\n    }\n  }\n  ngOnDestroy() {\n    this._destroySubscription();\n  }\n  onMouseup(event) {\n    if (!this.isDragging) {\n      return;\n    }\n    this.isDragging = false;\n    this.element.classList.remove('dragging');\n    if (this.subscription) {\n      this._destroySubscription();\n      this.dragEnd.emit({\n        event,\n        element: this.element,\n        model: this.dragModel\n      });\n    }\n  }\n  onMousedown(event) {\n    const isMouse = event instanceof MouseEvent;\n    // we only want to drag the inner header text\n    const isDragElm = event.target.classList.contains('draggable');\n    if (isDragElm && (this.dragX || this.dragY)) {\n      event.preventDefault();\n      this.isDragging = true;\n      const mouseDownPos = getPositionFromEvent(event);\n      const mouseup = fromEvent(document, isMouse ? 'mouseup' : 'touchend');\n      this.subscription = mouseup.subscribe(ev => this.onMouseup(ev));\n      const mouseMoveSub = fromEvent(document, isMouse ? 'mousemove' : 'touchmove').pipe(takeUntil(mouseup)).subscribe(ev => this.move(ev, mouseDownPos));\n      this.subscription.add(mouseMoveSub);\n      this.dragStart.emit({\n        event,\n        element: this.element,\n        model: this.dragModel\n      });\n    }\n  }\n  move(event, mouseDownPos) {\n    if (!this.isDragging) {\n      return;\n    }\n    const {\n      clientX,\n      clientY\n    } = getPositionFromEvent(event);\n    const x = clientX - mouseDownPos.clientX;\n    const y = clientY - mouseDownPos.clientY;\n    if (this.dragX) {\n      this.element.style.left = `${x}px`;\n    }\n    if (this.dragY) {\n      this.element.style.top = `${y}px`;\n    }\n    this.element.classList.add('dragging');\n    this.dragging.emit({\n      event,\n      element: this.element,\n      model: this.dragModel\n    });\n  }\n  _destroySubscription() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      this.subscription = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function DraggableDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DraggableDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DraggableDirective,\n      selectors: [[\"\", \"draggable\", \"\"]],\n      inputs: {\n        dragEventTarget: \"dragEventTarget\",\n        dragModel: \"dragModel\",\n        dragX: [2, \"dragX\", \"dragX\", booleanAttribute],\n        dragY: [2, \"dragY\", \"dragY\", booleanAttribute]\n      },\n      outputs: {\n        dragStart: \"dragStart\",\n        dragging: \"dragging\",\n        dragEnd: \"dragEnd\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[draggable]'\n    }]\n  }], null, {\n    dragEventTarget: [{\n      type: Input\n    }],\n    dragModel: [{\n      type: Input\n    }],\n    dragX: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dragY: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    dragging: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * This component is passed as ng-template and rendered by BodyComponent.\n * BodyComponent uses rowDefInternal to first inject actual row template.\n * This component will render that actual row template.\n */\nclass DatatableRowDefComponent {\n  constructor() {\n    this.rowDef = inject(RowDefToken);\n    this.rowContext = {\n      ...this.rowDef.rowDefInternal,\n      disabled: this.rowDef.rowDefInternalDisabled\n    };\n  }\n  static {\n    this.ɵfac = function DatatableRowDefComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableRowDefComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DatatableRowDefComponent,\n      selectors: [[\"datatable-row-def\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function DatatableRowDefComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵconditionalCreate(0, DatatableRowDefComponent_Conditional_0_Template, 1, 2, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.rowDef.rowDefInternal.rowTemplate ? 0 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableRowDefComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-row-def',\n      template: `@if (rowDef.rowDefInternal.rowTemplate) {\n    <ng-container\n      [ngTemplateOutlet]=\"rowDef.rowDefInternal.rowTemplate\"\n      [ngTemplateOutletContext]=\"rowContext\"\n    />\n  }`,\n      imports: [NgTemplateOutlet]\n    }]\n  }], null, null);\n})();\nclass DatatableRowDefDirective {\n  static ngTemplateContextGuard(_dir, ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DatatableRowDefDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableRowDefDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableRowDefDirective,\n      selectors: [[\"\", \"rowDef\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableRowDefDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[rowDef]'\n    }]\n  }], null, null);\n})();\n/**\n * @internal To be used internally by ngx-datatable.\n */\nclass DatatableRowDefInternalDirective {\n  constructor() {\n    this.vc = inject(ViewContainerRef);\n  }\n  ngOnInit() {\n    this.vc.createEmbeddedView(this.rowDefInternal.template, {\n      ...this.rowDefInternal\n    }, {\n      injector: Injector.create({\n        providers: [{\n          provide: RowDefToken,\n          useValue: this\n        }]\n      })\n    });\n  }\n  static {\n    this.ɵfac = function DatatableRowDefInternalDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableRowDefInternalDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableRowDefInternalDirective,\n      selectors: [[\"\", \"rowDefInternal\", \"\"]],\n      inputs: {\n        rowDefInternal: \"rowDefInternal\",\n        rowDefInternalDisabled: \"rowDefInternalDisabled\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableRowDefInternalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[rowDefInternal]'\n    }]\n  }], null, {\n    rowDefInternal: [{\n      type: Input\n    }],\n    rowDefInternalDisabled: [{\n      type: Input\n    }]\n  });\n})();\nconst RowDefToken = new InjectionToken('RowDef');\n\n/**\n * This token is created to break cycling import error which occurs when we import\n * DatatableComponent in DataTableRowWrapperComponent.\n */\nconst DatatableComponentToken = new InjectionToken('DatatableComponentToken');\nclass DataTableRowWrapperComponent {\n  constructor() {\n    this.rowContextmenu = new EventEmitter(false);\n    this.selectedGroupRows = signal([]);\n    this.expanded = false;\n    this.rowDiffer = inject(KeyValueDiffers).find({}).create();\n    this.iterableDiffers = inject(IterableDiffers);\n    this.tableComponent = inject(DatatableComponentToken);\n    this.cd = inject(ChangeDetectorRef);\n  }\n  ngOnInit() {\n    this.selectedRowsDiffer = this.iterableDiffers.find(this.selected ?? []).create();\n  }\n  ngOnChanges(changes) {\n    if (changes['row']) {\n      // this component renders either a group header or a row. Never both.\n      if (this.isGroup(this.row)) {\n        this.context = {\n          group: this.row,\n          expanded: this.expanded,\n          rowIndex: this.rowIndex\n        };\n      } else {\n        this.context = {\n          row: this.row,\n          expanded: this.expanded,\n          rowIndex: this.rowIndex,\n          disabled: this.disabled\n        };\n      }\n    }\n    if (changes['rowIndex']) {\n      this.context.rowIndex = this.rowIndex;\n    }\n    if (changes['expanded']) {\n      this.context.expanded = this.expanded;\n    }\n  }\n  ngDoCheck() {\n    if (this.rowDiffer.diff(this.row)) {\n      if ('group' in this.context) {\n        this.context.group = this.row;\n      } else {\n        this.context.row = this.row;\n      }\n      this.cd.markForCheck();\n    }\n    // When groupheader is used with chechbox we use iterableDiffer\n    // on currently selected rows to check if it is modified\n    // if any of the row of this group is not present in `selected` rows array\n    // mark group header checkbox state as indeterminate\n    if (this.isGroup(this.row) && this.groupHeader?.checkboxable && this.selectedRowsDiffer.diff(this.selected)) {\n      const thisRow = this.row;\n      const selectedRows = this.selected.filter(row => thisRow.value.find(item => item === row));\n      if (this.checkBoxInput) {\n        if (selectedRows.length && selectedRows.length !== this.row.value.length) {\n          this.checkBoxInput.nativeElement.indeterminate = true;\n        } else {\n          this.checkBoxInput.nativeElement.indeterminate = false;\n        }\n      }\n      this.selectedGroupRows.set(selectedRows);\n    }\n  }\n  onContextmenu($event) {\n    this.rowContextmenu.emit({\n      event: $event,\n      row: this.row\n    });\n  }\n  onCheckboxChange(groupSelected, group) {\n    // First remove all rows of this group from `selected`\n    this.selected = [...this.selected.filter(row => !group.value.find(item => item === row))];\n    // If checkbox is checked then add all rows of this group in `selected`\n    if (groupSelected) {\n      this.selected = [...this.selected, ...group.value];\n    }\n    // Update `selected` of DatatableComponent with newly evaluated `selected`\n    this.tableComponent.selected = [...this.selected];\n    // Emit select event with updated values\n    this.tableComponent.onBodySelect({\n      selected: this.selected\n    });\n  }\n  isGroup(row) {\n    return !!this.groupHeader;\n  }\n  static {\n    this.ɵfac = function DataTableRowWrapperComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableRowWrapperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableRowWrapperComponent,\n      selectors: [[\"datatable-row-wrapper\"]],\n      viewQuery: function DataTableRowWrapperComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkBoxInput = _t.first);\n        }\n      },\n      hostAttrs: [1, \"datatable-row-wrapper\"],\n      hostBindings: function DataTableRowWrapperComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"contextmenu\", function DataTableRowWrapperComponent_contextmenu_HostBindingHandler($event) {\n            return ctx.onContextmenu($event);\n          });\n        }\n      },\n      inputs: {\n        innerWidth: \"innerWidth\",\n        rowDetail: \"rowDetail\",\n        groupHeader: \"groupHeader\",\n        offsetX: \"offsetX\",\n        detailRowHeight: \"detailRowHeight\",\n        groupHeaderRowHeight: \"groupHeaderRowHeight\",\n        row: \"row\",\n        groupedRows: \"groupedRows\",\n        selected: \"selected\",\n        disabled: \"disabled\",\n        rowIndex: \"rowIndex\",\n        expanded: [2, \"expanded\", \"expanded\", booleanAttribute]\n      },\n      outputs: {\n        rowContextmenu: \"rowContextmenu\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 3,\n      consts: [[\"select\", \"\"], [1, \"datatable-group-header\", 3, \"height\", \"width\"], [1, \"datatable-row-detail\", 3, \"height\"], [1, \"datatable-group-header\"], [1, \"datatable-group-cell\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"datatable-checkbox\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"datatable-row-detail\"]],\n      template: function DataTableRowWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵconditionalCreate(0, DataTableRowWrapperComponent_Conditional_0_Template, 4, 7, \"div\", 1);\n          i0.ɵɵconditionalCreate(1, DataTableRowWrapperComponent_Conditional_1_Template, 1, 0);\n          i0.ɵɵconditionalCreate(2, DataTableRowWrapperComponent_Conditional_2_Template, 2, 4, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.isGroup(ctx.row) && (ctx.groupHeader == null ? null : ctx.groupHeader.template) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((ctx.groupHeader == null ? null : ctx.groupHeader.template) && ctx.expanded || !ctx.groupHeader || !ctx.groupHeader.template ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((ctx.rowDetail == null ? null : ctx.rowDetail.template) && ctx.expanded ? 2 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      styles: [\"[_nghost-%COMP%]{display:flex;flex-direction:column}.datatable-row-detail[_ngcontent-%COMP%]{overflow-y:hidden}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableRowWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-row-wrapper',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (isGroup(row) && groupHeader?.template) {\n    <div\n      class=\"datatable-group-header\"\n      [style.height.px]=\"groupHeaderRowHeight\"\n      [style.width.px]=\"innerWidth\"\n    >\n      <div class=\"datatable-group-cell\">\n        @if (groupHeader!.checkboxable) {\n        <div>\n          <label class=\"datatable-checkbox\">\n            <input\n              #select\n              type=\"checkbox\"\n              [checked]=\"selectedGroupRows().length === row.value.length\"\n              (change)=\"onCheckboxChange(select.checked, row)\"\n            />\n          </label>\n        </div>\n        }\n        <ng-template\n          [ngTemplateOutlet]=\"groupHeader!.template!\"\n          [ngTemplateOutletContext]=\"context\"\n        >\n        </ng-template>\n      </div>\n    </div>\n    } @if ((groupHeader?.template && expanded) || !groupHeader || !groupHeader.template) {\n    <ng-content> </ng-content>\n    } @if (rowDetail?.template && expanded) {\n    <div [style.height.px]=\"detailRowHeight\" class=\"datatable-row-detail\">\n      <ng-template [ngTemplateOutlet]=\"rowDetail!.template!\" [ngTemplateOutletContext]=\"context\">\n      </ng-template>\n    </div>\n    }\n  `,\n      host: {\n        class: 'datatable-row-wrapper'\n      },\n      imports: [NgTemplateOutlet],\n      styles: [\":host{display:flex;flex-direction:column}.datatable-row-detail{overflow-y:hidden}\\n\"]\n    }]\n  }], null, {\n    checkBoxInput: [{\n      type: ViewChild,\n      args: ['select']\n    }],\n    innerWidth: [{\n      type: Input\n    }],\n    rowDetail: [{\n      type: Input\n    }],\n    groupHeader: [{\n      type: Input\n    }],\n    offsetX: [{\n      type: Input\n    }],\n    detailRowHeight: [{\n      type: Input\n    }],\n    groupHeaderRowHeight: [{\n      type: Input\n    }],\n    row: [{\n      type: Input\n    }],\n    groupedRows: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    rowContextmenu: [{\n      type: Output\n    }],\n    rowIndex: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onContextmenu: [{\n      type: HostListener,\n      args: ['contextmenu', ['$event']]\n    }]\n  });\n})();\nfunction defaultSumFunc(cells) {\n  const cellsWithValues = cells.filter(cell => !!cell);\n  if (!cellsWithValues.length) {\n    return null;\n  }\n  if (cellsWithValues.some(cell => typeof cell !== 'number')) {\n    return null;\n  }\n  return cellsWithValues.reduce((res, cell) => res + cell);\n}\nfunction noopSumFunc(cells) {\n  return;\n}\nclass DataTableSummaryRowComponent {\n  constructor() {\n    this.summaryRow = {};\n  }\n  ngOnChanges() {\n    if (!this.columns.length || !this.rows.length) {\n      return;\n    }\n    this.updateInternalColumns();\n    this.updateValues();\n  }\n  updateInternalColumns() {\n    this._internalColumns = this.columns.map(col => ({\n      ...col,\n      cellTemplate: col.summaryTemplate\n    }));\n  }\n  updateValues() {\n    this.summaryRow = {};\n    this.columns.filter(col => !col.summaryTemplate && col.prop).forEach(col => {\n      const cellsFromSingleColumn = this.rows.map(row => row[col.prop]);\n      const sumFunc = this.getSummaryFunction(col);\n      this.summaryRow[col.prop] = col.pipe ? col.pipe.transform(sumFunc(cellsFromSingleColumn)) : sumFunc(cellsFromSingleColumn);\n    });\n  }\n  getSummaryFunction(column) {\n    if (column.summaryFunc === undefined) {\n      return defaultSumFunc;\n    } else if (column.summaryFunc === null) {\n      return noopSumFunc;\n    } else {\n      return column.summaryFunc;\n    }\n  }\n  static {\n    this.ɵfac = function DataTableSummaryRowComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableSummaryRowComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableSummaryRowComponent,\n      selectors: [[\"datatable-summary-row\"]],\n      hostAttrs: [1, \"datatable-summary-row\"],\n      inputs: {\n        rows: \"rows\",\n        columns: \"columns\",\n        rowHeight: \"rowHeight\",\n        innerWidth: \"innerWidth\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"tabindex\", \"-1\", 3, \"innerWidth\", \"columns\", \"rowHeight\", \"row\", \"rowIndex\"]],\n      template: function DataTableSummaryRowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵconditionalCreate(0, DataTableSummaryRowComponent_Conditional_0_Template, 1, 7, \"datatable-body-row\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.summaryRow && ctx._internalColumns ? 0 : -1);\n        }\n      },\n      dependencies: [DataTableBodyRowComponent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableSummaryRowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-summary-row',\n      template: `\n    @if (summaryRow && _internalColumns) {\n    <datatable-body-row\n      tabindex=\"-1\"\n      [innerWidth]=\"innerWidth\"\n      [columns]=\"_internalColumns\"\n      [rowHeight]=\"rowHeight\"\n      [row]=\"summaryRow\"\n      [rowIndex]=\"{ index: -1 }\"\n    >\n    </datatable-body-row>\n    }\n  `,\n      host: {\n        class: 'datatable-summary-row'\n      },\n      imports: [DataTableBodyRowComponent]\n    }]\n  }], null, {\n    rows: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    innerWidth: [{\n      type: Input\n    }]\n  });\n})();\nclass DataTableGhostLoaderComponent {\n  constructor() {\n    this.cellMode = false;\n  }\n  static {\n    this.ɵfac = function DataTableGhostLoaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableGhostLoaderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableGhostLoaderComponent,\n      selectors: [[\"ghost-loader\"]],\n      inputs: {\n        columns: \"columns\",\n        pageSize: [2, \"pageSize\", \"pageSize\", numberAttribute],\n        rowHeight: \"rowHeight\",\n        ghostBodyHeight: [2, \"ghostBodyHeight\", \"ghostBodyHeight\", numberAttribute],\n        cellMode: [2, \"cellMode\", \"cellMode\", booleanAttribute]\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[1, \"ghost-loader\", \"ghost-cell-container\"], [1, \"ghost-element\", 3, \"height\", \"datatable-body-row\"], [1, \"ghost-element\"], [1, \"ghost-cell\", 3, \"datatable-body-cell\", \"width\"], [1, \"ghost-cell\"], [1, \"line\", \"ghost-cell-strip\"], [3, \"ngTemplateOutlet\"]],\n      template: function DataTableGhostLoaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵrepeaterCreate(1, DataTableGhostLoaderComponent_For_2_Template, 3, 4, \"div\", 1, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.ghostBodyHeight, \"px\");\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(i0.ɵɵpureFunction0(2, _c4).constructor(ctx.pageSize));\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      styles: [\"@keyframes _ngcontent-%COMP%_ghost{0%{background-position:0vw 0}to{background-position:100vw 0}}.ghost-loader[_ngcontent-%COMP%]{overflow:hidden}.ghost-loader[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{width:100%;height:12px;animation-name:_ngcontent-%COMP%_ghost;animation-iteration-count:infinite;animation-timing-function:linear}.ghost-loader[_ngcontent-%COMP%]   .ghost-element[_ngcontent-%COMP%]{display:flex;align-items:center}.ghost-overlay[_nghost-%COMP%]{position:sticky;top:20px}.ghost-overlay[_nghost-%COMP%]   .ghost-cell[_ngcontent-%COMP%]{padding-inline:1.2rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableGhostLoaderComponent, [{\n    type: Component,\n    args: [{\n      selector: `ghost-loader`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgTemplateOutlet],\n      template: \"<div [style.height.px]=\\\"ghostBodyHeight\\\" class=\\\"ghost-loader ghost-cell-container\\\">\\n  @for (item of [].constructor(pageSize); track item) {\\n  <div [style.height.px]=\\\"rowHeight\\\" class=\\\"ghost-element\\\" [class.datatable-body-row]=\\\"cellMode\\\">\\n    @for (col of columns; track col) {\\n    <div class=\\\"ghost-cell\\\" [class.datatable-body-cell]=\\\"cellMode\\\" [style.width.px]=\\\"col.width\\\">\\n      @if (!col.ghostCellTemplate) {\\n      <div class=\\\"line ghost-cell-strip\\\"></div>\\n      } @else {\\n      <ng-template [ngTemplateOutlet]=\\\"col.ghostCellTemplate\\\"></ng-template>\\n      }\\n    </div>\\n    }\\n  </div>\\n  }\\n</div>\\n\",\n      styles: [\"@keyframes ghost{0%{background-position:0vw 0}to{background-position:100vw 0}}.ghost-loader{overflow:hidden}.ghost-loader .line{width:100%;height:12px;animation-name:ghost;animation-iteration-count:infinite;animation-timing-function:linear}.ghost-loader .ghost-element{display:flex;align-items:center}:host.ghost-overlay{position:sticky;top:20px}:host.ghost-overlay .ghost-cell{padding-inline:1.2rem}\\n\"]\n    }]\n  }], null, {\n    columns: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    ghostBodyHeight: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    cellMode: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass DatatableBodyRowDirective {\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n  static {\n    this.ɵfac = function DatatableBodyRowDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableBodyRowDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DatatableBodyRowDirective,\n      selectors: [[\"\", \"ngx-datatable-body-row\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableBodyRowDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-datatable-body-row]'\n    }]\n  }], null, null);\n})();\nfunction selectRows(selected, row, comparefn) {\n  const selectedIndex = comparefn(row, selected);\n  if (selectedIndex > -1) {\n    selected.splice(selectedIndex, 1);\n  } else {\n    selected.push(row);\n  }\n  return selected;\n}\nfunction selectRowsBetween(selected, rows, index, prevIndex) {\n  const reverse = index < prevIndex;\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    const greater = i >= prevIndex && i <= index;\n    const lesser = i <= prevIndex && i >= index;\n    let range = {\n      start: 0,\n      end: 0\n    };\n    if (reverse) {\n      range = {\n        start: index,\n        end: prevIndex\n      };\n    } else {\n      range = {\n        start: prevIndex,\n        end: index + 1\n      };\n    }\n    if (reverse && lesser || !reverse && greater) {\n      // if in the positive range to be added to `selected`, and\n      // not already in the selected array, add it\n      if (i >= range.start && i <= range.end && row) {\n        selected.push(row);\n      }\n    }\n  }\n  return selected;\n}\nclass DataTableBodyComponent {\n  set pageSize(val) {\n    if (val !== this._pageSize) {\n      this._pageSize = val;\n      this.recalcLayout();\n      // Emits the page event if page size has been changed\n      this._offsetEvent = -1;\n      this.updatePage('up');\n      this.updatePage('down');\n    }\n  }\n  get pageSize() {\n    return this._pageSize;\n  }\n  set rows(val) {\n    if (val !== this._rows) {\n      this._rows = val;\n      this.recalcLayout();\n    }\n  }\n  get rows() {\n    return this._rows;\n  }\n  set columns(val) {\n    if (val !== this._columns) {\n      this._columns = val;\n      this.updateColumnGroupWidths();\n    }\n  }\n  get columns() {\n    return this._columns;\n  }\n  set offset(val) {\n    if (val !== this._offset) {\n      this._offset = val;\n      if (!this.scrollbarV || this.scrollbarV && !this.virtualization) {\n        this.recalcLayout();\n      }\n    }\n  }\n  get offset() {\n    return this._offset;\n  }\n  set rowCount(val) {\n    if (val !== this._rowCount) {\n      this._rowCount = val;\n      this.recalcLayout();\n    }\n  }\n  get rowCount() {\n    return this._rowCount;\n  }\n  get bodyWidth() {\n    if (this.scrollbarH) {\n      return this.innerWidth + 'px';\n    } else {\n      return '100%';\n    }\n  }\n  set bodyHeight(val) {\n    if (this.scrollbarV) {\n      this._bodyHeight = val + 'px';\n    } else {\n      this._bodyHeight = 'auto';\n    }\n    this.recalcLayout();\n  }\n  get bodyHeight() {\n    return this._bodyHeight;\n  }\n  /**\n   * Returns if selection is enabled.\n   */\n  get selectEnabled() {\n    return !!this.selectionType;\n  }\n  /**\n   * Creates an instance of DataTableBodyComponent.\n   */\n  constructor() {\n    this.cd = inject(ChangeDetectorRef);\n    this.selected = [];\n    this.verticalScrollVisible = false;\n    this.scroll = new EventEmitter();\n    this.page = new EventEmitter();\n    this.activate = new EventEmitter();\n    this.select = new EventEmitter();\n    this.rowContextmenu = new EventEmitter(false);\n    this.treeAction = new EventEmitter();\n    /**\n     * Property that would calculate the height of scroll bar\n     * based on the row heights cache for virtual scroll and virtualization. Other scenarios\n     * calculate scroll height automatically (as height will be undefined).\n     */\n    this.scrollHeight = computed(() => {\n      if (this.rowHeightsCache() && this.scrollbarV && this.virtualization && this.rowCount) {\n        return this.rowHeightsCache().query(this.rowCount - 1);\n      }\n      // avoid TS7030: Not all code paths return a value.\n      return undefined;\n    });\n    this.rowsToRender = computed(() => {\n      return this.updateRows();\n    });\n    this.rowHeightsCache = signal(new RowHeightCache());\n    this.offsetY = 0;\n    this.indexes = signal({\n      first: 0,\n      last: 0\n    });\n    this.rowExpansions = [];\n    this._offsetEvent = -1;\n    /**\n     * Get the height of the detail row.\n     */\n    this.getDetailRowHeight = (row, index) => {\n      if (!this.rowDetail) {\n        return 0;\n      }\n      const rowHeight = this.rowDetail.rowHeight;\n      return typeof rowHeight === 'function' ? rowHeight(row, index) : rowHeight;\n    };\n    this.getGroupHeaderRowHeight = (row, index) => {\n      if (!this.groupHeader) {\n        return 0;\n      }\n      const rowHeight = this.groupHeader?.rowHeight === 0 ? this.rowHeight : this.groupHeader?.rowHeight;\n      return typeof rowHeight === 'function' ? rowHeight(row, index) : rowHeight;\n    };\n    /**\n     * Calculates the offset of the rendered rows.\n     * As virtual rows are not shown, we have to move all rendered rows\n     * by the total size of previous non-rendered rows.\n     * If each row has a size of 10px and the first 10 rows are not rendered due to scroll,\n     * then we have a renderOffset of 100px.\n     */\n    this.renderOffset = computed(() => {\n      if (this.scrollbarV && this.virtualization) {\n        return `translateY(${this.rowHeightsCache().query(this.indexes().first - 1)}px)`;\n      } else {\n        return '';\n      }\n    });\n    // declare fn here so we can get access to the `this` property\n    this.rowTrackingFn = (index, row) => {\n      if (this.ghostLoadingIndicator) {\n        return index;\n      }\n      if (this.trackByProp && row) {\n        return row[this.trackByProp];\n      } else {\n        return row;\n      }\n    };\n  }\n  /**\n   * Called after the constructor, initializing input properties\n   */\n  ngOnInit() {\n    if (this.rowDetail) {\n      this.listener = this.rowDetail.toggle.subscribe(({\n        type,\n        value\n      }) => this.toggleStateChange(type, value));\n    }\n    if (this.groupHeader) {\n      this.listener = this.groupHeader.toggle.subscribe(({\n        type,\n        value\n      }) => {\n        // Remove default expansion state once user starts manual toggle.\n        this.groupExpansionDefault = false;\n        this.toggleStateChange(type, value);\n      });\n    }\n  }\n  toggleStateChange(type, value) {\n    if (type === 'group' || type === 'row') {\n      this.toggleRowExpansion(value);\n    }\n    if (type === 'all') {\n      this.toggleAllRows(value);\n    }\n    // Refresh rows after toggle\n    // Fixes #883\n    this.updateIndexes();\n    this.cd.markForCheck();\n  }\n  /**\n   * Called once, before the instance is destroyed.\n   */\n  ngOnDestroy() {\n    if (this.rowDetail || this.groupHeader) {\n      this.listener.unsubscribe();\n    }\n  }\n  /**\n   * Updates the Y offset given a new offset.\n   */\n  updateOffsetY(offset) {\n    // scroller is missing on empty table\n    if (!this.scroller) {\n      return;\n    }\n    if (this.scrollbarV && this.virtualization && offset) {\n      // First get the row Index that we need to move to.\n      const rowIndex = this.pageSize * offset;\n      offset = this.rowHeightsCache().query(rowIndex - 1);\n    } else if (this.scrollbarV && !this.virtualization) {\n      offset = 0;\n    }\n    this.scroller.setOffset(offset || 0);\n  }\n  /**\n   * Body was scrolled, this is mainly useful for\n   * when a user is server-side pagination via virtual scroll.\n   */\n  onBodyScroll(event) {\n    const scrollYPos = event.scrollYPos;\n    const scrollXPos = event.scrollXPos;\n    // if scroll change, trigger update\n    // this is mainly used for header cell positions\n    if (this.offsetY !== scrollYPos || this.offsetX !== scrollXPos) {\n      this.scroll.emit({\n        offsetY: scrollYPos,\n        offsetX: scrollXPos\n      });\n    }\n    this.offsetY = scrollYPos;\n    this.offsetX = scrollXPos;\n    this.updateIndexes();\n    this.updatePage(event.direction);\n    this.cd.detectChanges();\n  }\n  /**\n   * Updates the page given a direction.\n   */\n  updatePage(direction) {\n    let offset = this.indexes().first / this.pageSize;\n    const scrollInBetween = !Number.isInteger(offset);\n    if (direction === 'up') {\n      offset = Math.ceil(offset);\n    } else if (direction === 'down') {\n      offset = Math.floor(offset);\n    }\n    if (direction !== undefined && !isNaN(offset) && offset !== this._offsetEvent) {\n      this._offsetEvent = offset;\n      // if scroll was done by mouse drag make sure previous row and next row data is also fetched if its not fetched\n      if (scrollInBetween && this.scrollbarV && this.virtualization && this.externalPaging) {\n        const upRow = this.rows[this.indexes().first - 1];\n        if (!upRow && direction === 'up') {\n          this.page.emit(offset - 1);\n        }\n        const downRow = this.rows[this.indexes().first + this.pageSize];\n        if (!downRow && direction === 'down') {\n          this.page.emit(offset + 1);\n        }\n      }\n      this.page.emit(offset);\n    }\n  }\n  /**\n   * Updates the rows in the view port\n   */\n  updateRows() {\n    const {\n      first,\n      last\n    } = this.indexes();\n    // if grouprowsby has been specified treat row paging\n    // parameters as group paging parameters ie if limit 10 has been\n    // specified treat it as 10 groups rather than 10 rows\n    const rows = this.groupedRows ? this.groupedRows.slice(first, Math.min(last, this.groupedRows.length)) : this.rows.slice(first, Math.min(last, this.rowCount));\n    rows.length = last - first;\n    return rows;\n  }\n  /**\n   * Get the row height\n   */\n  getRowHeight(row) {\n    // if its a function return it\n    if (typeof this.rowHeight === 'function') {\n      return this.rowHeight(row);\n    }\n    return this.rowHeight;\n  }\n  /**\n   * @param group the group with all rows\n   */\n  getGroupHeight(group) {\n    let rowHeight = 0;\n    if (group.value) {\n      // eslint-disable-next-line @typescript-eslint/prefer-for-of\n      for (let index = 0; index < group.value.length; index++) {\n        rowHeight += this.getRowAndDetailHeight(group.value[index]);\n      }\n    }\n    return rowHeight;\n  }\n  /**\n   * Calculate row height based on the expanded state of the row.\n   */\n  getRowAndDetailHeight(row) {\n    let rowHeight = this.getRowHeight(row);\n    const expanded = this.getRowExpanded(row);\n    // Adding detail row height if its expanded.\n    if (expanded) {\n      rowHeight += this.getDetailRowHeight(row);\n    }\n    return rowHeight;\n  }\n  /**\n   * Updates the index of the rows in the viewport\n   */\n  updateIndexes() {\n    let first = 0;\n    let last = 0;\n    if (this.scrollbarV) {\n      if (this.virtualization) {\n        // Calculation of the first and last indexes will be based on where the\n        // scrollY position would be at.  The last index would be the one\n        // that shows up inside the view port the last.\n        const height = parseInt(this._bodyHeight, 10);\n        first = this.rowHeightsCache().getRowIndex(this.offsetY);\n        last = this.rowHeightsCache().getRowIndex(height + this.offsetY) + 1;\n      } else {\n        // If virtual rows are not needed\n        // We render all in one go\n        first = 0;\n        last = this.rowCount;\n      }\n    } else {\n      // The server is handling paging and will pass an array that begins with the\n      // element at a specified offset.  first should always be 0 with external paging.\n      if (!this.externalPaging) {\n        first = Math.max(this.offset * this.pageSize, 0);\n      }\n      last = Math.min(first + this.pageSize, this.rowCount);\n    }\n    this.indexes.set({\n      first,\n      last\n    });\n  }\n  /**\n   * Refreshes the full Row Height cache.  Should be used\n   * when the entire row array state has changed.\n   */\n  refreshRowHeightCache() {\n    if (!this.scrollbarV || this.scrollbarV && !this.virtualization) {\n      return;\n    }\n    // clear the previous row height cache if already present.\n    // this is useful during sorts, filters where the state of the\n    // rows array is changed.\n    this.rowHeightsCache().clearCache();\n    // Initialize the tree only if there are rows inside the tree.\n    if (this.rows.length) {\n      const rowExpansions = new Set();\n      if (this.rowDetail) {\n        for (const row of this.rows) {\n          if (row && this.getRowExpanded(row)) {\n            rowExpansions.add(row);\n          }\n        }\n      }\n      this.rowHeightsCache().initCache({\n        rows: this.rows,\n        rowHeight: this.rowHeight,\n        detailRowHeight: this.getDetailRowHeight,\n        externalVirtual: this.scrollbarV && this.externalPaging,\n        indexOffset: this.indexes().first,\n        rowCount: this.rowCount,\n        rowExpansions\n      });\n      this.rowHeightsCache.set(Object.create(this.rowHeightsCache()));\n    }\n  }\n  /**\n   * Toggle the Expansion of the row i.e. if the row is expanded then it will\n   * collapse and vice versa.   Note that the expanded status is stored as\n   * a part of the row object itself as we have to preserve the expanded row\n   * status in case of sorting and filtering of the row set.\n   */\n  toggleRowExpansion(row) {\n    const rowExpandedIdx = this.getRowExpandedIdx(row, this.rowExpansions);\n    const expanded = rowExpandedIdx > -1;\n    // Update the toggled row and update thive nevere heights in the cache.\n    if (expanded) {\n      this.rowExpansions.splice(rowExpandedIdx, 1);\n    } else {\n      this.rowExpansions.push(row);\n    }\n    // If the detailRowHeight is auto --> only in case of non-virtualized scroll\n    if (this.scrollbarV && this.virtualization) {\n      this.refreshRowHeightCache();\n    }\n  }\n  /**\n   * Expand/Collapse all the rows no matter what their state is.\n   */\n  toggleAllRows(expanded) {\n    // clear prev expansions\n    this.rowExpansions = [];\n    const rows = this.groupedRows ?? this.rows;\n    if (expanded) {\n      for (const row of rows) {\n        this.rowExpansions.push(row);\n      }\n    }\n    if (this.scrollbarV) {\n      // Refresh the full row heights cache since every row was affected.\n      this.recalcLayout();\n    }\n  }\n  /**\n   * Recalculates the table\n   */\n  recalcLayout() {\n    this.refreshRowHeightCache();\n    this.updateIndexes();\n  }\n  /**\n   * Returns if the row was expanded and set default row expansion when row expansion is empty\n   */\n  getRowExpanded(row) {\n    if (this.rowExpansions.length === 0 && this.groupExpansionDefault) {\n      for (const group of this.groupedRows) {\n        this.rowExpansions.push(group);\n      }\n    }\n    return this.getRowExpandedIdx(row, this.rowExpansions) > -1;\n  }\n  getRowExpandedIdx(row, expanded) {\n    if (!expanded || !expanded.length) {\n      return -1;\n    }\n    const rowId = this.rowIdentity(row);\n    return expanded.findIndex(r => {\n      const id = this.rowIdentity(r);\n      return id === rowId;\n    });\n  }\n  onTreeAction(row) {\n    this.treeAction.emit({\n      row\n    });\n  }\n  dragOver(event, dropRow) {\n    event.preventDefault();\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      eventType: 'dragover',\n      dragRow: this._draggedRow,\n      dropRow\n    });\n  }\n  drag(event, dragRow, rowComponent) {\n    this._draggedRow = dragRow;\n    this._draggedRowElement = rowComponent._element;\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      eventType: 'dragstart',\n      dragRow\n    });\n  }\n  drop(event, dropRow, rowComponent) {\n    event.preventDefault();\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      targetElement: rowComponent._element,\n      eventType: 'drop',\n      dragRow: this._draggedRow,\n      dropRow\n    });\n  }\n  dragEnter(event, dropRow, rowComponent) {\n    event.preventDefault();\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      targetElement: rowComponent._element,\n      eventType: 'dragenter',\n      dragRow: this._draggedRow,\n      dropRow\n    });\n  }\n  dragLeave(event, dropRow, rowComponent) {\n    event.preventDefault();\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      targetElement: rowComponent._element,\n      eventType: 'dragleave',\n      dragRow: this._draggedRow,\n      dropRow\n    });\n  }\n  dragEnd(event, dragRow) {\n    event.preventDefault();\n    this.rowDragEvents.emit({\n      event,\n      srcElement: this._draggedRowElement,\n      eventType: 'dragend',\n      dragRow\n    });\n    this._draggedRow = undefined;\n    this._draggedRowElement = undefined;\n  }\n  updateColumnGroupWidths() {\n    const colsByPin = columnsByPin(this._columns);\n    this.columnGroupWidths = columnGroupWidths(colsByPin, this._columns);\n  }\n  selectRow(event, index, row) {\n    if (!this.selectEnabled) {\n      return;\n    }\n    const chkbox = this.selectionType === SelectionType.checkbox;\n    const multi = this.selectionType === SelectionType.multi;\n    const multiClick = this.selectionType === SelectionType.multiClick;\n    let selected = [];\n    // TODO: this code needs cleanup. Casting it to KeyboardEvent is not correct as it could also be other types.\n    if (multi || chkbox || multiClick) {\n      if (event.shiftKey) {\n        selected = selectRowsBetween([], this.rows, index, this.prevIndex);\n      } else if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n        // select all rows except dummy rows which are added for ghostloader in case of virtual scroll\n        selected = this.rows.filter(rowItem => !!rowItem);\n      } else if (event.ctrlKey || event.metaKey || multiClick || chkbox) {\n        selected = selectRows([...this.selected], row, this.getRowSelectedIdx.bind(this));\n      } else {\n        selected = selectRows([], row, this.getRowSelectedIdx.bind(this));\n      }\n    } else {\n      selected = selectRows([], row, this.getRowSelectedIdx.bind(this));\n    }\n    if (typeof this.selectCheck === 'function') {\n      selected = selected.filter(this.selectCheck.bind(this));\n    }\n    if (typeof this.disableRowCheck === 'function') {\n      selected = selected.filter(rowData => !this.disableRowCheck(rowData));\n    }\n    this.selected.splice(0, this.selected.length);\n    this.selected.push(...selected);\n    this.prevIndex = index;\n    this.select.emit({\n      selected\n    });\n  }\n  onActivate(model, index) {\n    const {\n      type,\n      event,\n      row\n    } = model;\n    const chkbox = this.selectionType === SelectionType.checkbox;\n    const select = !chkbox && (type === 'click' || type === 'dblclick') || chkbox && type === 'checkbox';\n    if (select) {\n      this.selectRow(event, index, row);\n    } else if (type === 'keydown') {\n      if (event.key === Keys.return) {\n        this.selectRow(event, index, row);\n      } else if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n        this.selectRow(event, 0, row); // The row property is ignored in this case. So we can pass anything.\n      } else {\n        this.onKeyboardFocus(model);\n      }\n    }\n    this.activate.emit(model);\n  }\n  onKeyboardFocus(model) {\n    const {\n      key\n    } = model.event;\n    const shouldFocus = key === Keys.up || key === Keys.down || key === Keys.right || key === Keys.left;\n    if (shouldFocus) {\n      const isCellSelection = this.selectionType === SelectionType.cell;\n      if (typeof this.disableRowCheck === 'function') {\n        const isRowDisabled = this.disableRowCheck(model.row);\n        if (isRowDisabled) {\n          return;\n        }\n      }\n      if (!model.cellElement || !isCellSelection) {\n        this.focusRow(model.rowElement, key);\n      } else if (isCellSelection && model.cellIndex !== undefined) {\n        this.focusCell(model.cellElement, model.rowElement, key, model.cellIndex);\n      }\n    }\n  }\n  focusRow(rowElement, key) {\n    const nextRowElement = this.getPrevNextRow(rowElement, key);\n    if (nextRowElement) {\n      nextRowElement.focus();\n    }\n  }\n  getPrevNextRow(rowElement, key) {\n    const parentElement = rowElement.parentElement;\n    if (parentElement) {\n      let focusElement = null;\n      if (key === Keys.up) {\n        focusElement = parentElement.previousElementSibling;\n      } else if (key === Keys.down) {\n        focusElement = parentElement.nextElementSibling;\n      }\n      if (focusElement && focusElement.children.length) {\n        return focusElement.children[0];\n      }\n    }\n  }\n  focusCell(cellElement, rowElement, key, cellIndex) {\n    let nextCellElement = null;\n    if (key === Keys.left) {\n      nextCellElement = cellElement.previousElementSibling;\n    } else if (key === Keys.right) {\n      nextCellElement = cellElement.nextElementSibling;\n    } else if (key === Keys.up || key === Keys.down) {\n      const nextRowElement = this.getPrevNextRow(rowElement, key);\n      if (nextRowElement) {\n        const children = nextRowElement.getElementsByClassName('datatable-body-cell');\n        if (children.length) {\n          nextCellElement = children[cellIndex];\n        }\n      }\n    }\n    if (nextCellElement && 'focus' in nextCellElement && typeof nextCellElement.focus === 'function') {\n      nextCellElement.focus();\n    }\n  }\n  getRowSelected(row) {\n    return this.getRowSelectedIdx(row, this.selected) > -1;\n  }\n  getRowSelectedIdx(row, selected) {\n    if (!selected || !selected.length) {\n      return -1;\n    }\n    const rowId = this.rowIdentity(row);\n    return selected.findIndex(r => {\n      const id = this.rowIdentity(r);\n      return id === rowId;\n    });\n  }\n  isGroup(row) {\n    return !!this.groupedRows;\n  }\n  isRow(row) {\n    return !this.groupedRows;\n  }\n  static {\n    this.ɵfac = function DataTableBodyComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableBodyComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableBodyComponent,\n      selectors: [[\"datatable-body\"]],\n      viewQuery: function DataTableBodyComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ScrollerComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n        }\n      },\n      hostAttrs: [1, \"datatable-body\"],\n      hostVars: 4,\n      hostBindings: function DataTableBodyComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", ctx.bodyWidth)(\"height\", ctx.bodyHeight);\n        }\n      },\n      inputs: {\n        rowDefTemplate: \"rowDefTemplate\",\n        scrollbarV: \"scrollbarV\",\n        scrollbarH: \"scrollbarH\",\n        loadingIndicator: \"loadingIndicator\",\n        ghostLoadingIndicator: \"ghostLoadingIndicator\",\n        externalPaging: \"externalPaging\",\n        rowHeight: \"rowHeight\",\n        offsetX: \"offsetX\",\n        selectionType: \"selectionType\",\n        selected: \"selected\",\n        rowIdentity: \"rowIdentity\",\n        rowDetail: \"rowDetail\",\n        groupHeader: \"groupHeader\",\n        selectCheck: \"selectCheck\",\n        displayCheck: \"displayCheck\",\n        trackByProp: \"trackByProp\",\n        rowClass: \"rowClass\",\n        groupedRows: \"groupedRows\",\n        groupExpansionDefault: \"groupExpansionDefault\",\n        innerWidth: \"innerWidth\",\n        groupRowsBy: \"groupRowsBy\",\n        virtualization: \"virtualization\",\n        summaryRow: \"summaryRow\",\n        summaryPosition: \"summaryPosition\",\n        summaryHeight: \"summaryHeight\",\n        rowDraggable: \"rowDraggable\",\n        rowDragEvents: \"rowDragEvents\",\n        disableRowCheck: \"disableRowCheck\",\n        pageSize: \"pageSize\",\n        rows: \"rows\",\n        columns: \"columns\",\n        offset: \"offset\",\n        rowCount: \"rowCount\",\n        bodyHeight: \"bodyHeight\",\n        verticalScrollVisible: \"verticalScrollVisible\"\n      },\n      outputs: {\n        scroll: \"scroll\",\n        page: \"page\",\n        activate: \"activate\",\n        select: \"select\",\n        rowContextmenu: \"rowContextmenu\",\n        treeAction: \"treeAction\"\n      },\n      ngContentSelectors: _c6,\n      decls: 4,\n      vars: 4,\n      consts: [[\"bodyRow\", \"\"], [\"rowElement\", \"\"], [1, \"custom-loading-indicator-wrapper\"], [1, \"ghost-overlay\", 3, \"columns\", \"pageSize\", \"rowHeight\", \"ghostBodyHeight\"], [3, \"scrollbarV\", \"scrollbarH\", \"scrollHeight\", \"width\"], [1, \"custom-loading-content\"], [3, \"scroll\", \"scrollbarV\", \"scrollbarH\", \"scrollHeight\", \"scrollWidth\"], [3, \"rowHeight\", \"innerWidth\", \"rows\", \"columns\"], [\"ngx-datatable-body-row\", \"\"], [\"role\", \"row\", 3, \"rowHeight\", \"innerWidth\", \"rows\", \"columns\"], [\"role\", \"row\", \"tabindex\", \"-1\", 3, \"treeAction\", \"activate\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", \"dragstart\", \"dragend\", \"disabled\", \"isSelected\", \"innerWidth\", \"columns\", \"rowHeight\", \"row\", \"group\", \"rowIndex\", \"expanded\", \"rowClass\", \"displayCheck\", \"treeStatus\", \"draggable\", \"verticalScrollVisible\"], [\"cellMode\", \"\", 3, \"columns\", \"pageSize\", \"rowHeight\"], [3, \"groupedRows\", \"innerWidth\", \"width\", \"rowDetail\", \"groupHeader\", \"offsetX\", \"detailRowHeight\", \"groupHeaderRowHeight\", \"row\", \"disabled\", \"expanded\", \"rowIndex\", \"selected\"], [3, \"rowContextmenu\", \"groupedRows\", \"innerWidth\", \"rowDetail\", \"groupHeader\", \"offsetX\", \"detailRowHeight\", \"groupHeaderRowHeight\", \"row\", \"disabled\", \"expanded\", \"rowIndex\", \"selected\"], [4, \"rowDefInternal\", \"rowDefInternalDisabled\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"scroll\", \"scrollbarV\", \"scrollbarH\", \"scrollHeight\"]],\n      template: function DataTableBodyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c5);\n          i0.ɵɵconditionalCreate(0, DataTableBodyComponent_Conditional_0_Template, 3, 0, \"div\", 2);\n          i0.ɵɵconditionalCreate(1, DataTableBodyComponent_Conditional_1_Template, 1, 4, \"ghost-loader\", 3);\n          i0.ɵɵconditionalCreate(2, DataTableBodyComponent_Conditional_2_Template, 8, 8);\n          i0.ɵɵconditionalCreate(3, DataTableBodyComponent_Conditional_3_Template, 2, 5, \"datatable-scroller\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.loadingIndicator ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.ghostLoadingIndicator && (!ctx.rowCount || !ctx.virtualization || !ctx.scrollbarV) ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.rows.length ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!(ctx.rows == null ? null : ctx.rows.length) && !ctx.loadingIndicator && !ctx.ghostLoadingIndicator ? 3 : -1);\n        }\n      },\n      dependencies: [DataTableGhostLoaderComponent, ScrollerComponent, DataTableSummaryRowComponent, DataTableRowWrapperComponent, DatatableRowDefInternalDirective, DataTableBodyRowComponent, DraggableDirective, NgTemplateOutlet, DatatableBodyRowDirective],\n      styles: [\"[_nghost-%COMP%]{position:relative;z-index:10;display:block;overflow:hidden}ngx-datatable.scroll-horz   [_nghost-%COMP%]{overflow-x:auto;-webkit-overflow-scrolling:touch}datatable-scroller[_ngcontent-%COMP%]{display:block}ngx-datatable.fixed-row[_nghost-%COMP%]   datatable-scroller[_ngcontent-%COMP%], ngx-datatable.fixed-row   [_nghost-%COMP%]   datatable-scroller[_ngcontent-%COMP%]{white-space:nowrap}ngx-datatable.scroll-vertical   [_nghost-%COMP%]{overflow-y:auto}[hidden][_ngcontent-%COMP%]{display:none!important}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableBodyComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-body',\n      template: `\n    @if (loadingIndicator) {\n      <div class=\"custom-loading-indicator-wrapper\">\n        <div class=\"custom-loading-content\">\n          <ng-content select=\"[loading-indicator]\"></ng-content>\n        </div>\n      </div>\n    }\n    @if (ghostLoadingIndicator && (!rowCount || !virtualization || !scrollbarV)) {\n      <ghost-loader\n        class=\"ghost-overlay\"\n        [columns]=\"columns\"\n        [pageSize]=\"pageSize\"\n        [rowHeight]=\"rowHeight\"\n        [ghostBodyHeight]=\"bodyHeight\"\n      >\n      </ghost-loader>\n    }\n    @if (rows.length) {\n      <datatable-scroller\n        [scrollbarV]=\"scrollbarV\"\n        [scrollbarH]=\"scrollbarH\"\n        [scrollHeight]=\"scrollHeight()\"\n        [scrollWidth]=\"columnGroupWidths?.total\"\n        (scroll)=\"onBodyScroll($event)\"\n      >\n        @if (summaryRow && summaryPosition === 'top') {\n          <datatable-summary-row\n            [rowHeight]=\"summaryHeight\"\n            [innerWidth]=\"innerWidth\"\n            [rows]=\"rows\"\n            [columns]=\"columns\"\n          >\n          </datatable-summary-row>\n        }\n        <ng-template\n          ngx-datatable-body-row\n          #bodyRow\n          let-row=\"row\"\n          let-index=\"index\"\n          let-indexInGroup=\"indexInGroup\"\n          let-groupedRows=\"groupedRows\"\n          let-disabled=\"disabled\"\n        >\n          <datatable-body-row\n            role=\"row\"\n            tabindex=\"-1\"\n            #rowElement\n            [disabled]=\"disabled\"\n            [isSelected]=\"getRowSelected(row)\"\n            [innerWidth]=\"innerWidth\"\n            [columns]=\"columns\"\n            [rowHeight]=\"getRowHeight(row)\"\n            [row]=\"row\"\n            [group]=\"groupedRows\"\n            [rowIndex]=\"{ index: index, indexInGroup: indexInGroup }\"\n            [expanded]=\"getRowExpanded(row)\"\n            [rowClass]=\"rowClass\"\n            [displayCheck]=\"displayCheck\"\n            [treeStatus]=\"row?.treeStatus\"\n            [draggable]=\"rowDraggable\"\n            [verticalScrollVisible]=\"verticalScrollVisible\"\n            (treeAction)=\"onTreeAction(row)\"\n            (activate)=\"onActivate($event, index)\"\n            (drop)=\"drop($event, row, rowElement)\"\n            (dragover)=\"dragOver($event, row)\"\n            (dragenter)=\"dragEnter($event, row, rowElement)\"\n            (dragleave)=\"dragLeave($event, row, rowElement)\"\n            (dragstart)=\"drag($event, row, rowElement)\"\n            (dragend)=\"dragEnd($event, row)\"\n          >\n          </datatable-body-row>\n        </ng-template>\n\n        <div [style.transform]=\"renderOffset()\">\n          @for (group of rowsToRender(); track rowTrackingFn(i, group); let i = $index) {\n            @if (!group && ghostLoadingIndicator) {\n              <ghost-loader cellMode [columns]=\"columns\" [pageSize]=\"1\" [rowHeight]=\"rowHeight\" />\n            } @else if (group) {\n              @let disabled = isRow(group) && disableRowCheck && disableRowCheck(group);\n              <!-- $any(group) is needed as the typing is broken and the feature as well. See #147. -->\n              <!-- FIXME: This has to be revisited and fixed. -->\n              <datatable-row-wrapper\n                [attr.hidden]=\"\n                  ghostLoadingIndicator && (!rowCount || !virtualization || !scrollbarV)\n                    ? true\n                    : null\n                \"\n                [groupedRows]=\"groupedRows\"\n                [innerWidth]=\"innerWidth\"\n                [style.width]=\"groupedRows ? columnGroupWidths.total : undefined\"\n                [rowDetail]=\"rowDetail\"\n                [groupHeader]=\"groupHeader\"\n                [offsetX]=\"offsetX\"\n                [detailRowHeight]=\"getDetailRowHeight(group && $any(group)[i], i)\"\n                [groupHeaderRowHeight]=\"getGroupHeaderRowHeight(group && $any(group)[i], i)\"\n                [row]=\"group\"\n                [disabled]=\"disabled\"\n                [expanded]=\"getRowExpanded(group)\"\n                [rowIndex]=\"indexes().first + i\"\n                [selected]=\"selected\"\n                (rowContextmenu)=\"rowContextmenu.emit($event)\"\n              >\n                @if (rowDefTemplate) {\n                  <ng-container\n                    *rowDefInternal=\"\n                      {\n                        template: rowDefTemplate,\n                        rowTemplate: bodyRow,\n                        row: group,\n                        index: i\n                      };\n                      disabled: disabled\n                    \"\n                  />\n                } @else {\n                  @if (isRow(group)) {\n                    <ng-container\n                      [ngTemplateOutlet]=\"bodyRow\"\n                      [ngTemplateOutletContext]=\"{\n                        row: group,\n                        index: indexes().first + i,\n                        disabled\n                      }\"\n                    ></ng-container>\n                  }\n                }\n\n                @if (isGroup(group)) {\n                  <!-- The row typecast is due to angular compiler acting weird. It is obvious that it is of type TRow, but the compiler does not understand. -->\n                  @for (row of group.value; track rowTrackingFn($index, row)) {\n                    @let disabled = disableRowCheck && disableRowCheck(row);\n                    <ng-container\n                      [ngTemplateOutlet]=\"bodyRow\"\n                      [ngTemplateOutletContext]=\"{\n                        row,\n                        groupedRows: group?.value,\n                        index: indexes().first + i,\n                        indexInGroup: $index,\n                        disabled\n                      }\"\n                    ></ng-container>\n                  }\n                }\n              </datatable-row-wrapper>\n            }\n          }\n        </div>\n      </datatable-scroller>\n      @if (summaryRow && summaryPosition === 'bottom') {\n        <datatable-summary-row\n          role=\"row\"\n          [rowHeight]=\"summaryHeight\"\n          [innerWidth]=\"innerWidth\"\n          [rows]=\"rows\"\n          [columns]=\"columns\"\n        >\n        </datatable-summary-row>\n      }\n    }\n    @if (!rows?.length && !loadingIndicator && !ghostLoadingIndicator) {\n      <datatable-scroller\n        [scrollbarV]=\"scrollbarV\"\n        [scrollbarH]=\"scrollbarH\"\n        [scrollHeight]=\"scrollHeight()\"\n        [style.width]=\"scrollbarH ? columnGroupWidths?.total + 'px' : '100%'\"\n        (scroll)=\"onBodyScroll($event)\"\n      >\n        <ng-content select=\"[empty-content]\"></ng-content>\n      </datatable-scroller>\n    }\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'datatable-body'\n      },\n      imports: [DataTableGhostLoaderComponent, ScrollerComponent, DataTableSummaryRowComponent, DataTableRowWrapperComponent, DatatableRowDefInternalDirective, DataTableBodyRowComponent, DraggableDirective, NgTemplateOutlet, DatatableBodyRowDirective],\n      styles: [\":host{position:relative;z-index:10;display:block;overflow:hidden}:host-context(ngx-datatable.scroll-horz) :host{overflow-x:auto;-webkit-overflow-scrolling:touch}datatable-scroller{display:block}:host-context(ngx-datatable.fixed-row) datatable-scroller{white-space:nowrap}:host-context(ngx-datatable.scroll-vertical) :host{overflow-y:auto}[hidden]{display:none!important}\\n\"]\n    }]\n  }], () => [], {\n    rowDefTemplate: [{\n      type: Input\n    }],\n    scrollbarV: [{\n      type: Input\n    }],\n    scrollbarH: [{\n      type: Input\n    }],\n    loadingIndicator: [{\n      type: Input\n    }],\n    ghostLoadingIndicator: [{\n      type: Input\n    }],\n    externalPaging: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    offsetX: [{\n      type: Input\n    }],\n    selectionType: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    rowIdentity: [{\n      type: Input\n    }],\n    rowDetail: [{\n      type: Input\n    }],\n    groupHeader: [{\n      type: Input\n    }],\n    selectCheck: [{\n      type: Input\n    }],\n    displayCheck: [{\n      type: Input\n    }],\n    trackByProp: [{\n      type: Input\n    }],\n    rowClass: [{\n      type: Input\n    }],\n    groupedRows: [{\n      type: Input\n    }],\n    groupExpansionDefault: [{\n      type: Input\n    }],\n    innerWidth: [{\n      type: Input\n    }],\n    groupRowsBy: [{\n      type: Input\n    }],\n    virtualization: [{\n      type: Input\n    }],\n    summaryRow: [{\n      type: Input\n    }],\n    summaryPosition: [{\n      type: Input\n    }],\n    summaryHeight: [{\n      type: Input\n    }],\n    rowDraggable: [{\n      type: Input\n    }],\n    rowDragEvents: [{\n      type: Input\n    }],\n    disableRowCheck: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    rows: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    offset: [{\n      type: Input\n    }],\n    rowCount: [{\n      type: Input\n    }],\n    bodyWidth: [{\n      type: HostBinding,\n      args: ['style.width']\n    }],\n    bodyHeight: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['style.height']\n    }],\n    verticalScrollVisible: [{\n      type: Input\n    }],\n    scroll: [{\n      type: Output\n    }],\n    page: [{\n      type: Output\n    }],\n    activate: [{\n      type: Output\n    }],\n    select: [{\n      type: Output\n    }],\n    rowContextmenu: [{\n      type: Output\n    }],\n    treeAction: [{\n      type: Output\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: [ScrollerComponent]\n    }]\n  });\n})();\n\n/**\n * Gets the width of the scrollbar.  Nesc for windows\n * http://stackoverflow.com/a/13382873/888165\n */\nclass ScrollbarHelper {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.width = this.getWidth();\n  }\n  getWidth() {\n    const outer = this.document.createElement('div');\n    outer.style.visibility = 'hidden';\n    outer.style.width = '100px';\n    this.document.body.appendChild(outer);\n    const widthNoScroll = outer.offsetWidth;\n    outer.style.overflow = 'scroll';\n    const inner = this.document.createElement('div');\n    inner.style.width = '100%';\n    outer.appendChild(inner);\n    const widthWithScroll = inner.offsetWidth;\n    this.document.body.removeChild(outer);\n    return widthNoScroll - widthWithScroll;\n  }\n  static {\n    this.ɵfac = function ScrollbarHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollbarHelper,\n      factory: ScrollbarHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarHelper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass LongPressDirective {\n  constructor() {\n    this.pressEnabled = true;\n    this.duration = 500;\n    this.longPressStart = new EventEmitter();\n    this.longPressEnd = new EventEmitter();\n    this.pressing = signal(false);\n    this.isLongPressing = signal(false);\n  }\n  onMouseDown(event) {\n    const isMouse = event instanceof MouseEvent;\n    // don't do right/middle clicks\n    if (!this.pressEnabled || isMouse && event.button !== 0) {\n      return;\n    }\n    // don't start drag if its on resize handle\n    const target = event.target;\n    if (target.classList.contains('resize-handle')) {\n      return;\n    }\n    this.pressing.set(true);\n    this.isLongPressing.set(false);\n    const mouseup = fromEvent(document, isMouse ? 'mouseup' : 'touchend');\n    this.subscription = mouseup.subscribe(() => this.endPress());\n    this.timeout = setTimeout(() => {\n      this.isLongPressing.set(true);\n      this.longPressStart.emit({\n        event,\n        model: this.pressModel\n      });\n    }, this.duration);\n  }\n  endPress() {\n    clearTimeout(this.timeout);\n    this.isLongPressing.set(false);\n    this.pressing.set(false);\n    this._destroySubscription();\n    this.longPressEnd.emit({\n      model: this.pressModel\n    });\n  }\n  ngOnDestroy() {\n    clearTimeout(this.timeout);\n    this._destroySubscription();\n  }\n  _destroySubscription() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      this.subscription = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function LongPressDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LongPressDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LongPressDirective,\n      selectors: [[\"\", \"long-press\", \"\"]],\n      hostVars: 4,\n      hostBindings: function LongPressDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"touchstart\", function LongPressDirective_touchstart_HostBindingHandler($event) {\n            return ctx.onMouseDown($event);\n          })(\"mousedown\", function LongPressDirective_mousedown_HostBindingHandler($event) {\n            return ctx.onMouseDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"press\", ctx.pressing())(\"longpress\", ctx.isLongPressing());\n        }\n      },\n      inputs: {\n        pressEnabled: [2, \"pressEnabled\", \"pressEnabled\", booleanAttribute],\n        pressModel: \"pressModel\",\n        duration: [2, \"duration\", \"duration\", numberAttribute]\n      },\n      outputs: {\n        longPressStart: \"longPressStart\",\n        longPressEnd: \"longPressEnd\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LongPressDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[long-press]',\n      host: {\n        '(touchstart)': 'onMouseDown($event)',\n        '(mousedown)': 'onMouseDown($event)',\n        '[class.press]': 'pressing()',\n        '[class.longpress]': 'isLongPressing()'\n      }\n    }]\n  }], null, {\n    pressEnabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pressModel: [{\n      type: Input\n    }],\n    duration: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    longPressStart: [{\n      type: Output\n    }],\n    longPressEnd: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Gets the next sort direction\n */\nfunction nextSortDir(sortType, current) {\n  if (sortType === SortType.single) {\n    if (current === SortDirection.asc) {\n      return SortDirection.desc;\n    } else {\n      return SortDirection.asc;\n    }\n  } else {\n    if (!current) {\n      return SortDirection.asc;\n    } else if (current === SortDirection.asc) {\n      return SortDirection.desc;\n    } else if (current === SortDirection.desc) {\n      return undefined;\n    }\n    // avoid TS7030: Not all code paths return a value.\n    return undefined;\n  }\n}\n/**\n * Adapted from fueld-ui on 6/216\n * https://github.com/FuelInteractive/fuel-ui/tree/master/src/pipes/OrderBy\n */\nfunction orderByComparator(a, b) {\n  if (a === null || typeof a === 'undefined') {\n    a = 0;\n  }\n  if (b === null || typeof b === 'undefined') {\n    b = 0;\n  }\n  if (a instanceof Date && b instanceof Date) {\n    if (a < b) {\n      return -1;\n    }\n    if (a > b) {\n      return 1;\n    }\n  } else if (isNaN(parseFloat(a)) || !isFinite(a) || isNaN(parseFloat(b)) || !isFinite(b)) {\n    // Convert to string in case of a=0 or b=0\n    a = String(a);\n    b = String(b);\n    // Isn't a number so lowercase the string to properly compare\n    if (a.toLowerCase() < b.toLowerCase()) {\n      return -1;\n    }\n    if (a.toLowerCase() > b.toLowerCase()) {\n      return 1;\n    }\n  } else {\n    // Parse strings as numbers to compare properly\n    if (parseFloat(a) < parseFloat(b)) {\n      return -1;\n    }\n    if (parseFloat(a) > parseFloat(b)) {\n      return 1;\n    }\n  }\n  // equal each other\n  return 0;\n}\n/**\n * creates a shallow copy of the `rows` input and returns the sorted copy. this function\n * does not sort the `rows` argument in place\n */\nfunction sortRows(rows, columns, dirs) {\n  if (!rows) {\n    return [];\n  }\n  if (!dirs || !dirs.length || !columns) {\n    return [...rows];\n  }\n  const temp = [...rows];\n  const cols = columns.reduce((obj, col) => {\n    if (col.sortable) {\n      obj[col.prop] = col.comparator;\n    }\n    return obj;\n  }, {});\n  // cache valueGetter and compareFn so that they\n  // do not need to be looked-up in the sort function body\n  const cachedDirs = dirs.map(dir => {\n    const prop = dir.prop;\n    return {\n      prop,\n      dir: dir.dir,\n      valueGetter: getterForProp(prop),\n      compareFn: cols[prop]\n    };\n  });\n  return temp.sort((rowA, rowB) => {\n    for (const cachedDir of cachedDirs) {\n      // Get property and valuegetters for column to be sorted\n      const {\n        prop,\n        valueGetter\n      } = cachedDir;\n      // Get A and B cell values from rows based on properties of the columns\n      const propA = valueGetter(rowA, prop);\n      const propB = valueGetter(rowB, prop);\n      // Compare function gets five parameters:\n      // Two cell values to be compared as propA and propB\n      // Two rows corresponding to the cells as rowA and rowB\n      // Direction of the sort for this column as SortDirection\n      // Compare can be a standard JS comparison function (a,b) => -1|0|1\n      // as additional parameters are silently ignored. The whole row and sort\n      // direction enable more complex sort logic.\n      const comparison = cachedDir.dir !== SortDirection.desc ? cachedDir.compareFn(propA, propB, rowA, rowB, cachedDir.dir) : -cachedDir.compareFn(propA, propB, rowA, rowB, cachedDir.dir);\n      // Don't return 0 yet in case of needing to sort by next property\n      if (comparison !== 0) {\n        return comparison;\n      }\n    }\n    return 0;\n  });\n}\nfunction sortGroupedRows(groupedRows, columns, dirs, sortOnGroupHeader) {\n  if (sortOnGroupHeader) {\n    groupedRows = sortRows(groupedRows, columns, [{\n      dir: sortOnGroupHeader.dir,\n      prop: 'key'\n    }]);\n  }\n  return groupedRows.map(group => ({\n    ...group,\n    value: sortRows(group.value, columns, dirs)\n  }));\n}\nclass DataTableHeaderCellComponent {\n  set allRowsSelected(value) {\n    this._allRowsSelected = value;\n    this.cellContext.allRowsSelected = value;\n  }\n  get allRowsSelected() {\n    return this._allRowsSelected;\n  }\n  set column(column) {\n    this._column = column;\n    this.cellContext.column = column;\n    this.cd.markForCheck();\n  }\n  get column() {\n    return this._column;\n  }\n  set sorts(val) {\n    this._sorts = val;\n    this.sortDir = this.calcSortDir(val);\n    this.cellContext.sortDir = this.sortDir;\n    this.sortClass = this.calcSortClass(this.sortDir);\n    this.cd.markForCheck();\n  }\n  get sorts() {\n    return this._sorts;\n  }\n  get columnCssClasses() {\n    let cls = 'datatable-header-cell';\n    if (this.column.sortable) {\n      cls += ' sortable';\n    }\n    if (this.column.resizeable) {\n      cls += ' resizeable';\n    }\n    if (this.column.headerClass) {\n      if (typeof this.column.headerClass === 'string') {\n        cls += ' ' + this.column.headerClass;\n      } else if (typeof this.column.headerClass === 'function') {\n        const res = this.column.headerClass({\n          column: this.column\n        });\n        if (typeof res === 'string') {\n          cls += ' ' + res;\n        } else if (typeof res === 'object') {\n          const keys = Object.keys(res);\n          for (const k of keys) {\n            if (res[k] === true) {\n              cls += ` ${k}`;\n            }\n          }\n        }\n      }\n    }\n    const sortDir = this.sortDir;\n    if (sortDir) {\n      cls += ` sort-active sort-${sortDir}`;\n    }\n    return cls;\n  }\n  get name() {\n    // guaranteed to have a value by setColumnDefaults() in column-helper.ts\n    return this.column.headerTemplate === undefined ? this.column.name : undefined;\n  }\n  get minWidth() {\n    return this.column.minWidth;\n  }\n  get maxWidth() {\n    return this.column.maxWidth;\n  }\n  get width() {\n    return this.column.width;\n  }\n  get tabindex() {\n    return this.column.sortable ? 0 : -1;\n  }\n  get isCheckboxable() {\n    return this.column.headerCheckboxable;\n  }\n  constructor() {\n    this.cd = inject(ChangeDetectorRef);\n    this.enableClearingSortState = false;\n    this.sort = new EventEmitter();\n    this.select = new EventEmitter();\n    this.columnContextmenu = new EventEmitter(false);\n    this.resize = new EventEmitter();\n    this.resizing = new EventEmitter();\n    this.element = inject(ElementRef).nativeElement;\n    // Counter to reset sort once user sort asc and desc.\n    this.totalSortStatesApplied = 0;\n    this.cellContext = {\n      column: this.column,\n      sortDir: this.sortDir,\n      sortFn: () => this.onSort(),\n      allRowsSelected: this.allRowsSelected,\n      selectFn: () => this.select.emit()\n    };\n  }\n  onContextmenu($event) {\n    this.columnContextmenu.emit({\n      event: $event,\n      column: this.column\n    });\n    if (this.column.draggable) {\n      $event.preventDefault();\n    }\n  }\n  enter() {\n    this.onSort();\n  }\n  ngOnInit() {\n    this.sortClass = this.calcSortClass(this.sortDir);\n    // If there is already a default sort then start the counter with 1.\n    if (this.sortDir) {\n      this.totalSortStatesApplied = 1;\n    }\n  }\n  ngOnDestroy() {\n    this.destroySubscription();\n  }\n  calcSortDir(sorts) {\n    if (sorts && this.column) {\n      const sort = sorts.find(s => {\n        return s.prop === this.column.prop;\n      });\n      if (sort) {\n        return sort.dir;\n      }\n    }\n  }\n  onSort() {\n    if (!this.column.sortable) {\n      return;\n    }\n    this.totalSortStatesApplied++;\n    let newValue = nextSortDir(this.sortType, this.sortDir);\n    // User has done both direction sort so we reset the next sort.\n    if (this.enableClearingSortState && this.totalSortStatesApplied === 3) {\n      newValue = undefined;\n      this.totalSortStatesApplied = 0;\n    }\n    this.sort.emit({\n      column: this.column,\n      prevValue: this.sortDir,\n      newValue\n    });\n  }\n  calcSortClass(sortDir) {\n    if (!this.cellContext.column.sortable) {\n      return undefined;\n    }\n    if (sortDir === SortDirection.asc) {\n      return `sort-btn sort-asc ${this.sortAscendingIcon ?? 'datatable-icon-up'}`;\n    } else if (sortDir === SortDirection.desc) {\n      return `sort-btn sort-desc ${this.sortDescendingIcon ?? 'datatable-icon-down'}`;\n    } else {\n      return `sort-btn ${this.sortUnsetIcon ?? 'datatable-icon-sort-unset'}`;\n    }\n  }\n  onMousedown(event) {\n    const isMouse = event instanceof MouseEvent;\n    const initialWidth = this.element.clientWidth;\n    const {\n      screenX\n    } = getPositionFromEvent(event);\n    event.stopPropagation();\n    const mouseup = fromEvent(document, isMouse ? 'mouseup' : 'touchend');\n    this.subscription = mouseup.subscribe(() => this.onMouseup());\n    const mouseMoveSub = fromEvent(document, isMouse ? 'mousemove' : 'touchmove').pipe(takeUntil$1(mouseup)).subscribe(e => this.move(e, initialWidth, screenX));\n    this.subscription.add(mouseMoveSub);\n  }\n  onMouseup() {\n    if (this.subscription && !this.subscription.closed) {\n      this.destroySubscription();\n      this.resize.emit({\n        width: this.element.clientWidth,\n        column: this.column\n      });\n    }\n  }\n  move(event, initialWidth, mouseDownScreenX) {\n    const movementX = getPositionFromEvent(event).screenX - mouseDownScreenX;\n    const newWidth = initialWidth + movementX;\n    this.resizing.emit({\n      width: newWidth,\n      column: this.column\n    });\n  }\n  destroySubscription() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      this.subscription = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function DataTableHeaderCellComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableHeaderCellComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableHeaderCellComponent,\n      selectors: [[\"datatable-header-cell\"]],\n      hostAttrs: [1, \"datatable-header-cell\"],\n      hostVars: 13,\n      hostBindings: function DataTableHeaderCellComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"contextmenu\", function DataTableHeaderCellComponent_contextmenu_HostBindingHandler($event) {\n            return ctx.onContextmenu($event);\n          })(\"keydown.enter\", function DataTableHeaderCellComponent_keydown_enter_HostBindingHandler() {\n            return ctx.enter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"tabIndex\", ctx.tabindex);\n          i0.ɵɵattribute(\"resizeable\", ctx.column.resizeable)(\"title\", ctx.name);\n          i0.ɵɵclassMap(ctx.columnCssClasses);\n          i0.ɵɵstyleProp(\"height\", ctx.headerHeight, \"px\")(\"min-width\", ctx.minWidth, \"px\")(\"max-width\", ctx.maxWidth, \"px\")(\"width\", ctx.width, \"px\");\n        }\n      },\n      inputs: {\n        sortType: \"sortType\",\n        sortAscendingIcon: \"sortAscendingIcon\",\n        sortDescendingIcon: \"sortDescendingIcon\",\n        sortUnsetIcon: \"sortUnsetIcon\",\n        isTarget: \"isTarget\",\n        targetMarkerTemplate: \"targetMarkerTemplate\",\n        targetMarkerContext: \"targetMarkerContext\",\n        enableClearingSortState: \"enableClearingSortState\",\n        allRowsSelected: \"allRowsSelected\",\n        selectionType: \"selectionType\",\n        column: \"column\",\n        headerHeight: \"headerHeight\",\n        sorts: \"sorts\"\n      },\n      outputs: {\n        sort: \"sort\",\n        select: \"select\",\n        columnContextmenu: \"columnContextmenu\",\n        resize: \"resize\",\n        resizing: \"resizing\"\n      },\n      decls: 7,\n      vars: 6,\n      consts: [[1, \"datatable-header-cell-template-wrap\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"datatable-checkbox\"], [1, \"datatable-header-cell-wrapper\"], [3, \"click\"], [1, \"resize-handle\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"datatable-header-cell-label\", \"draggable\", 3, \"click\"], [1, \"resize-handle\", 3, \"mousedown\", \"touchstart\"]],\n      template: function DataTableHeaderCellComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵconditionalCreate(1, DataTableHeaderCellComponent_Conditional_1_Template, 1, 2, null, 1);\n          i0.ɵɵconditionalCreate(2, DataTableHeaderCellComponent_Conditional_2_Template, 2, 1, \"label\", 2);\n          i0.ɵɵconditionalCreate(3, DataTableHeaderCellComponent_Conditional_3_Template, 1, 2, null, 1)(4, DataTableHeaderCellComponent_Conditional_4_Template, 3, 1, \"span\", 3);\n          i0.ɵɵelementStart(5, \"span\", 4);\n          i0.ɵɵlistener(\"click\", function DataTableHeaderCellComponent_Template_span_click_5_listener() {\n            return ctx.onSort();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵconditionalCreate(6, DataTableHeaderCellComponent_Conditional_6_Template, 1, 0, \"span\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isTarget ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isCheckboxable ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.column.headerTemplate ? 3 : 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.sortClass);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.column.resizeable ? 6 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      styles: [\"[_nghost-%COMP%]{overflow-x:hidden;vertical-align:top;line-height:1.625;position:relative;display:inline-block}[_nghost-%COMP%]:focus{outline:none}ngx-datatable.fixed-header   [_nghost-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.sortable[_nghost-%COMP%]   .datatable-header-cell-wrapper[_ngcontent-%COMP%]{cursor:pointer}.longpress[_nghost-%COMP%]   .datatable-header-cell-wrapper[_ngcontent-%COMP%]{cursor:move}.datatable-header-cell-template-wrap[_ngcontent-%COMP%]{height:inherit}.sort-btn[_ngcontent-%COMP%]{line-height:100%;vertical-align:middle;display:inline-block;cursor:pointer}.resize-handle[_ngcontent-%COMP%], .resize-handle--not-resizable[_ngcontent-%COMP%]{display:inline-block;position:absolute;right:0;top:0;bottom:0;width:5px;padding:0 4px;visibility:hidden}.resize-handle[_ngcontent-%COMP%]{cursor:ew-resize}.resizeable[_nghost-%COMP%]:hover   .resize-handle[_ngcontent-%COMP%]{visibility:visible}@media (hover: none){[_nghost-%COMP%]{touch-action:none}[_nghost-%COMP%]   .resize-handle[_ngcontent-%COMP%]{visibility:visible}[_nghost-%COMP%]   .datatable-header-cell-label.draggable[_ngcontent-%COMP%]{-webkit-user-select:none;user-select:none}}.resize-handle--not-resizable   [_nghost-%COMP%]:hover{visibility:visible}[_nghost-%COMP%]  .targetMarker{position:absolute;top:0;bottom:0}[_nghost-%COMP%]  .targetMarker.dragFromLeft{right:0}[_nghost-%COMP%]  .targetMarker.dragFromRight{left:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableHeaderCellComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-header-cell',\n      template: `\n    <div class=\"datatable-header-cell-template-wrap\">\n      @if (isTarget) {\n      <ng-template\n        [ngTemplateOutlet]=\"targetMarkerTemplate!\"\n        [ngTemplateOutletContext]=\"targetMarkerContext\"\n      >\n      </ng-template>\n      } @if (isCheckboxable) {\n      <label class=\"datatable-checkbox\">\n        <input type=\"checkbox\" [checked]=\"allRowsSelected\" (change)=\"select.emit()\" />\n      </label>\n      } @if (column.headerTemplate) {\n      <ng-template\n        [ngTemplateOutlet]=\"column.headerTemplate\"\n        [ngTemplateOutletContext]=\"cellContext\"\n      >\n      </ng-template>\n      } @else {\n      <span class=\"datatable-header-cell-wrapper\">\n        <span class=\"datatable-header-cell-label draggable\" (click)=\"onSort()\">\n          {{ name }}\n        </span>\n      </span>\n      }\n      <span (click)=\"onSort()\" [class]=\"sortClass\"> </span>\n    </div>\n    @if (column.resizeable) {\n    <span\n      class=\"resize-handle\"\n      (mousedown)=\"onMousedown($event)\"\n      (touchstart)=\"onMousedown($event)\"\n    ></span>\n    }\n  `,\n      host: {\n        'class': 'datatable-header-cell',\n        '[attr.resizeable]': 'column.resizeable'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgTemplateOutlet],\n      styles: [\":host{overflow-x:hidden;vertical-align:top;line-height:1.625;position:relative;display:inline-block}:host:focus{outline:none}:host-context(ngx-datatable.fixed-header) :host{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:host.sortable .datatable-header-cell-wrapper{cursor:pointer}:host.longpress .datatable-header-cell-wrapper{cursor:move}.datatable-header-cell-template-wrap{height:inherit}.sort-btn{line-height:100%;vertical-align:middle;display:inline-block;cursor:pointer}.resize-handle,.resize-handle--not-resizable{display:inline-block;position:absolute;right:0;top:0;bottom:0;width:5px;padding:0 4px;visibility:hidden}.resize-handle{cursor:ew-resize}:host(.resizeable:hover) .resize-handle{visibility:visible}@media (hover: none){:host{touch-action:none}:host .resize-handle{visibility:visible}:host .datatable-header-cell-label.draggable{-webkit-user-select:none;user-select:none}}.resize-handle--not-resizable :host(:hover){visibility:visible}:host::ng-deep .targetMarker{position:absolute;top:0;bottom:0}:host::ng-deep .targetMarker.dragFromLeft{right:0}:host::ng-deep .targetMarker.dragFromRight{left:0}\\n\"]\n    }]\n  }], () => [], {\n    sortType: [{\n      type: Input\n    }],\n    sortAscendingIcon: [{\n      type: Input\n    }],\n    sortDescendingIcon: [{\n      type: Input\n    }],\n    sortUnsetIcon: [{\n      type: Input\n    }],\n    isTarget: [{\n      type: Input\n    }],\n    targetMarkerTemplate: [{\n      type: Input\n    }],\n    targetMarkerContext: [{\n      type: Input\n    }],\n    enableClearingSortState: [{\n      type: Input\n    }],\n    allRowsSelected: [{\n      type: Input\n    }],\n    selectionType: [{\n      type: Input\n    }],\n    column: [{\n      type: Input\n    }],\n    headerHeight: [{\n      type: HostBinding,\n      args: ['style.height.px']\n    }, {\n      type: Input\n    }],\n    sorts: [{\n      type: Input\n    }],\n    sort: [{\n      type: Output\n    }],\n    select: [{\n      type: Output\n    }],\n    columnContextmenu: [{\n      type: Output\n    }],\n    resize: [{\n      type: Output\n    }],\n    resizing: [{\n      type: Output\n    }],\n    columnCssClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    name: [{\n      type: HostBinding,\n      args: ['attr.title']\n    }],\n    minWidth: [{\n      type: HostBinding,\n      args: ['style.minWidth.px']\n    }],\n    maxWidth: [{\n      type: HostBinding,\n      args: ['style.maxWidth.px']\n    }],\n    width: [{\n      type: HostBinding,\n      args: ['style.width.px']\n    }],\n    tabindex: [{\n      type: HostBinding,\n      args: ['tabindex']\n    }],\n    onContextmenu: [{\n      type: HostListener,\n      args: ['contextmenu', ['$event']]\n    }],\n    enter: [{\n      type: HostListener,\n      args: ['keydown.enter']\n    }]\n  });\n})();\nclass OrderableDirective {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.reorder = new EventEmitter();\n    this.targetChanged = new EventEmitter();\n    this.differ = inject(KeyValueDiffers).find({}).create();\n  }\n  ngAfterContentInit() {\n    // HACK: Investigate Better Way\n    this.updateSubscriptions();\n    this.draggables.changes.subscribe(this.updateSubscriptions.bind(this));\n  }\n  ngOnDestroy() {\n    this.draggables.forEach(d => {\n      d.dragStart.unsubscribe();\n      d.dragging.unsubscribe();\n      d.dragEnd.unsubscribe();\n    });\n  }\n  updateSubscriptions() {\n    const diffs = this.differ.diff(this.createMapDiffs());\n    if (diffs) {\n      const subscribe = record => {\n        unsubscribe(record);\n        const {\n          currentValue\n        } = record;\n        if (currentValue) {\n          currentValue.dragStart.subscribe(this.onDragStart.bind(this));\n          currentValue.dragging.subscribe(this.onDragging.bind(this));\n          currentValue.dragEnd.subscribe(this.onDragEnd.bind(this));\n        }\n      };\n      const unsubscribe = ({\n        previousValue\n      }) => {\n        if (previousValue) {\n          previousValue.dragStart.unsubscribe();\n          previousValue.dragging.unsubscribe();\n          previousValue.dragEnd.unsubscribe();\n        }\n      };\n      diffs.forEachAddedItem(subscribe);\n      // diffs.forEachChangedItem(subscribe.bind(this));\n      diffs.forEachRemovedItem(unsubscribe);\n    }\n  }\n  onDragStart() {\n    this.positions = {};\n    let i = 0;\n    for (const dragger of this.draggables.toArray()) {\n      const elm = dragger.element;\n      const left = parseInt(elm.offsetLeft.toString(), 0);\n      this.positions[dragger.dragModel.$$id] = {\n        left,\n        right: left + parseInt(elm.offsetWidth.toString(), 0),\n        index: i++,\n        element: elm\n      };\n    }\n  }\n  onDragging({\n    element,\n    model,\n    event\n  }) {\n    const prevPos = this.positions[model.$$id];\n    const target = this.isTarget(model, event);\n    if (target) {\n      if (this.lastDraggingIndex !== target.i) {\n        this.targetChanged.emit({\n          prevIndex: this.lastDraggingIndex,\n          newIndex: target.i,\n          initialIndex: prevPos.index\n        });\n        this.lastDraggingIndex = target.i;\n      }\n    } else if (this.lastDraggingIndex !== prevPos.index) {\n      this.targetChanged.emit({\n        prevIndex: this.lastDraggingIndex,\n        initialIndex: prevPos.index\n      });\n      this.lastDraggingIndex = prevPos.index;\n    }\n  }\n  onDragEnd({\n    element,\n    model,\n    event\n  }) {\n    const prevPos = this.positions[model.$$id];\n    const target = this.isTarget(model, event);\n    if (target) {\n      this.reorder.emit({\n        prevValue: prevPos.index,\n        newValue: target.i,\n        column: model\n      });\n    }\n    this.lastDraggingIndex = undefined;\n    element.style.left = 'auto';\n  }\n  isTarget(model, event) {\n    let i = 0;\n    const {\n      clientX,\n      clientY\n    } = getPositionFromEvent(event);\n    const targets = this.document.elementsFromPoint(clientX, clientY);\n    for (const id in this.positions) {\n      // current column position which throws event.\n      const pos = this.positions[id];\n      // since we drag the inner span, we need to find it in the elements at the cursor\n      if (model.$$id !== id && targets.find(el => el === pos.element)) {\n        return {\n          pos,\n          i\n        };\n      }\n      i++;\n    }\n  }\n  createMapDiffs() {\n    return this.draggables.toArray().reduce((acc, curr) => {\n      acc[curr.dragModel.$$id] = curr;\n      return acc;\n    }, {});\n  }\n  static {\n    this.ɵfac = function OrderableDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrderableDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OrderableDirective,\n      selectors: [[\"\", \"orderable\", \"\"]],\n      contentQueries: function OrderableDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DraggableDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.draggables = _t);\n        }\n      },\n      outputs: {\n        reorder: \"reorder\",\n        targetChanged: \"targetChanged\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OrderableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[orderable]'\n    }]\n  }], null, {\n    reorder: [{\n      type: Output\n    }],\n    targetChanged: [{\n      type: Output\n    }],\n    draggables: [{\n      type: ContentChildren,\n      args: [DraggableDirective, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass DataTableHeaderComponent {\n  constructor() {\n    this.cd = inject(ChangeDetectorRef);\n    this.scrollbarHelper = inject(ScrollbarHelper);\n    this.enableClearingSortState = false;\n    this.verticalScrollVisible = false;\n    this.sort = new EventEmitter();\n    this.reorder = new EventEmitter();\n    this.resize = new EventEmitter();\n    this.resizing = new EventEmitter();\n    this.select = new EventEmitter();\n    this.columnContextmenu = new EventEmitter(false);\n    this._columnGroupWidths = {\n      total: 100\n    };\n    this._styleByGroup = {\n      left: {},\n      center: {},\n      right: {}\n    };\n    this.destroyed = false;\n  }\n  set innerWidth(val) {\n    this._innerWidth = val;\n    setTimeout(() => {\n      if (this._columns) {\n        const colByPin = columnsByPin(this._columns);\n        this._columnGroupWidths = columnGroupWidths(colByPin, this._columns);\n        this.setStylesByGroup();\n      }\n    });\n  }\n  get innerWidth() {\n    return this._innerWidth;\n  }\n  set headerHeight(val) {\n    if (val !== 'auto') {\n      this._headerHeight = `${val}px`;\n    } else {\n      this._headerHeight = val;\n    }\n  }\n  get headerHeight() {\n    return this._headerHeight;\n  }\n  set columns(val) {\n    this._columns = val;\n    const colsByPin = columnsByPin(val);\n    this._columnsByPin = columnsByPinArr(val);\n    setTimeout(() => {\n      this._columnGroupWidths = columnGroupWidths(colsByPin, val);\n      this.setStylesByGroup();\n    });\n  }\n  get columns() {\n    return this._columns;\n  }\n  set offsetX(val) {\n    this._offsetX = val;\n    this.setStylesByGroup();\n  }\n  get offsetX() {\n    return this._offsetX;\n  }\n  ngOnChanges(changes) {\n    if (changes.verticalScrollVisible) {\n      this._styleByGroup.right = this.calcStylesByGroup('right');\n      if (!this.destroyed) {\n        this.cd.detectChanges();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed = true;\n  }\n  onLongPressStart({\n    event,\n    model\n  }) {\n    model.dragging = true;\n    this.dragEventTarget = event;\n  }\n  onLongPressEnd({\n    model\n  }) {\n    this.dragEventTarget = undefined;\n    // delay resetting so sort can be\n    // prevented if we were dragging\n    setTimeout(() => {\n      // datatable component creates copies from columns on reorder\n      // set dragging to false on new objects\n      const column = this._columns.find(c => c.$$id === model.$$id);\n      if (column && 'dragging' in column) {\n        column.dragging = false;\n      }\n    }, 5);\n  }\n  get headerWidth() {\n    if (this.scrollbarH) {\n      const width = this.verticalScrollVisible ? this.innerWidth - this.scrollbarHelper.width : this.innerWidth;\n      return width + 'px';\n    }\n    return '100%';\n  }\n  onColumnResized({\n    width,\n    column\n  }) {\n    this.resize.emit(this.makeResizeEvent(width, column));\n  }\n  onColumnResizing({\n    width,\n    column\n  }) {\n    this.resizing.emit(this.makeResizeEvent(width, column));\n  }\n  makeResizeEvent(width, column) {\n    if (column.minWidth && width <= column.minWidth) {\n      width = column.minWidth;\n    } else if (column.maxWidth && width >= column.maxWidth) {\n      width = column.maxWidth;\n    }\n    return {\n      column,\n      prevValue: column.width,\n      newValue: width\n    };\n  }\n  onColumnReordered(event) {\n    const column = this.getColumn(event.newValue);\n    column.isTarget = false;\n    column.targetMarkerContext = undefined;\n    this.reorder.emit(event);\n  }\n  onTargetChanged({\n    prevIndex,\n    newIndex,\n    initialIndex\n  }) {\n    if (prevIndex || prevIndex === 0) {\n      const oldColumn = this.getColumn(prevIndex);\n      oldColumn.isTarget = false;\n      oldColumn.targetMarkerContext = undefined;\n    }\n    if (newIndex || newIndex === 0) {\n      const newColumn = this.getColumn(newIndex);\n      newColumn.isTarget = true;\n      if (initialIndex !== newIndex) {\n        newColumn.targetMarkerContext = {\n          class: 'targetMarker '.concat(initialIndex > newIndex ? 'dragFromRight' : 'dragFromLeft')\n        };\n      }\n    }\n  }\n  getColumn(index) {\n    const leftColumnCount = this._columnsByPin[0].columns.length;\n    if (index < leftColumnCount) {\n      return this._columnsByPin[0].columns[index];\n    }\n    const centerColumnCount = this._columnsByPin[1].columns.length;\n    if (index < leftColumnCount + centerColumnCount) {\n      return this._columnsByPin[1].columns[index - leftColumnCount];\n    }\n    return this._columnsByPin[2].columns[index - leftColumnCount - centerColumnCount];\n  }\n  onSort({\n    column,\n    prevValue,\n    newValue\n  }) {\n    // if we are dragging don't sort!\n    if (column.dragging) {\n      return;\n    }\n    const sorts = this.calcNewSorts(column, prevValue, newValue);\n    this.sort.emit({\n      sorts,\n      column,\n      prevValue,\n      newValue\n    });\n  }\n  calcNewSorts(column, prevValue, newValue) {\n    let idx = 0;\n    if (!this.sorts) {\n      this.sorts = [];\n    }\n    const sorts = this.sorts.map((s, i) => {\n      s = {\n        ...s\n      };\n      if (s.prop === column.prop) {\n        idx = i;\n      }\n      return s;\n    });\n    if (newValue === undefined) {\n      sorts.splice(idx, 1);\n    } else if (prevValue) {\n      sorts[idx].dir = newValue;\n    } else {\n      if (this.sortType === SortType.single) {\n        sorts.splice(0, this.sorts.length);\n      }\n      sorts.push({\n        dir: newValue,\n        prop: column.prop\n      });\n    }\n    return sorts;\n  }\n  setStylesByGroup() {\n    this._styleByGroup.left = this.calcStylesByGroup('left');\n    this._styleByGroup.center = this.calcStylesByGroup('center');\n    this._styleByGroup.right = this.calcStylesByGroup('right');\n    if (!this.destroyed) {\n      this.cd.detectChanges();\n    }\n  }\n  calcStylesByGroup(group) {\n    const widths = this._columnGroupWidths;\n    if (group === 'center') {\n      return {\n        transform: `translateX(${this.offsetX * -1}px)`,\n        width: `${widths[group]}px`,\n        willChange: 'transform'\n      };\n    }\n    return {\n      width: `${widths[group]}px`\n    };\n  }\n  static {\n    this.ɵfac = function DataTableHeaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableHeaderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableHeaderComponent,\n      selectors: [[\"datatable-header\"]],\n      hostAttrs: [1, \"datatable-header\"],\n      hostVars: 4,\n      hostBindings: function DataTableHeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.headerHeight)(\"width\", ctx.headerWidth);\n        }\n      },\n      inputs: {\n        sortAscendingIcon: \"sortAscendingIcon\",\n        sortDescendingIcon: \"sortDescendingIcon\",\n        sortUnsetIcon: \"sortUnsetIcon\",\n        scrollbarH: \"scrollbarH\",\n        dealsWithGroup: \"dealsWithGroup\",\n        targetMarkerTemplate: \"targetMarkerTemplate\",\n        enableClearingSortState: \"enableClearingSortState\",\n        innerWidth: \"innerWidth\",\n        sorts: \"sorts\",\n        sortType: \"sortType\",\n        allRowsSelected: \"allRowsSelected\",\n        selectionType: \"selectionType\",\n        reorderable: \"reorderable\",\n        verticalScrollVisible: \"verticalScrollVisible\",\n        headerHeight: \"headerHeight\",\n        columns: \"columns\",\n        offsetX: \"offsetX\"\n      },\n      outputs: {\n        sort: \"sort\",\n        reorder: \"reorder\",\n        resize: \"resize\",\n        resizing: \"resizing\",\n        select: \"select\",\n        columnContextmenu: \"columnContextmenu\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"role\", \"row\", \"orderable\", \"\", 1, \"datatable-header-inner\", 3, \"reorder\", \"targetChanged\"], [1, \"datatable-row-group\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"columnheader\", \"long-press\", \"\", \"draggable\", \"\", 3, \"pressModel\", \"pressEnabled\", \"dragX\", \"dragY\", \"dragModel\", \"dragEventTarget\", \"headerHeight\", \"isTarget\", \"targetMarkerTemplate\", \"targetMarkerContext\", \"column\", \"sortType\", \"sorts\", \"selectionType\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"enableClearingSortState\"], [\"role\", \"columnheader\", \"long-press\", \"\", \"draggable\", \"\", 3, \"resize\", \"resizing\", \"longPressStart\", \"longPressEnd\", \"sort\", \"select\", \"columnContextmenu\", \"pressModel\", \"pressEnabled\", \"dragX\", \"dragY\", \"dragModel\", \"dragEventTarget\", \"headerHeight\", \"isTarget\", \"targetMarkerTemplate\", \"targetMarkerContext\", \"column\", \"sortType\", \"sorts\", \"selectionType\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"enableClearingSortState\"]],\n      template: function DataTableHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"reorder\", function DataTableHeaderComponent_Template_div_reorder_0_listener($event) {\n            return ctx.onColumnReordered($event);\n          })(\"targetChanged\", function DataTableHeaderComponent_Template_div_targetChanged_0_listener($event) {\n            return ctx.onTargetChanged($event);\n          });\n          i0.ɵɵrepeaterCreate(1, DataTableHeaderComponent_For_2_Template, 1, 1, null, null, _forTrack0);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", ctx._columnGroupWidths.total, \"px\");\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx._columnsByPin);\n        }\n      },\n      dependencies: [OrderableDirective, NgStyle, DataTableHeaderCellComponent, LongPressDirective, DraggableDirective, NgClass],\n      styles: [\"[_nghost-%COMP%]{display:block;overflow:hidden}.datatable-header-inner[_ngcontent-%COMP%]{display:flex}ngx-datatable.fixed-header[_nghost-%COMP%]   .datatable-header-inner[_ngcontent-%COMP%], ngx-datatable.fixed-header   [_nghost-%COMP%]   .datatable-header-inner[_ngcontent-%COMP%]{white-space:nowrap}.datatable-row-group[_ngcontent-%COMP%]{display:flex}.datatable-row-left[_ngcontent-%COMP%], .datatable-row-right[_ngcontent-%COMP%]{position:sticky;z-index:9}.datatable-row-left[_ngcontent-%COMP%]{left:0}.datatable-row-right[_ngcontent-%COMP%]{right:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableHeaderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-header',\n      template: `\n    <div\n      role=\"row\"\n      orderable\n      (reorder)=\"onColumnReordered($event)\"\n      (targetChanged)=\"onTargetChanged($event)\"\n      [style.width.px]=\"_columnGroupWidths.total\"\n      class=\"datatable-header-inner\"\n    >\n      @for (colGroup of _columnsByPin; track colGroup.type) { @if (colGroup.columns.length) {\n      <div\n        class=\"datatable-row-group\"\n        [ngClass]=\"'datatable-row-' + colGroup.type\"\n        [ngStyle]=\"_styleByGroup[colGroup.type]\"\n      >\n        @for (column of colGroup.columns; track column.$$id) {\n        <datatable-header-cell\n          role=\"columnheader\"\n          (resize)=\"onColumnResized($event)\"\n          (resizing)=\"onColumnResizing($event)\"\n          long-press\n          [pressModel]=\"column\"\n          [pressEnabled]=\"reorderable && column.draggable\"\n          (longPressStart)=\"onLongPressStart($event)\"\n          (longPressEnd)=\"onLongPressEnd($event)\"\n          draggable\n          [dragX]=\"reorderable && column.draggable && column.dragging\"\n          [dragY]=\"false\"\n          [dragModel]=\"column\"\n          [dragEventTarget]=\"dragEventTarget\"\n          [headerHeight]=\"headerHeight\"\n          [isTarget]=\"column.isTarget\"\n          [targetMarkerTemplate]=\"targetMarkerTemplate\"\n          [targetMarkerContext]=\"column.targetMarkerContext\"\n          [column]=\"column\"\n          [sortType]=\"sortType\"\n          [sorts]=\"sorts\"\n          [selectionType]=\"selectionType\"\n          [sortAscendingIcon]=\"sortAscendingIcon\"\n          [sortDescendingIcon]=\"sortDescendingIcon\"\n          [sortUnsetIcon]=\"sortUnsetIcon\"\n          [allRowsSelected]=\"allRowsSelected\"\n          [enableClearingSortState]=\"enableClearingSortState\"\n          (sort)=\"onSort($event)\"\n          (select)=\"select.emit($event)\"\n          (columnContextmenu)=\"columnContextmenu.emit($event)\"\n        >\n        </datatable-header-cell>\n        }\n      </div>\n      } }\n    </div>\n  `,\n      host: {\n        class: 'datatable-header'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [OrderableDirective, NgStyle, DataTableHeaderCellComponent, LongPressDirective, DraggableDirective, NgClass],\n      styles: [\":host{display:block;overflow:hidden}.datatable-header-inner{display:flex}:host-context(ngx-datatable.fixed-header) .datatable-header-inner{white-space:nowrap}.datatable-row-group{display:flex}.datatable-row-left,.datatable-row-right{position:sticky;z-index:9}.datatable-row-left{left:0}.datatable-row-right{right:0}\\n\"]\n    }]\n  }], null, {\n    sortAscendingIcon: [{\n      type: Input\n    }],\n    sortDescendingIcon: [{\n      type: Input\n    }],\n    sortUnsetIcon: [{\n      type: Input\n    }],\n    scrollbarH: [{\n      type: Input\n    }],\n    dealsWithGroup: [{\n      type: Input\n    }],\n    targetMarkerTemplate: [{\n      type: Input\n    }],\n    enableClearingSortState: [{\n      type: Input\n    }],\n    innerWidth: [{\n      type: Input\n    }],\n    sorts: [{\n      type: Input\n    }],\n    sortType: [{\n      type: Input\n    }],\n    allRowsSelected: [{\n      type: Input\n    }],\n    selectionType: [{\n      type: Input\n    }],\n    reorderable: [{\n      type: Input\n    }],\n    verticalScrollVisible: [{\n      type: Input\n    }],\n    headerHeight: [{\n      type: HostBinding,\n      args: ['style.height']\n    }, {\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    offsetX: [{\n      type: Input\n    }],\n    sort: [{\n      type: Output\n    }],\n    reorder: [{\n      type: Output\n    }],\n    resize: [{\n      type: Output\n    }],\n    resizing: [{\n      type: Output\n    }],\n    select: [{\n      type: Output\n    }],\n    columnContextmenu: [{\n      type: Output\n    }],\n    headerWidth: [{\n      type: HostBinding,\n      args: ['style.width']\n    }]\n  });\n})();\n\n/**\n * Throttle a function\n */\nfunction throttle(func, wait, options) {\n  options = options || {};\n  let context;\n  let args;\n  let result;\n  let timeout = null;\n  let previous = 0;\n  function later() {\n    previous = options.leading === false ? 0 : +new Date();\n    timeout = null;\n    result = func.apply(context, args);\n  }\n  return function () {\n    const now = +new Date();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0) {\n      clearTimeout(timeout);\n      timeout = null;\n      previous = now;\n      result = func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n/**\n * Throttle decorator\n *\n *  class MyClass {\n *    throttleable(10)\n *    myFn() { ... }\n *  }\n */\nfunction throttleable(duration, options) {\n  return function innerDecorator(target, key, descriptor) {\n    return {\n      configurable: true,\n      enumerable: descriptor.enumerable,\n      get: function getter() {\n        Object.defineProperty(this, key, {\n          configurable: true,\n          enumerable: descriptor.enumerable,\n          value: throttle(descriptor.value, duration, options)\n        });\n        return target[key];\n      }\n    };\n  };\n}\n\n/**\n * Calculates the Total Flex Grow\n */\nfunction getTotalFlexGrow(columns) {\n  let totalFlexGrow = 0;\n  for (const c of columns) {\n    totalFlexGrow += c.flexGrow || 0;\n  }\n  return totalFlexGrow;\n}\n/**\n * Adjusts the column widths.\n * Inspired by: https://github.com/facebook/fixed-data-table/blob/master/src/FixedDataTableWidthHelper.js\n */\nfunction adjustColumnWidths(allColumns, expectedWidth) {\n  const columnsWidth = columnTotalWidth(allColumns);\n  const totalFlexGrow = getTotalFlexGrow(allColumns);\n  const colsByGroup = columnsByPin(allColumns);\n  if (columnsWidth !== expectedWidth) {\n    scaleColumns(colsByGroup, expectedWidth, totalFlexGrow);\n  }\n}\n/**\n * Resizes columns based on the flexGrow property, while respecting manually set widths\n */\nfunction scaleColumns(colsByGroup, maxWidth, totalFlexGrow) {\n  // calculate total width and flexgrow points for columns that can be resized\n  for (const column of Object.values(colsByGroup).flat()) {\n    if (column.$$oldWidth) {\n      // when manually resized, switch off auto-resize\n      column.canAutoResize = false;\n    }\n    if (!column.canAutoResize) {\n      maxWidth -= column.width;\n      totalFlexGrow -= column.flexGrow ? column.flexGrow : 0;\n    } else {\n      column.width = 0;\n    }\n  }\n  const hasMinWidth = {};\n  let remainingWidth = maxWidth;\n  // resize columns until no width is left to be distributed\n  do {\n    const widthPerFlexPoint = remainingWidth / totalFlexGrow;\n    remainingWidth = 0;\n    for (const column of Object.values(colsByGroup).flat()) {\n      // if the column can be resize and it hasn't reached its minimum width yet\n      if (column.canAutoResize && !hasMinWidth[column.prop]) {\n        const newWidth = column.width + column.flexGrow * widthPerFlexPoint;\n        if (column.minWidth !== undefined && newWidth < column.minWidth) {\n          remainingWidth += newWidth - column.minWidth;\n          column.width = column.minWidth;\n          hasMinWidth[column.prop] = true;\n        } else {\n          column.width = newWidth;\n        }\n      }\n    }\n  } while (remainingWidth !== 0);\n  // Adjust for any remaining offset in computed widths vs maxWidth\n  const columns = Object.values(colsByGroup).reduce((acc, col) => acc.concat(col), []);\n  const totalWidthAchieved = columns.reduce((acc, col) => acc + col.width, 0);\n  const delta = maxWidth - totalWidthAchieved;\n  if (delta === 0) {\n    return;\n  }\n  // adjust the first column that can be auto-resized respecting the min/max widths\n  for (const col of columns.filter(c => c.canAutoResize).sort((a, b) => a.width - b.width)) {\n    if (delta > 0 && (!col.maxWidth || col.width + delta <= col.maxWidth) || delta < 0 && (!col.minWidth || col.width + delta >= col.minWidth)) {\n      col.width += delta;\n      break;\n    }\n  }\n}\n/**\n * Forces the width of the columns to\n * distribute equally but overflowing when necessary\n *\n * Rules:\n *\n *  - If combined withs are less than the total width of the grid,\n *    proportion the widths given the min / max / normal widths to fill the width.\n *\n *  - If the combined widths, exceed the total width of the grid,\n *    use the standard widths.\n *\n *  - If a column is resized, it should always use that width\n *\n *  - The proportional widths should never fall below min size if specified.\n *\n *  - If the grid starts off small but then becomes greater than the size ( + / - )\n *    the width should use the original width; not the newly proportioned widths.\n */\nfunction forceFillColumnWidths(allColumns, expectedWidth, startIdx, allowBleed, defaultColWidth = 150, verticalScrollWidth = 0) {\n  const columnsToResize = allColumns.slice(startIdx + 1, allColumns.length).filter(c => c.canAutoResize !== false);\n  for (const column of columnsToResize) {\n    if (!column.$$oldWidth) {\n      column.$$oldWidth = column.width;\n    }\n  }\n  let additionWidthPerColumn = 0;\n  let exceedsWindow = false;\n  let contentWidth = getContentWidth(allColumns, defaultColWidth);\n  let remainingWidth = expectedWidth - contentWidth;\n  const initialRemainingWidth = remainingWidth;\n  const columnsProcessed = [];\n  const remainingWidthLimit = 1; // when to stop\n  // This loop takes care of the\n  do {\n    additionWidthPerColumn = remainingWidth / columnsToResize.length;\n    exceedsWindow = contentWidth >= expectedWidth;\n    for (const column of columnsToResize) {\n      // don't bleed if the initialRemainingWidth is same as verticalScrollWidth\n      if (exceedsWindow && allowBleed && initialRemainingWidth !== -1 * verticalScrollWidth) {\n        column.width = column.$$oldWidth || column.width || defaultColWidth;\n      } else {\n        const newSize = (column.width || defaultColWidth) + additionWidthPerColumn;\n        if (column.minWidth && newSize < column.minWidth) {\n          column.width = column.minWidth;\n          columnsProcessed.push(column);\n        } else if (column.maxWidth && newSize > column.maxWidth) {\n          column.width = column.maxWidth;\n          columnsProcessed.push(column);\n        } else {\n          column.width = newSize;\n        }\n      }\n      column.width = Math.max(0, column.width);\n    }\n    contentWidth = getContentWidth(allColumns, defaultColWidth);\n    remainingWidth = expectedWidth - contentWidth;\n    removeProcessedColumns(columnsToResize, columnsProcessed);\n  } while (remainingWidth > remainingWidthLimit && columnsToResize.length !== 0);\n  // reset so we don't have stale values\n  for (const column of columnsToResize) {\n    column.$$oldWidth = 0;\n  }\n}\n/**\n * Remove the processed columns from the current active columns.\n */\nfunction removeProcessedColumns(columnsToResize, columnsProcessed) {\n  for (const column of columnsProcessed) {\n    const index = columnsToResize.indexOf(column);\n    columnsToResize.splice(index, 1);\n  }\n}\n/**\n * Gets the width of the columns\n */\nfunction getContentWidth(allColumns, defaultColWidth = 150) {\n  let contentWidth = 0;\n  for (const column of allColumns) {\n    contentWidth += column.width || defaultColWidth;\n  }\n  return contentWidth;\n}\nclass DataTablePagerComponent {\n  constructor() {\n    this.dataTable = inject(DatatableComponent, {\n      optional: true\n    });\n    this.change = new EventEmitter();\n    this._count = 0;\n    this._page = 1;\n    this._size = 0;\n  }\n  get messages() {\n    return this.dataTable?.messages ?? {};\n  }\n  set size(val) {\n    this._size = val;\n    this.pages = this.calcPages();\n  }\n  get size() {\n    return this._size;\n  }\n  set count(val) {\n    this._count = val;\n    this.pages = this.calcPages();\n  }\n  get count() {\n    return this._count;\n  }\n  set page(val) {\n    this._page = val;\n    this.pages = this.calcPages();\n  }\n  get page() {\n    return this._page;\n  }\n  get totalPages() {\n    const count = this.size < 1 ? 1 : Math.ceil(this.count / this.size);\n    return Math.max(count || 0, 1);\n  }\n  canPrevious() {\n    return this.page > 1;\n  }\n  canNext() {\n    return this.page < this.totalPages;\n  }\n  prevPage() {\n    this.selectPage(this.page - 1);\n  }\n  nextPage() {\n    this.selectPage(this.page + 1);\n  }\n  selectPage(page) {\n    if (page > 0 && page <= this.totalPages && page !== this.page) {\n      this.page = page;\n      this.change.emit({\n        page\n      });\n    }\n  }\n  calcPages(page) {\n    const pages = [];\n    let startPage = 1;\n    let endPage = this.totalPages;\n    const maxSize = 5;\n    const isMaxSized = maxSize < this.totalPages;\n    page = page || this.page;\n    if (isMaxSized) {\n      startPage = page - Math.floor(maxSize / 2);\n      endPage = page + Math.floor(maxSize / 2);\n      if (startPage < 1) {\n        startPage = 1;\n        endPage = Math.min(startPage + maxSize - 1, this.totalPages);\n      } else if (endPage > this.totalPages) {\n        startPage = Math.max(this.totalPages - maxSize + 1, 1);\n        endPage = this.totalPages;\n      }\n    }\n    for (let num = startPage; num <= endPage; num++) {\n      pages.push({\n        number: num,\n        text: num\n      });\n    }\n    return pages;\n  }\n  static {\n    this.ɵfac = function DataTablePagerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTablePagerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTablePagerComponent,\n      selectors: [[\"datatable-pager\"]],\n      hostAttrs: [1, \"datatable-pager\"],\n      inputs: {\n        pagerLeftArrowIcon: \"pagerLeftArrowIcon\",\n        pagerRightArrowIcon: \"pagerRightArrowIcon\",\n        pagerPreviousIcon: \"pagerPreviousIcon\",\n        pagerNextIcon: \"pagerNextIcon\",\n        size: \"size\",\n        count: \"count\",\n        page: \"page\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      decls: 15,\n      vars: 20,\n      consts: [[1, \"pager\"], [\"role\", \"button\", 3, \"click\"], [1, \"pages\", 3, \"active\"], [1, \"pages\"]],\n      template: function DataTablePagerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵdomElementStart(0, \"ul\", 0)(1, \"li\")(2, \"a\", 1);\n          i0.ɵɵdomListener(\"click\", function DataTablePagerComponent_Template_a_click_2_listener() {\n            return ctx.selectPage(1);\n          });\n          i0.ɵɵdomElement(3, \"i\");\n          i0.ɵɵdomElementEnd()();\n          i0.ɵɵdomElementStart(4, \"li\")(5, \"a\", 1);\n          i0.ɵɵdomListener(\"click\", function DataTablePagerComponent_Template_a_click_5_listener() {\n            return ctx.prevPage();\n          });\n          i0.ɵɵdomElement(6, \"i\");\n          i0.ɵɵdomElementEnd()();\n          i0.ɵɵrepeaterCreate(7, DataTablePagerComponent_For_8_Template, 3, 4, \"li\", 2, _forTrack2);\n          i0.ɵɵdomElementStart(9, \"li\")(10, \"a\", 1);\n          i0.ɵɵdomListener(\"click\", function DataTablePagerComponent_Template_a_click_10_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵdomElement(11, \"i\");\n          i0.ɵɵdomElementEnd()();\n          i0.ɵɵdomElementStart(12, \"li\")(13, \"a\", 1);\n          i0.ɵɵdomListener(\"click\", function DataTablePagerComponent_Template_a_click_13_listener() {\n            return ctx.selectPage(ctx.totalPages);\n          });\n          i0.ɵɵdomElement(14, \"i\");\n          i0.ɵɵdomElementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"disabled\", !ctx.canPrevious());\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", ctx.messages.ariaFirstPageMessage ?? \"go to first page\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.pagerPreviousIcon ?? \"datatable-icon-prev\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"disabled\", !ctx.canPrevious());\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", ctx.messages.ariaPreviousPageMessage ?? \"go to previous page\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.pagerLeftArrowIcon ?? \"datatable-icon-left\");\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx.pages);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", !ctx.canNext());\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", ctx.messages.ariaNextPageMessage ?? \"go to next page\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.pagerRightArrowIcon ?? \"datatable-icon-right\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"disabled\", !ctx.canNext());\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", ctx.messages.ariaLastPageMessage ?? \"go to last page\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.pagerNextIcon ?? \"datatable-icon-skip\");\n        }\n      },\n      styles: [\"datatable-footer   .selected-count[_nghost-%COMP%]   .datatable-pager[_ngcontent-%COMP%], datatable-footer   .selected-count   [_nghost-%COMP%]   .datatable-pager[_ngcontent-%COMP%]{flex:1 1 60%}[_nghost-%COMP%]{flex:1 1 80%;text-align:right}.pager[_ngcontent-%COMP%], .pager[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:0;margin:0;display:inline-block;list-style:none}.pager[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .pager[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{outline:none}.pager[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:pointer;display:inline-block}.pager[_ngcontent-%COMP%]   li.disabled[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:not-allowed}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTablePagerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-pager',\n      template: `\n    <ul class=\"pager\">\n      <li [class.disabled]=\"!canPrevious()\">\n        <a\n          role=\"button\"\n          [attr.aria-label]=\"messages.ariaFirstPageMessage ?? 'go to first page'\"\n          (click)=\"selectPage(1)\"\n        >\n          <i class=\"{{ pagerPreviousIcon ?? 'datatable-icon-prev' }}\"></i>\n        </a>\n      </li>\n      <li [class.disabled]=\"!canPrevious()\">\n        <a\n          role=\"button\"\n          [attr.aria-label]=\"messages.ariaPreviousPageMessage ?? 'go to previous page'\"\n          (click)=\"prevPage()\"\n        >\n          <i class=\"{{ pagerLeftArrowIcon ?? 'datatable-icon-left' }}\"></i>\n        </a>\n      </li>\n      @for (pg of pages; track pg.number) {\n      <li\n        [attr.aria-label]=\"(messages.ariaPageNMessage ?? 'page') + ' ' + pg.number\"\n        class=\"pages\"\n        [class.active]=\"pg.number === page\"\n      >\n        <a role=\"button\" (click)=\"selectPage(pg.number)\">\n          {{ pg.text }}\n        </a>\n      </li>\n      }\n      <li [class.disabled]=\"!canNext()\">\n        <a\n          role=\"button\"\n          [attr.aria-label]=\"messages.ariaNextPageMessage ?? 'go to next page'\"\n          (click)=\"nextPage()\"\n        >\n          <i class=\"{{ pagerRightArrowIcon ?? 'datatable-icon-right' }}\"></i>\n        </a>\n      </li>\n      <li [class.disabled]=\"!canNext()\">\n        <a\n          role=\"button\"\n          [attr.aria-label]=\"messages.ariaLastPageMessage ?? 'go to last page'\"\n          (click)=\"selectPage(totalPages)\"\n        >\n          <i class=\"{{ pagerNextIcon ?? 'datatable-icon-skip' }}\"></i>\n        </a>\n      </li>\n    </ul>\n  `,\n      host: {\n        class: 'datatable-pager'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host-context(datatable-footer .selected-count) .datatable-pager{flex:1 1 60%}:host{flex:1 1 80%;text-align:right}.pager,.pager li{padding:0;margin:0;display:inline-block;list-style:none}.pager li,.pager li a{outline:none}.pager li a{cursor:pointer;display:inline-block}.pager li.disabled a{cursor:not-allowed}\\n\"]\n    }]\n  }], null, {\n    pagerLeftArrowIcon: [{\n      type: Input\n    }],\n    pagerRightArrowIcon: [{\n      type: Input\n    }],\n    pagerPreviousIcon: [{\n      type: Input\n    }],\n    pagerNextIcon: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    count: [{\n      type: Input\n    }],\n    page: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }]\n  });\n})();\nclass DataTableFooterComponent {\n  constructor() {\n    this.selectedCount = 0;\n    this.page = new EventEmitter();\n  }\n  get isVisible() {\n    return this.rowCount / this.pageSize > 1;\n  }\n  get curPage() {\n    return this.offset + 1;\n  }\n  static {\n    this.ɵfac = function DataTableFooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataTableFooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DataTableFooterComponent,\n      selectors: [[\"datatable-footer\"]],\n      hostAttrs: [1, \"datatable-footer\"],\n      inputs: {\n        footerHeight: \"footerHeight\",\n        rowCount: \"rowCount\",\n        pageSize: \"pageSize\",\n        offset: \"offset\",\n        pagerLeftArrowIcon: \"pagerLeftArrowIcon\",\n        pagerRightArrowIcon: \"pagerRightArrowIcon\",\n        pagerPreviousIcon: \"pagerPreviousIcon\",\n        pagerNextIcon: \"pagerNextIcon\",\n        totalMessage: \"totalMessage\",\n        footerTemplate: \"footerTemplate\",\n        selectedCount: \"selectedCount\",\n        selectedMessage: \"selectedMessage\"\n      },\n      outputs: {\n        page: \"page\"\n      },\n      decls: 3,\n      vars: 6,\n      consts: [[1, \"datatable-footer-inner\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"page-count\"], [3, \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"pagerNextIcon\", \"page\", \"size\", \"count\"], [3, \"change\", \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"pagerNextIcon\", \"page\", \"size\", \"count\"]],\n      template: function DataTableFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵconditionalCreate(1, DataTableFooterComponent_Conditional_1_Template, 1, 8, null, 1)(2, DataTableFooterComponent_Conditional_2_Template, 4, 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.footerHeight, \"px\");\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c11, ctx.selectedMessage));\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((ctx.footerTemplate == null ? null : ctx.footerTemplate.template) ? 1 : 2);\n        }\n      },\n      dependencies: [NgClass, NgTemplateOutlet, DataTablePagerComponent],\n      styles: [\"[_nghost-%COMP%]{display:block;width:100%;overflow:auto}.datatable-footer-inner[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.page-count[_ngcontent-%COMP%]{flex:1 1 20%}.selected-count[_ngcontent-%COMP%]   .page-count[_ngcontent-%COMP%]{flex:1 1 40%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-footer',\n      template: `\n    <div\n      class=\"datatable-footer-inner\"\n      [ngClass]=\"{ 'selected-count': selectedMessage }\"\n      [style.height.px]=\"footerHeight\"\n    >\n      @if (footerTemplate?.template) {\n      <ng-template\n        [ngTemplateOutlet]=\"footerTemplate!.template!\"\n        [ngTemplateOutletContext]=\"{\n          rowCount: rowCount,\n          pageSize: pageSize,\n          selectedCount: selectedCount,\n          curPage: curPage,\n          offset: offset\n        }\"\n      >\n      </ng-template>\n      } @else {\n      <div class=\"page-count\">\n        @if (selectedMessage) {\n        <span> {{ selectedCount?.toLocaleString() }} {{ selectedMessage }} / </span>\n        }\n        {{ rowCount?.toLocaleString() }} {{ totalMessage }}\n      </div>\n      @if (isVisible) {\n      <datatable-pager\n        [pagerLeftArrowIcon]=\"pagerLeftArrowIcon\"\n        [pagerRightArrowIcon]=\"pagerRightArrowIcon\"\n        [pagerPreviousIcon]=\"pagerPreviousIcon\"\n        [pagerNextIcon]=\"pagerNextIcon\"\n        [page]=\"curPage\"\n        [size]=\"pageSize\"\n        [count]=\"rowCount\"\n        (change)=\"page.emit($event)\"\n      >\n      </datatable-pager>\n      } }\n    </div>\n  `,\n      host: {\n        class: 'datatable-footer'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgClass, NgTemplateOutlet, DataTablePagerComponent],\n      styles: [\":host{display:block;width:100%;overflow:auto}.datatable-footer-inner{display:flex;align-items:center;width:100%}.page-count{flex:1 1 20%}.selected-count .page-count{flex:1 1 40%}\\n\"]\n    }]\n  }], null, {\n    footerHeight: [{\n      type: Input\n    }],\n    rowCount: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    offset: [{\n      type: Input\n    }],\n    pagerLeftArrowIcon: [{\n      type: Input\n    }],\n    pagerRightArrowIcon: [{\n      type: Input\n    }],\n    pagerPreviousIcon: [{\n      type: Input\n    }],\n    pagerNextIcon: [{\n      type: Input\n    }],\n    totalMessage: [{\n      type: Input\n    }],\n    footerTemplate: [{\n      type: Input\n    }],\n    selectedCount: [{\n      type: Input\n    }],\n    selectedMessage: [{\n      type: Input\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Visibility Observer Directive\n *\n * Usage:\n *\n * \t\t<div\n * \t\t\tvisibilityObserver\n * \t\t\t(visible)=\"onVisible($event)\">\n * \t\t</div>\n *\n */\nclass VisibilityDirective {\n  constructor() {\n    this.element = inject(ElementRef);\n    this.zone = inject(NgZone);\n    this.isVisible = false;\n    this.visible = new EventEmitter();\n  }\n  ngOnInit() {\n    this.runCheck();\n  }\n  ngOnDestroy() {\n    clearTimeout(this.timeout);\n  }\n  onVisibilityChange() {\n    // trigger zone recalc for columns\n    this.zone.run(() => {\n      this.isVisible = true;\n      this.visible.emit(true);\n    });\n  }\n  runCheck() {\n    const check = () => {\n      // https://davidwalsh.name/offsetheight-visibility\n      const {\n        offsetHeight,\n        offsetWidth\n      } = this.element.nativeElement;\n      if (offsetHeight && offsetWidth) {\n        clearTimeout(this.timeout);\n        this.onVisibilityChange();\n      } else {\n        clearTimeout(this.timeout);\n        this.zone.runOutsideAngular(() => {\n          this.timeout = setTimeout(() => check(), 50);\n        });\n      }\n    };\n    this.timeout = setTimeout(() => check());\n  }\n  static {\n    this.ɵfac = function VisibilityDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VisibilityDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: VisibilityDirective,\n      selectors: [[\"\", \"visibilityObserver\", \"\"]],\n      hostVars: 2,\n      hostBindings: function VisibilityDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"visible\", ctx.isVisible);\n        }\n      },\n      outputs: {\n        visible: \"visible\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VisibilityDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[visibilityObserver]'\n    }]\n  }], null, {\n    isVisible: [{\n      type: HostBinding,\n      args: ['class.visible']\n    }],\n    visible: [{\n      type: Output\n    }]\n  });\n})();\nclass ProgressBarComponent {\n  static {\n    this.ɵfac = function ProgressBarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgressBarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ProgressBarComponent,\n      selectors: [[\"datatable-progress\"]],\n      decls: 3,\n      vars: 0,\n      consts: [[\"role\", \"progressbar\", 1, \"progress-linear\"], [1, \"container\"], [1, \"bar\"]],\n      template: function ProgressBarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵdomElementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵdomElement(2, \"div\", 2);\n          i0.ɵɵdomElementEnd()();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressBarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'datatable-progress',\n      template: `\n    <div class=\"progress-linear\" role=\"progressbar\">\n      <div class=\"container\">\n        <div class=\"bar\"></div>\n      </div>\n    </div>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n\n/**\n * Converts strings from something to camel case\n * http://stackoverflow.com/questions/10425287/convert-dash-separated-string-to-camelcase\n */\nfunction camelCase(str) {\n  // Replace special characters with a space\n  str = str.replace(/[^a-zA-Z0-9 ]/g, ' ');\n  // put a space before an uppercase letter\n  str = str.replace(/([a-z](?=[A-Z]))/g, '$1 ');\n  // Lower case first character and some other stuff\n  str = str.replace(/([^a-zA-Z0-9 ])|^[0-9]+/g, '').trim().toLowerCase();\n  // uppercase characters preceded by a space or number\n  str = str.replace(/([ 0-9]+)([a-zA-Z])/g, function (a, b, c) {\n    return b.trim() + c.toUpperCase();\n  });\n  return str;\n}\n/**\n * Converts strings from camel case to words\n * http://stackoverflow.com/questions/7225407/convert-camelcasetext-to-camel-case-text\n */\nfunction deCamelCase(str) {\n  return str.replace(/([A-Z])/g, match => ` ${match}`).replace(/^./, match => match.toUpperCase());\n}\n\n/**\n * Creates a unique object id.\n * http://stackoverflow.com/questions/6248666/how-to-generate-short-uid-like-ax4j9z-in-js\n */\nfunction id() {\n  return ('0000' + (Math.random() * Math.pow(36, 4) << 0).toString(36)).slice(-4);\n}\nfunction toInternalColumn(columns, defaultColumnWidth = 150) {\n  let hasTreeColumn = false;\n  // TS fails to infer the type here.\n  return columns.map(column => {\n    const prop = column.prop ?? (column.name ? camelCase(column.name) : undefined);\n    // Only one column should hold the tree view,\n    // Thus if multiple columns are provided with\n    // isTreeColumn as true, we take only the first one\n    const isTreeColumn = !!column.isTreeColumn && !hasTreeColumn;\n    hasTreeColumn = hasTreeColumn || isTreeColumn;\n    // TODO: add check if prop or name is provided if sorting is enabled.\n    return {\n      ...column,\n      $$id: id(),\n      $$valueGetter: getterForProp(prop),\n      prop,\n      name: column.name ?? (prop ? deCamelCase(String(prop)) : ''),\n      resizeable: column.resizeable ?? true,\n      sortable: column.sortable ?? true,\n      comparator: column.comparator ?? orderByComparator,\n      draggable: column.draggable ?? true,\n      canAutoResize: column.canAutoResize ?? true,\n      width: column.width ?? defaultColumnWidth,\n      isTreeColumn,\n      // in case of the directive, those are getters, so call them explicitly.\n      headerTemplate: column.headerTemplate,\n      cellTemplate: column.cellTemplate,\n      summaryTemplate: column.summaryTemplate,\n      ghostCellTemplate: column.ghostCellTemplate\n    }; // TS cannot cast here\n  });\n}\nfunction isNullOrUndefined(value) {\n  return value === null || value === undefined;\n}\nconst NGX_DATATABLE_CONFIG = new InjectionToken('ngx-datatable.config');\n/**\n * Provides a global configuration for ngx-datatable.\n *\n * @param overrides The overrides of the table configuration.\n */\nfunction providedNgxDatatableConfig(overrides) {\n  return {\n    provide: NGX_DATATABLE_CONFIG,\n    useValue: overrides\n  };\n}\nclass DatatableComponent {\n  /**\n   * Rows that are displayed in the table.\n   */\n  set rows(val) {\n    this._rows = val ?? [];\n    // This will ensure that datatable detects changes on doing like this rows = [...rows];\n    this.rowDiffer.diff([]);\n    if (val) {\n      this._internalRows = [...val];\n    }\n  }\n  /**\n   * Gets the rows.\n   */\n  get rows() {\n    return this._rows;\n  }\n  /**\n   * This attribute allows the user to set the name of the column to group the data with\n   */\n  set groupRowsBy(val) {\n    if (val) {\n      this._groupRowsBy = val;\n      if (this._groupRowsBy) {\n        // creates a new array with the data grouped\n        this.groupedRows = this.groupArrayBy(this._rows, this._groupRowsBy);\n      }\n    }\n  }\n  get groupRowsBy() {\n    return this._groupRowsBy;\n  }\n  /**\n   * Columns to be displayed.\n   */\n  set columns(val) {\n    if (val) {\n      this._internalColumns = toInternalColumn(val, this._defaultColumnWidth);\n      this.recalculateColumns();\n    }\n    this._columns = val;\n  }\n  /**\n   * Get the columns.\n   */\n  get columns() {\n    return this._columns;\n  }\n  /**\n   * The page size to be shown.\n   * Default value: `undefined`\n   */\n  set limit(val) {\n    this._limit = val;\n    // recalculate sizes/etc\n    this.recalculate();\n  }\n  /**\n   * Gets the limit.\n   */\n  get limit() {\n    return this._limit;\n  }\n  /**\n   * The total count of all rows.\n   * Default value: `0`\n   */\n  set count(val) {\n    this._count = val;\n    // recalculate sizes/etc\n    this.recalculate();\n  }\n  /**\n   * Gets the count.\n   */\n  get count() {\n    return this._count;\n  }\n  /**\n   * The current offset ( page - 1 ) shown.\n   * Default value: `0`\n   */\n  set offset(val) {\n    this._offset = val;\n  }\n  get offset() {\n    return Math.max(Math.min(this._offset, Math.ceil(this.rowCount / this.pageSize) - 1), 0);\n  }\n  /**\n   * Show ghost loaders on each cell.\n   * Default value: `false`\n   */\n  set ghostLoadingIndicator(val) {\n    this._ghostLoadingIndicator = val;\n    if (val && this.scrollbarV && !this.externalPaging) {\n      // in case where we don't have predefined total page length\n      this.rows = [...this.rows, undefined]; // undefined row will render ghost cell row at the end of the page\n    }\n  }\n  get ghostLoadingIndicator() {\n    return this._ghostLoadingIndicator;\n  }\n  /**\n   * CSS class applied if the header height if fixed height.\n   */\n  get isFixedHeader() {\n    const headerHeight = this.headerHeight;\n    return typeof headerHeight === 'string' ? headerHeight !== 'auto' : true;\n  }\n  /**\n   * CSS class applied to the root element if\n   * the row heights are fixed heights.\n   */\n  get isFixedRow() {\n    return this.rowHeight !== 'auto';\n  }\n  /**\n   * CSS class applied to root element if\n   * vertical scrolling is enabled.\n   */\n  get isVertScroll() {\n    return this.scrollbarV;\n  }\n  /**\n   * CSS class applied to root element if\n   * virtualization is enabled.\n   */\n  get isVirtualized() {\n    return this.virtualization;\n  }\n  /**\n   * CSS class applied to the root element\n   * if the horziontal scrolling is enabled.\n   */\n  get isHorScroll() {\n    return this.scrollbarH;\n  }\n  /**\n   * CSS class applied to root element is selectable.\n   */\n  get isSelectable() {\n    return this.selectionType !== undefined;\n  }\n  /**\n   * CSS class applied to root is checkbox selection.\n   */\n  get isCheckboxSelection() {\n    return this.selectionType === SelectionType.checkbox;\n  }\n  /**\n   * CSS class applied to root if cell selection.\n   */\n  get isCellSelection() {\n    return this.selectionType === SelectionType.cell;\n  }\n  /**\n   * CSS class applied to root if single select.\n   */\n  get isSingleSelection() {\n    return this.selectionType === SelectionType.single;\n  }\n  /**\n   * CSS class added to root element if mulit select\n   */\n  get isMultiSelection() {\n    return this.selectionType === SelectionType.multi;\n  }\n  /**\n   * CSS class added to root element if mulit click select\n   */\n  get isMultiClickSelection() {\n    return this.selectionType === SelectionType.multiClick;\n  }\n  /**\n   * Returns if all rows are selected.\n   */\n  get allRowsSelected() {\n    let allRowsSelected = this.rows && this.selected && this.selected.length === this.rows.length;\n    if (this.bodyComponent && this.selectAllRowsOnPage) {\n      const indexes = this.bodyComponent.indexes;\n      const rowsOnPage = indexes().last - indexes().first;\n      allRowsSelected = this.selected.length === rowsOnPage;\n    }\n    return this.selected && this.rows && this.rows.length !== 0 && allRowsSelected;\n  }\n  constructor() {\n    this.scrollbarHelper = inject(ScrollbarHelper);\n    this.cd = inject(ChangeDetectorRef);\n    this.columnChangesService = inject(ColumnChangesService);\n    this.configuration = inject(NGX_DATATABLE_CONFIG, {\n      optional: true\n    }) ??\n    // This is the old injection token for backward compatibility.\n    inject('configuration', {\n      optional: true\n    });\n    /**\n     * List of row objects that should be\n     * represented as selected in the grid.\n     * Default value: `[]`\n     */\n    this.selected = [];\n    /**\n     * Enable vertical scrollbars\n     */\n    this.scrollbarV = false;\n    /**\n     * Enable vertical scrollbars dynamically on demand.\n     * Property `scrollbarV` needs to be set `true` too.\n     * Width that is gained when no scrollbar is needed\n     * is added to the inner table width.\n     */\n    this.scrollbarVDynamic = false;\n    /**\n     * Enable horz scrollbars\n     */\n    this.scrollbarH = false;\n    /**\n     * The row height; which is necessary\n     * to calculate the height for the lazy rendering.\n     */\n    this.rowHeight = 30;\n    /**\n     * Type of column width distribution formula.\n     * Example: flex, force, standard\n     */\n    this.columnMode = ColumnMode.standard;\n    /**\n     * The minimum header height in pixels.\n     * Pass a falsey for no header\n     */\n    this.headerHeight = 30;\n    /**\n     * The minimum footer height in pixels.\n     * Pass falsey for no footer\n     */\n    this.footerHeight = 0;\n    /**\n     * If the table should use external paging\n     * otherwise its assumed that all data is preloaded.\n     */\n    this.externalPaging = false;\n    /**\n     * If the table should use external sorting or\n     * the built-in basic sorting.\n     */\n    this.externalSorting = false;\n    /**\n     * Show the linear loading bar.\n     * Default value: `false`\n     */\n    this.loadingIndicator = false;\n    /**\n     * Enable/Disable ability to re-order columns\n     * by dragging them.\n     */\n    this.reorderable = true;\n    /**\n     * Swap columns on re-order columns or\n     * move them.\n     */\n    this.swapColumns = true;\n    /**\n     * The type of sorting\n     */\n    this.sortType = SortType.single;\n    /**\n     * Array of sorted columns by property and type.\n     * Default value: `[]`\n     */\n    this.sorts = [];\n    /**\n     * Css class overrides\n     */\n    this.cssClasses = {};\n    /**\n     * Message overrides for localization\n     *\n     * @defaultValue\n     * ```\n     * {\n     *   emptyMessage: 'No data to display',\n     *   totalMessage: 'total',\n     *   selectedMessage: 'selected',\n     *   ariaFirstPageMessage: 'go to first page',\n     *   ariaPreviousPageMessage: 'go to previous page',\n     *   ariaPageNMessage: 'page',\n     *   ariaNextPageMessage: 'go to next page',\n     *   ariaLastPageMessage: 'go to last page'\n     * }\n     * ```\n     */\n    this.messages = {};\n    /**\n     * A boolean you can use to set the detault behaviour of rows and groups\n     * whether they will start expanded or not. If ommited the default is NOT expanded.\n     *\n     */\n    this.groupExpansionDefault = false;\n    /**\n     * Property to which you can use for determining select all\n     * rows on current page or not.\n     */\n    this.selectAllRowsOnPage = false;\n    /**\n     * A flag for row virtualization on / off\n     */\n    this.virtualization = true;\n    /**\n     * A flag for switching summary row on / off\n     */\n    this.summaryRow = false;\n    /**\n     * A height of summary row\n     */\n    this.summaryHeight = 30;\n    /**\n     * A property holds a summary row position: top/bottom\n     */\n    this.summaryPosition = 'top';\n    /**\n     * A flag to enable drag behavior of native HTML5 drag and drop API on rows.\n     * If set to true, {@link rowDragEvents} will emit dragstart and dragend events.\n     */\n    this.rowDraggable = false;\n    /**\n     * A flag to controll behavior of sort states.\n     * By default sort on column toggles between ascending and descending without getting removed.\n     * Set true to clear sorting of column after performing ascending and descending sort on that column.\n     */\n    this.enableClearingSortState = false;\n    /**\n     * Body was scrolled typically in a `scrollbarV:true` scenario.\n     */\n    this.scroll = new EventEmitter();\n    /**\n     * A cell or row was focused via keyboard or mouse click.\n     */\n    this.activate = new EventEmitter();\n    /**\n     * A cell or row was selected.\n     */\n    this.select = new EventEmitter();\n    /**\n     * Column sort was invoked.\n     */\n    this.sort = new EventEmitter();\n    /**\n     * The table was paged either triggered by the pager or the body scroll.\n     */\n    this.page = new EventEmitter();\n    /**\n     * Columns were re-ordered.\n     */\n    this.reorder = new EventEmitter();\n    /**\n     * Column was resized.\n     */\n    this.resize = new EventEmitter();\n    /**\n     * The context menu was invoked on the table.\n     * type indicates whether the header or the body was clicked.\n     * content contains either the column or the row that was clicked.\n     */\n    this.tableContextmenu = new EventEmitter(false);\n    /**\n     * A row was expanded ot collapsed for tree\n     */\n    this.treeAction = new EventEmitter();\n    /**\n     * Emits HTML5 native drag events.\n     * Only emits dragenter, dragover, drop events by default.\n     * Set {@link rowDraggble} to true for dragstart and dragend.\n     */\n    this.rowDragEvents = new EventEmitter();\n    this.element = inject(ElementRef).nativeElement;\n    this.rowDiffer = inject(KeyValueDiffers).find([]).create();\n    this.rowCount = 0;\n    this._offsetX = 0;\n    this._count = 0;\n    this._offset = 0;\n    this._rows = [];\n    this._internalRows = [];\n    this._subscriptions = [];\n    this._ghostLoadingIndicator = false;\n    this.verticalScrollVisible = false;\n    // In case horizontal scroll is enabled\n    // the column widths are initially calculated without vertical scroll offset\n    // this makes horizontal scroll to appear on load even if columns can fit in view\n    // this will be set to true once rows are available and rendered on UI\n    this._rowInitDone = signal(false);\n    /**\n     * This will be used when displaying or selecting rows.\n     * when tracking/comparing them, we'll use the value of this fn,\n     *\n     * (`fn(x) === fn(y)` instead of `x === y`)\n     */\n    this.rowIdentity = x => {\n      if (this._groupRowsBy) {\n        // each group in groupedRows are stored as {key, value: [rows]},\n        // where key is the groupRowsBy index\n        return x.key ?? x;\n      } else {\n        return x;\n      }\n    };\n    // apply global settings from Module.forRoot\n    if (this.configuration) {\n      if (this.configuration.messages) {\n        this.messages = {\n          ...this.configuration.messages\n        };\n      }\n      if (this.configuration.cssClasses) {\n        this.cssClasses = {\n          ...this.configuration.cssClasses\n        };\n      }\n      this.headerHeight = this.configuration.headerHeight ?? this.headerHeight;\n      this.footerHeight = this.configuration.footerHeight ?? this.footerHeight;\n      this.rowHeight = this.configuration.rowHeight ?? this.rowHeight;\n      this._defaultColumnWidth = this.configuration.defaultColumnWidth ?? 150;\n    }\n  }\n  /**\n   * Lifecycle hook that is called after data-bound\n   * properties of a directive are initialized.\n   */\n  ngOnInit() {\n    // need to call this immediatly to size\n    // if the table is hidden the visibility\n    // listener will invoke this itself upon show\n    this.recalculate();\n  }\n  /**\n   * Lifecycle hook that is called after a component's\n   * view has been fully initialized.\n   */\n  ngAfterViewInit() {\n    // this has to be done to prevent the change detection\n    // tree from freaking out because we are readjusting\n    if (typeof requestAnimationFrame === 'undefined') {\n      return;\n    }\n    requestAnimationFrame(() => {\n      this.recalculate();\n      // emit page for virtual server-side kickoff\n      if (this.externalPaging && this.scrollbarV) {\n        this.page.emit({\n          count: this.count,\n          pageSize: this.pageSize,\n          limit: this.limit,\n          offset: 0,\n          sorts: this.sorts\n        });\n      }\n    });\n  }\n  /**\n   * Lifecycle hook that is called after a component's\n   * content has been fully initialized.\n   */\n  ngAfterContentInit() {\n    if (this.columnTemplates.length) {\n      this.translateColumns(this.columnTemplates);\n    }\n    this._subscriptions.push(this.columnTemplates.changes.subscribe(v => this.translateColumns(v)));\n    this.listenForColumnInputChanges();\n  }\n  /**\n   * Translates the templates to the column objects\n   */\n  translateColumns(val) {\n    if (val) {\n      if (val.length) {\n        this._internalColumns = toInternalColumn(val, this._defaultColumnWidth);\n        this.recalculateColumns();\n        if (!this.externalSorting && this.rows?.length) {\n          this.sortInternalRows();\n        }\n        this.cd.markForCheck();\n      }\n    }\n  }\n  /**\n   * Creates a map with the data grouped by the user choice of grouping index\n   *\n   * @param originalArray the original array passed via parameter\n   * @param groupBy the key of the column to group the data by\n   */\n  groupArrayBy(originalArray, groupBy) {\n    // create a map to hold groups with their corresponding results\n    const map = new Map();\n    let i = 0;\n    originalArray.forEach(item => {\n      if (!item) {\n        // skip undefined items\n        return;\n      }\n      const key = item[groupBy];\n      const value = map.get(key);\n      if (!value) {\n        map.set(key, [item]);\n      } else {\n        value.push(item);\n      }\n      i++;\n    });\n    const addGroup = (key, value) => ({\n      key,\n      value\n    });\n    // convert map back to a simple array of objects\n    return Array.from(map, x => addGroup(x[0], x[1]));\n  }\n  /*\n   * Lifecycle hook that is called when Angular dirty checks a directive.\n   */\n  ngDoCheck() {\n    const rowDiffers = this.rowDiffer.diff(this.rows);\n    if (rowDiffers || this.disableRowCheck) {\n      // we don't sort again when ghost loader adds a dummy row\n      if (!this.ghostLoadingIndicator && !this.externalSorting && this._internalColumns) {\n        this.sortInternalRows();\n      } else {\n        this._internalRows = [...this.rows];\n      }\n      // auto group by parent on new update\n      this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation));\n      if (this._groupRowsBy) {\n        // If a column has been specified in _groupRowsBy create a new array with the data grouped by that row\n        this.groupedRows = this.groupArrayBy(this._rows, this._groupRowsBy);\n      }\n      if (rowDiffers) {\n        queueMicrotask(() => {\n          this._rowInitDone.set(true);\n          this.recalculate();\n          this.cd.markForCheck();\n        });\n      }\n      this.recalculatePages();\n      this.cd.markForCheck();\n    }\n  }\n  /**\n   * Recalc's the sizes of the grid.\n   *\n   * Updated automatically on changes to:\n   *\n   *  - Columns\n   *  - Rows\n   *  - Paging related\n   *\n   * Also can be manually invoked or upon window resize.\n   */\n  recalculate() {\n    this.recalculateDims();\n    this.recalculateColumns();\n    this.cd.markForCheck();\n  }\n  /**\n   * Window resize handler to update sizes.\n   */\n  onWindowResize() {\n    this.recalculate();\n  }\n  /**\n   * Recalulcates the column widths based on column width\n   * distribution mode and scrollbar offsets.\n   */\n  recalculateColumns(columns = this._internalColumns, forceIdx = -1, allowBleed = this.scrollbarH) {\n    let width = this._innerWidth;\n    if (!columns || !width) {\n      return undefined;\n    }\n    const bodyElement = this.bodyElement?.nativeElement;\n    this.verticalScrollVisible = bodyElement?.scrollHeight > bodyElement?.clientHeight;\n    if (this.scrollbarV || this.scrollbarVDynamic) {\n      width = width - (this.verticalScrollVisible || !this._rowInitDone() ? this.scrollbarHelper.width : 0);\n    }\n    if (this.columnMode === ColumnMode.force) {\n      forceFillColumnWidths(columns, width, forceIdx, allowBleed, this._defaultColumnWidth, this.scrollbarHelper.width);\n    } else if (this.columnMode === ColumnMode.flex) {\n      adjustColumnWidths(columns, width);\n    }\n    if (this.bodyComponent && this.bodyComponent.columnGroupWidths.total !== width) {\n      this.bodyComponent.columns = [...this._internalColumns];\n      this.bodyComponent.cd.markForCheck();\n    }\n    if (this.headerComponent && this.headerComponent._columnGroupWidths.total !== width) {\n      this.headerComponent.columns = [...this._internalColumns];\n    }\n    return columns;\n  }\n  /**\n   * Recalculates the dimensions of the table size.\n   * Internally calls the page size and row count calcs too.\n   *\n   */\n  recalculateDims() {\n    const dims = this.element.getBoundingClientRect();\n    this._innerWidth = Math.floor(dims.width);\n    if (this.scrollbarV) {\n      let height = dims.height;\n      if (this.headerHeight) {\n        height = height - this.headerHeight;\n      }\n      if (this.footerHeight) {\n        height = height - this.footerHeight;\n      }\n      this.bodyHeight = height;\n    }\n    this.recalculatePages();\n  }\n  /**\n   * Recalculates the pages after a update.\n   */\n  recalculatePages() {\n    this.pageSize = this.calcPageSize();\n    this.rowCount = this.calcRowCount();\n  }\n  /**\n   * Body triggered a page event.\n   */\n  onBodyPage(offset) {\n    // Avoid pagination caming from body events like scroll when the table\n    // has no virtualization and the external paging is enable.\n    // This means, let's the developer handle pagination by my him(her) self\n    if (this.externalPaging && !this.virtualization) {\n      return;\n    }\n    this.offset = offset;\n    if (!isNaN(this.offset)) {\n      this.page.emit({\n        count: this.count,\n        pageSize: this.pageSize,\n        limit: this.limit,\n        offset: this.offset,\n        sorts: this.sorts\n      });\n    }\n  }\n  /**\n   * The body triggered a scroll event.\n   */\n  onBodyScroll(event) {\n    this._offsetX = event.offsetX;\n    this.scroll.emit(event);\n  }\n  /**\n   * The footer triggered a page event.\n   */\n  onFooterPage(event) {\n    this.offset = event.page - 1;\n    this.bodyComponent.updateOffsetY(this.offset);\n    this.page.emit({\n      count: this.count,\n      pageSize: this.pageSize,\n      limit: this.limit,\n      offset: this.offset,\n      sorts: this.sorts\n    });\n    if (this.selectAllRowsOnPage) {\n      this.selected = [];\n      this.select.emit({\n        selected: this.selected\n      });\n    }\n  }\n  /**\n   * Recalculates the sizes of the page\n   */\n  calcPageSize() {\n    // Keep the page size constant even if the row has been expanded.\n    // This is because an expanded row is still considered to be a child of\n    // the original row.  Hence calculation would use rowHeight only.\n    if (this.scrollbarV && this.virtualization) {\n      const size = Math.ceil(this.bodyHeight / this.rowHeight);\n      return Math.max(size, 0);\n    }\n    // if limit is passed, we are paging\n    if (this.limit !== undefined) {\n      return this.limit;\n    }\n    // otherwise use row length\n    if (this.rows) {\n      return this.rows.length;\n    }\n    // other empty :(\n    return 0;\n  }\n  /**\n   * Calculates the row count.\n   */\n  calcRowCount() {\n    if (!this.externalPaging) {\n      if (this.groupedRows) {\n        return this.groupedRows.length;\n      } else if (this.treeFromRelation != null && this.treeToRelation != null) {\n        return this._internalRows.length;\n      } else {\n        return this.rows.length;\n      }\n    }\n    return this.count;\n  }\n  /**\n   * The header triggered a contextmenu event.\n   */\n  onColumnContextmenu({\n    event,\n    column\n  }) {\n    this.tableContextmenu.emit({\n      event,\n      type: ContextmenuType.header,\n      content: column\n    });\n  }\n  /**\n   * The body triggered a contextmenu event.\n   */\n  onRowContextmenu({\n    event,\n    row\n  }) {\n    this.tableContextmenu.emit({\n      event,\n      type: ContextmenuType.body,\n      content: row\n    });\n  }\n  /**\n   * The header triggered a column resize event.\n   */\n  onColumnResize({\n    column,\n    newValue,\n    prevValue\n  }) {\n    /* Safari/iOS 10.2 workaround */\n    if (column === undefined) {\n      return;\n    }\n    const idx = this._internalColumns.indexOf(column);\n    const cols = this._internalColumns.map(col => ({\n      ...col\n    }));\n    cols[idx].width = newValue;\n    // set this so we can force the column\n    // width distribution to be to this value\n    cols[idx].$$oldWidth = newValue;\n    this.recalculateColumns(cols, idx);\n    this._internalColumns = cols;\n    this.resize.emit({\n      column,\n      newValue,\n      prevValue\n    });\n  }\n  onColumnResizing({\n    column,\n    newValue\n  }) {\n    if (column === undefined) {\n      return;\n    }\n    column.width = newValue;\n    column.$$oldWidth = newValue;\n    const idx = this._internalColumns.indexOf(column);\n    this.recalculateColumns(this._internalColumns, idx);\n  }\n  /**\n   * The header triggered a column re-order event.\n   */\n  onColumnReorder(event) {\n    const {\n      column,\n      newValue,\n      prevValue\n    } = event;\n    const cols = this._internalColumns.map(c => ({\n      ...c\n    }));\n    if (this.swapColumns) {\n      const prevCol = cols[newValue];\n      cols[newValue] = column;\n      cols[prevValue] = prevCol;\n    } else {\n      if (newValue > prevValue) {\n        const movedCol = cols[prevValue];\n        for (let i = prevValue; i < newValue; i++) {\n          cols[i] = cols[i + 1];\n        }\n        cols[newValue] = movedCol;\n      } else {\n        const movedCol = cols[prevValue];\n        for (let i = prevValue; i > newValue; i--) {\n          cols[i] = cols[i - 1];\n        }\n        cols[newValue] = movedCol;\n      }\n    }\n    this._internalColumns = cols;\n    this.reorder.emit(event);\n  }\n  /**\n   * The header triggered a column sort event.\n   */\n  onColumnSort(event) {\n    // clean selected rows\n    if (this.selectAllRowsOnPage) {\n      this.selected = [];\n      this.select.emit({\n        selected: this.selected\n      });\n    }\n    this.sorts = event.sorts;\n    // this could be optimized better since it will resort\n    // the rows again on the 'push' detection...\n    if (this.externalSorting === false) {\n      // don't use normal setter so we don't resort\n      this.sortInternalRows();\n    }\n    // auto group by parent on new update\n    this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation));\n    // Always go to first page when sorting to see the newly sorted data\n    this.offset = 0;\n    this.bodyComponent.updateOffsetY(this.offset);\n    // Emit the page object with updated offset value\n    this.page.emit({\n      count: this.count,\n      pageSize: this.pageSize,\n      limit: this.limit,\n      offset: this.offset,\n      sorts: this.sorts\n    });\n    this.sort.emit(event);\n  }\n  /**\n   * Toggle all row selection\n   */\n  onHeaderSelect() {\n    if (this.bodyComponent && this.selectAllRowsOnPage) {\n      // before we splice, chk if we currently have all selected\n      const first = this.bodyComponent.indexes().first;\n      const last = this.bodyComponent.indexes().last;\n      const allSelected = this.selected.length === last - first;\n      // remove all existing either way\n      this.selected = [];\n      // do the opposite here\n      if (!allSelected) {\n        this.selected.push(...this._internalRows.slice(first, last).filter(row => !!row));\n      }\n    } else {\n      let relevantRows;\n      if (this.disableRowCheck) {\n        relevantRows = this.rows.filter(row => row && !this.disableRowCheck(row));\n      } else {\n        relevantRows = this.rows.filter(row => !!row);\n      }\n      // before we splice, chk if we currently have all selected\n      const allSelected = this.selected.length === relevantRows.length;\n      // remove all existing either way\n      this.selected = [];\n      // do the opposite here\n      if (!allSelected) {\n        this.selected.push(...relevantRows);\n      }\n    }\n    this.select.emit({\n      selected: this.selected\n    });\n  }\n  /**\n   * A row was selected from body\n   */\n  onBodySelect(event) {\n    this.select.emit(event);\n  }\n  /**\n   * A row was expanded or collapsed for tree\n   */\n  onTreeAction(event) {\n    const row = event.row;\n    // TODO: For duplicated items this will not work\n    const rowIndex = this._rows.findIndex(r => r && r[this.treeToRelation] === event.row[this.treeToRelation]);\n    this.treeAction.emit({\n      row,\n      rowIndex\n    });\n  }\n  ngOnDestroy() {\n    this._subscriptions.forEach(subscription => subscription.unsubscribe());\n  }\n  /**\n   * listen for changes to input bindings of all DataTableColumnDirective and\n   * trigger the columnTemplates.changes observable to emit\n   */\n  listenForColumnInputChanges() {\n    this._subscriptions.push(this.columnChangesService.columnInputChanges$.subscribe(() => {\n      if (this.columnTemplates) {\n        this.columnTemplates.notifyOnChanges();\n      }\n    }));\n  }\n  sortInternalRows() {\n    // if there are no sort criteria we reset the rows with original rows\n    if (!this.sorts || !this.sorts?.length) {\n      this._internalRows = this._rows;\n      // if there is any tree relation then re-group rows accordingly\n      if (this.treeFromRelation && this.treeToRelation) {\n        this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation));\n      }\n    }\n    if (this.groupedRows && this.groupedRows.length) {\n      const sortOnGroupHeader = this.sorts?.find(sortColumns => sortColumns.prop === this._groupRowsBy);\n      this.groupedRows = this.groupArrayBy(this._rows, this._groupRowsBy);\n      this.groupedRows = sortGroupedRows(this.groupedRows, this._internalColumns, this.sorts, sortOnGroupHeader);\n      this._internalRows = [...this._internalRows];\n    } else {\n      this._internalRows = sortRows(this._internalRows, this._internalColumns, this.sorts);\n    }\n  }\n  static {\n    this.ɵfac = function DatatableComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DatatableComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DatatableComponent,\n      selectors: [[\"ngx-datatable\"]],\n      contentQueries: function DatatableComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, DatatableRowDetailDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, DatatableGroupHeaderDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, DatatableFooterDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, DatatableRowDefDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, DataTableColumnDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowDetail = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.groupHeader = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowDefTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnTemplates = _t);\n        }\n      },\n      viewQuery: function DatatableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(DataTableBodyComponent, 5);\n          i0.ɵɵviewQuery(DataTableHeaderComponent, 5);\n          i0.ɵɵviewQuery(DataTableBodyComponent, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bodyComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bodyElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ngx-datatable\"],\n      hostVars: 22,\n      hostBindings: function DatatableComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function DatatableComponent_resize_HostBindingHandler() {\n            return ctx.onWindowResize();\n          }, i0.ɵɵresolveWindow);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"fixed-header\", ctx.isFixedHeader)(\"fixed-row\", ctx.isFixedRow)(\"scroll-vertical\", ctx.isVertScroll)(\"virtualized\", ctx.isVirtualized)(\"scroll-horz\", ctx.isHorScroll)(\"selectable\", ctx.isSelectable)(\"checkbox-selection\", ctx.isCheckboxSelection)(\"cell-selection\", ctx.isCellSelection)(\"single-selection\", ctx.isSingleSelection)(\"multi-selection\", ctx.isMultiSelection)(\"multi-click-selection\", ctx.isMultiClickSelection);\n        }\n      },\n      inputs: {\n        targetMarkerTemplate: \"targetMarkerTemplate\",\n        rows: \"rows\",\n        groupRowsBy: \"groupRowsBy\",\n        groupedRows: \"groupedRows\",\n        columns: \"columns\",\n        selected: \"selected\",\n        scrollbarV: [2, \"scrollbarV\", \"scrollbarV\", booleanAttribute],\n        scrollbarVDynamic: [2, \"scrollbarVDynamic\", \"scrollbarVDynamic\", booleanAttribute],\n        scrollbarH: [2, \"scrollbarH\", \"scrollbarH\", booleanAttribute],\n        rowHeight: \"rowHeight\",\n        columnMode: \"columnMode\",\n        headerHeight: [2, \"headerHeight\", \"headerHeight\", numberAttribute],\n        footerHeight: [2, \"footerHeight\", \"footerHeight\", numberAttribute],\n        externalPaging: [2, \"externalPaging\", \"externalPaging\", booleanAttribute],\n        externalSorting: [2, \"externalSorting\", \"externalSorting\", booleanAttribute],\n        limit: [2, \"limit\", \"limit\", numberAttribute],\n        count: [2, \"count\", \"count\", numberAttribute],\n        offset: [2, \"offset\", \"offset\", numberAttribute],\n        loadingIndicator: [2, \"loadingIndicator\", \"loadingIndicator\", booleanAttribute],\n        ghostLoadingIndicator: [2, \"ghostLoadingIndicator\", \"ghostLoadingIndicator\", booleanAttribute],\n        selectionType: \"selectionType\",\n        reorderable: [2, \"reorderable\", \"reorderable\", booleanAttribute],\n        swapColumns: [2, \"swapColumns\", \"swapColumns\", booleanAttribute],\n        sortType: \"sortType\",\n        sorts: \"sorts\",\n        cssClasses: \"cssClasses\",\n        messages: \"messages\",\n        rowClass: \"rowClass\",\n        selectCheck: \"selectCheck\",\n        displayCheck: \"displayCheck\",\n        groupExpansionDefault: [2, \"groupExpansionDefault\", \"groupExpansionDefault\", booleanAttribute],\n        trackByProp: \"trackByProp\",\n        selectAllRowsOnPage: [2, \"selectAllRowsOnPage\", \"selectAllRowsOnPage\", booleanAttribute],\n        virtualization: [2, \"virtualization\", \"virtualization\", booleanAttribute],\n        treeFromRelation: \"treeFromRelation\",\n        treeToRelation: \"treeToRelation\",\n        summaryRow: [2, \"summaryRow\", \"summaryRow\", booleanAttribute],\n        summaryHeight: [2, \"summaryHeight\", \"summaryHeight\", numberAttribute],\n        summaryPosition: \"summaryPosition\",\n        disableRowCheck: \"disableRowCheck\",\n        rowDraggable: [2, \"rowDraggable\", \"rowDraggable\", booleanAttribute],\n        enableClearingSortState: [2, \"enableClearingSortState\", \"enableClearingSortState\", booleanAttribute],\n        rowIdentity: \"rowIdentity\"\n      },\n      outputs: {\n        scroll: \"scroll\",\n        activate: \"activate\",\n        select: \"select\",\n        sort: \"sort\",\n        page: \"page\",\n        reorder: \"reorder\",\n        resize: \"resize\",\n        tableContextmenu: \"tableContextmenu\",\n        treeAction: \"treeAction\",\n        rowDragEvents: \"rowDragEvents\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: DatatableComponentToken,\n        useExisting: DatatableComponent\n      }, ColumnChangesService])],\n      ngContentSelectors: _c6,\n      decls: 9,\n      vars: 37,\n      consts: [[\"visibilityObserver\", \"\", 3, \"visible\"], [\"role\", \"table\"], [\"role\", \"rowgroup\", 3, \"sorts\", \"sortType\", \"scrollbarH\", \"innerWidth\", \"offsetX\", \"dealsWithGroup\", \"columns\", \"headerHeight\", \"reorderable\", \"targetMarkerTemplate\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"selectionType\", \"verticalScrollVisible\", \"enableClearingSortState\"], [\"tabindex\", \"0\", \"role\", \"rowgroup\", 3, \"page\", \"activate\", \"rowContextmenu\", \"select\", \"scroll\", \"treeAction\", \"groupRowsBy\", \"groupedRows\", \"rows\", \"groupExpansionDefault\", \"scrollbarV\", \"scrollbarH\", \"virtualization\", \"loadingIndicator\", \"ghostLoadingIndicator\", \"externalPaging\", \"rowHeight\", \"rowCount\", \"offset\", \"trackByProp\", \"columns\", \"pageSize\", \"offsetX\", \"rowDetail\", \"groupHeader\", \"selected\", \"innerWidth\", \"bodyHeight\", \"selectionType\", \"rowIdentity\", \"rowClass\", \"selectCheck\", \"displayCheck\", \"summaryRow\", \"summaryHeight\", \"summaryPosition\", \"verticalScrollVisible\", \"disableRowCheck\", \"rowDraggable\", \"rowDragEvents\", \"rowDefTemplate\"], [3, \"rowCount\", \"pageSize\", \"offset\", \"footerHeight\", \"footerTemplate\", \"totalMessage\", \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"selectedCount\", \"selectedMessage\", \"pagerNextIcon\"], [\"role\", \"rowgroup\", 3, \"sort\", \"resize\", \"resizing\", \"reorder\", \"select\", \"columnContextmenu\", \"sorts\", \"sortType\", \"scrollbarH\", \"innerWidth\", \"offsetX\", \"dealsWithGroup\", \"columns\", \"headerHeight\", \"reorderable\", \"targetMarkerTemplate\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"selectionType\", \"verticalScrollVisible\", \"enableClearingSortState\"], [\"role\", \"row\"], [\"role\", \"cell\", 1, \"empty-row\", 3, \"innerHTML\"], [3, \"page\", \"rowCount\", \"pageSize\", \"offset\", \"footerHeight\", \"footerTemplate\", \"totalMessage\", \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"selectedCount\", \"selectedMessage\", \"pagerNextIcon\"]],\n      template: function DatatableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c5);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"visible\", function DatatableComponent_Template_div_visible_0_listener() {\n            return ctx.recalculate();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵconditionalCreate(2, DatatableComponent_Conditional_2_Template, 1, 17, \"datatable-header\", 2);\n          i0.ɵɵelementStart(3, \"datatable-body\", 3);\n          i0.ɵɵlistener(\"page\", function DatatableComponent_Template_datatable_body_page_3_listener($event) {\n            return ctx.onBodyPage($event);\n          })(\"activate\", function DatatableComponent_Template_datatable_body_activate_3_listener($event) {\n            return ctx.activate.emit($event);\n          })(\"rowContextmenu\", function DatatableComponent_Template_datatable_body_rowContextmenu_3_listener($event) {\n            return ctx.onRowContextmenu($event);\n          })(\"select\", function DatatableComponent_Template_datatable_body_select_3_listener($event) {\n            return ctx.onBodySelect($event);\n          })(\"scroll\", function DatatableComponent_Template_datatable_body_scroll_3_listener($event) {\n            return ctx.onBodyScroll($event);\n          })(\"treeAction\", function DatatableComponent_Template_datatable_body_treeAction_3_listener($event) {\n            return ctx.onTreeAction($event);\n          });\n          i0.ɵɵprojection(4, 0, [\"ngProjectAs\", \"[loading-indicator]\", 5, [\"\", \"loading-indicator\", \"\"]], DatatableComponent_ProjectionFallback_4_Template, 1, 0);\n          i0.ɵɵprojection(6, 1, [\"ngProjectAs\", \"[empty-content]\", 5, [\"\", \"empty-content\", \"\"]], DatatableComponent_ProjectionFallback_6_Template, 2, 1);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵconditionalCreate(8, DatatableComponent_Conditional_8_Template, 1, 12, \"datatable-footer\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.headerHeight ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"groupRowsBy\", ctx.groupRowsBy)(\"groupedRows\", ctx.groupedRows)(\"rows\", ctx._internalRows)(\"groupExpansionDefault\", ctx.groupExpansionDefault)(\"scrollbarV\", ctx.scrollbarV)(\"scrollbarH\", ctx.scrollbarH)(\"virtualization\", ctx.virtualization)(\"loadingIndicator\", ctx.loadingIndicator)(\"ghostLoadingIndicator\", ctx.ghostLoadingIndicator)(\"externalPaging\", ctx.externalPaging)(\"rowHeight\", ctx.rowHeight)(\"rowCount\", ctx.rowCount)(\"offset\", ctx.offset)(\"trackByProp\", ctx.trackByProp)(\"columns\", ctx._internalColumns)(\"pageSize\", ctx.pageSize)(\"offsetX\", ctx._offsetX)(\"rowDetail\", ctx.rowDetail)(\"groupHeader\", ctx.groupHeader)(\"selected\", ctx.selected)(\"innerWidth\", ctx._innerWidth)(\"bodyHeight\", ctx.bodyHeight)(\"selectionType\", ctx.selectionType)(\"rowIdentity\", ctx.rowIdentity)(\"rowClass\", ctx.rowClass)(\"selectCheck\", ctx.selectCheck)(\"displayCheck\", ctx.displayCheck)(\"summaryRow\", ctx.summaryRow)(\"summaryHeight\", ctx.summaryHeight)(\"summaryPosition\", ctx.summaryPosition)(\"verticalScrollVisible\", ctx.verticalScrollVisible)(\"disableRowCheck\", ctx.disableRowCheck)(\"rowDraggable\", ctx.rowDraggable)(\"rowDragEvents\", ctx.rowDragEvents)(\"rowDefTemplate\", ctx.rowDefTemplate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(ctx.footerHeight ? 8 : -1);\n        }\n      },\n      dependencies: [VisibilityDirective, DataTableHeaderComponent, DataTableBodyComponent, DataTableFooterComponent, ProgressBarComponent],\n      styles: [\"[_nghost-%COMP%]{display:block;overflow:hidden;justify-content:center;position:relative;transform:translateZ(0)}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([throttleable(5)], DatatableComponent.prototype, \"onWindowResize\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatatableComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-datatable',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'ngx-datatable'\n      },\n      providers: [{\n        provide: DatatableComponentToken,\n        useExisting: DatatableComponent\n      }, ColumnChangesService],\n      imports: [VisibilityDirective, DataTableHeaderComponent, DataTableBodyComponent, DataTableFooterComponent, ProgressBarComponent],\n      template: \"<div visibilityObserver (visible)=\\\"recalculate()\\\">\\n  <div role=\\\"table\\\">\\n    @if (headerHeight) {\\n    <datatable-header\\n      role=\\\"rowgroup\\\"\\n      [sorts]=\\\"sorts\\\"\\n      [sortType]=\\\"sortType\\\"\\n      [scrollbarH]=\\\"scrollbarH\\\"\\n      [innerWidth]=\\\"_innerWidth\\\"\\n      [offsetX]=\\\"_offsetX\\\"\\n      [dealsWithGroup]=\\\"groupedRows !== undefined\\\"\\n      [columns]=\\\"_internalColumns\\\"\\n      [headerHeight]=\\\"headerHeight\\\"\\n      [reorderable]=\\\"reorderable\\\"\\n      [targetMarkerTemplate]=\\\"targetMarkerTemplate\\\"\\n      [sortAscendingIcon]=\\\"cssClasses.sortAscending\\\"\\n      [sortDescendingIcon]=\\\"cssClasses.sortDescending\\\"\\n      [sortUnsetIcon]=\\\"cssClasses.sortUnset\\\"\\n      [allRowsSelected]=\\\"allRowsSelected\\\"\\n      [selectionType]=\\\"selectionType\\\"\\n      [verticalScrollVisible]=\\\"verticalScrollVisible\\\"\\n      [enableClearingSortState]=\\\"enableClearingSortState\\\"\\n      (sort)=\\\"onColumnSort($event)\\\"\\n      (resize)=\\\"onColumnResize($event)\\\"\\n      (resizing)=\\\"onColumnResizing($event)\\\"\\n      (reorder)=\\\"onColumnReorder($event)\\\"\\n      (select)=\\\"onHeaderSelect()\\\"\\n      (columnContextmenu)=\\\"onColumnContextmenu($event)\\\"\\n    >\\n    </datatable-header>\\n    }\\n    <datatable-body\\n      tabindex=\\\"0\\\"\\n      role=\\\"rowgroup\\\"\\n      [groupRowsBy]=\\\"groupRowsBy\\\"\\n      [groupedRows]=\\\"groupedRows\\\"\\n      [rows]=\\\"_internalRows\\\"\\n      [groupExpansionDefault]=\\\"groupExpansionDefault\\\"\\n      [scrollbarV]=\\\"scrollbarV\\\"\\n      [scrollbarH]=\\\"scrollbarH\\\"\\n      [virtualization]=\\\"virtualization\\\"\\n      [loadingIndicator]=\\\"loadingIndicator\\\"\\n      [ghostLoadingIndicator]=\\\"ghostLoadingIndicator\\\"\\n      [externalPaging]=\\\"externalPaging\\\"\\n      [rowHeight]=\\\"rowHeight\\\"\\n      [rowCount]=\\\"rowCount\\\"\\n      [offset]=\\\"offset\\\"\\n      [trackByProp]=\\\"trackByProp\\\"\\n      [columns]=\\\"_internalColumns\\\"\\n      [pageSize]=\\\"pageSize\\\"\\n      [offsetX]=\\\"_offsetX\\\"\\n      [rowDetail]=\\\"rowDetail\\\"\\n      [groupHeader]=\\\"groupHeader\\\"\\n      [selected]=\\\"selected\\\"\\n      [innerWidth]=\\\"_innerWidth\\\"\\n      [bodyHeight]=\\\"bodyHeight\\\"\\n      [selectionType]=\\\"selectionType\\\"\\n      [rowIdentity]=\\\"rowIdentity\\\"\\n      [rowClass]=\\\"rowClass\\\"\\n      [selectCheck]=\\\"selectCheck\\\"\\n      [displayCheck]=\\\"displayCheck\\\"\\n      [summaryRow]=\\\"summaryRow\\\"\\n      [summaryHeight]=\\\"summaryHeight\\\"\\n      [summaryPosition]=\\\"summaryPosition\\\"\\n      [verticalScrollVisible]=\\\"verticalScrollVisible\\\"\\n      (page)=\\\"onBodyPage($event)\\\"\\n      (activate)=\\\"activate.emit($event)\\\"\\n      (rowContextmenu)=\\\"onRowContextmenu($event)\\\"\\n      (select)=\\\"onBodySelect($event)\\\"\\n      (scroll)=\\\"onBodyScroll($event)\\\"\\n      (treeAction)=\\\"onTreeAction($event)\\\"\\n      [disableRowCheck]=\\\"disableRowCheck\\\"\\n      [rowDraggable]=\\\"rowDraggable\\\"\\n      [rowDragEvents]=\\\"rowDragEvents\\\"\\n      [rowDefTemplate]=\\\"rowDefTemplate\\\"\\n    >\\n      <ng-content select=\\\"[loading-indicator]\\\" ngProjectAs=\\\"[loading-indicator]\\\">\\n        <datatable-progress></datatable-progress>\\n      </ng-content>\\n      <ng-content select=\\\"[empty-content]\\\" ngProjectAs=\\\"[empty-content]\\\">\\n        <div role=\\\"row\\\">\\n          <div\\n            role=\\\"cell\\\"\\n            class=\\\"empty-row\\\"\\n            [innerHTML]=\\\"messages.emptyMessage ?? 'No data to display'\\\"\\n          ></div>\\n        </div>\\n      </ng-content>\\n    </datatable-body>\\n  </div>\\n  @if (footerHeight) {\\n  <datatable-footer\\n    [rowCount]=\\\"groupedRows !== undefined ? _internalRows.length : rowCount\\\"\\n    [pageSize]=\\\"pageSize\\\"\\n    [offset]=\\\"offset\\\"\\n    [footerHeight]=\\\"footerHeight\\\"\\n    [footerTemplate]=\\\"footer\\\"\\n    [totalMessage]=\\\"messages.totalMessage ?? 'total'\\\"\\n    [pagerLeftArrowIcon]=\\\"cssClasses.pagerLeftArrow\\\"\\n    [pagerRightArrowIcon]=\\\"cssClasses.pagerRightArrow\\\"\\n    [pagerPreviousIcon]=\\\"cssClasses.pagerPrevious\\\"\\n    [selectedCount]=\\\"selected.length\\\"\\n    [selectedMessage]=\\\"!!selectionType && (messages.selectedMessage ?? 'selected')\\\"\\n    [pagerNextIcon]=\\\"cssClasses.pagerNext\\\"\\n    (page)=\\\"onFooterPage($event)\\\"\\n  >\\n  </datatable-footer>\\n  }\\n</div>\\n\",\n      styles: [\":host{display:block;overflow:hidden;justify-content:center;position:relative;transform:translateZ(0)}\\n\"]\n    }]\n  }], () => [], {\n    targetMarkerTemplate: [{\n      type: Input\n    }],\n    rows: [{\n      type: Input\n    }],\n    groupRowsBy: [{\n      type: Input\n    }],\n    groupedRows: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    scrollbarV: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrollbarVDynamic: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrollbarH: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    columnMode: [{\n      type: Input\n    }],\n    headerHeight: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    footerHeight: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    externalPaging: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    externalSorting: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    limit: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    count: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    offset: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    loadingIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ghostLoadingIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectionType: [{\n      type: Input\n    }],\n    reorderable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    swapColumns: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    sortType: [{\n      type: Input\n    }],\n    sorts: [{\n      type: Input\n    }],\n    cssClasses: [{\n      type: Input\n    }],\n    messages: [{\n      type: Input\n    }],\n    rowClass: [{\n      type: Input\n    }],\n    selectCheck: [{\n      type: Input\n    }],\n    displayCheck: [{\n      type: Input\n    }],\n    groupExpansionDefault: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    trackByProp: [{\n      type: Input\n    }],\n    selectAllRowsOnPage: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    virtualization: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    treeFromRelation: [{\n      type: Input\n    }],\n    treeToRelation: [{\n      type: Input\n    }],\n    summaryRow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    summaryHeight: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    summaryPosition: [{\n      type: Input\n    }],\n    disableRowCheck: [{\n      type: Input\n    }],\n    rowDraggable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableClearingSortState: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scroll: [{\n      type: Output\n    }],\n    activate: [{\n      type: Output\n    }],\n    select: [{\n      type: Output\n    }],\n    sort: [{\n      type: Output\n    }],\n    page: [{\n      type: Output\n    }],\n    reorder: [{\n      type: Output\n    }],\n    resize: [{\n      type: Output\n    }],\n    tableContextmenu: [{\n      type: Output\n    }],\n    treeAction: [{\n      type: Output\n    }],\n    rowDragEvents: [{\n      type: Output\n    }],\n    isFixedHeader: [{\n      type: HostBinding,\n      args: ['class.fixed-header']\n    }],\n    isFixedRow: [{\n      type: HostBinding,\n      args: ['class.fixed-row']\n    }],\n    isVertScroll: [{\n      type: HostBinding,\n      args: ['class.scroll-vertical']\n    }],\n    isVirtualized: [{\n      type: HostBinding,\n      args: ['class.virtualized']\n    }],\n    isHorScroll: [{\n      type: HostBinding,\n      args: ['class.scroll-horz']\n    }],\n    isSelectable: [{\n      type: HostBinding,\n      args: ['class.selectable']\n    }],\n    isCheckboxSelection: [{\n      type: HostBinding,\n      args: ['class.checkbox-selection']\n    }],\n    isCellSelection: [{\n      type: HostBinding,\n      args: ['class.cell-selection']\n    }],\n    isSingleSelection: [{\n      type: HostBinding,\n      args: ['class.single-selection']\n    }],\n    isMultiSelection: [{\n      type: HostBinding,\n      args: ['class.multi-selection']\n    }],\n    isMultiClickSelection: [{\n      type: HostBinding,\n      args: ['class.multi-click-selection']\n    }],\n    columnTemplates: [{\n      type: ContentChildren,\n      args: [DataTableColumnDirective]\n    }],\n    rowDetail: [{\n      type: ContentChild,\n      args: [DatatableRowDetailDirective]\n    }],\n    groupHeader: [{\n      type: ContentChild,\n      args: [DatatableGroupHeaderDirective]\n    }],\n    footer: [{\n      type: ContentChild,\n      args: [DatatableFooterDirective]\n    }],\n    bodyComponent: [{\n      type: ViewChild,\n      args: [DataTableBodyComponent]\n    }],\n    headerComponent: [{\n      type: ViewChild,\n      args: [DataTableHeaderComponent]\n    }],\n    bodyElement: [{\n      type: ViewChild,\n      args: [DataTableBodyComponent, {\n        read: ElementRef\n      }]\n    }],\n    rowDefTemplate: [{\n      type: ContentChild,\n      args: [DatatableRowDefDirective, {\n        read: TemplateRef\n      }]\n    }],\n    rowIdentity: [{\n      type: Input\n    }],\n    onWindowResize: [{\n      type: HostListener,\n      args: ['window:resize']\n    }]\n  });\n})();\n\n/**\n * Row Disable Directive\n * Use this to disable/enable all children elements\n * Usage:\n *  To disable\n * \t\t<div [disabled]=\"true\" disable-row >\n * \t\t</div>\n *  To enable\n *  \t<div [disabled]=\"false\" disable-row >\n * \t\t</div>\n */\nclass DisableRowDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef);\n    this.disabled = input(false, {\n      transform: booleanAttribute\n    });\n    effect(() => {\n      if (this.disabled()) {\n        this.disableAllElements();\n      }\n    });\n  }\n  disableAllElements() {\n    const hostElement = this.elementRef?.nativeElement;\n    if (!hostElement) {\n      return;\n    }\n    Array.from(hostElement.querySelectorAll('*')).forEach(child => {\n      child.setAttribute('disabled', '');\n    });\n  }\n  static {\n    this.ɵfac = function DisableRowDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DisableRowDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DisableRowDirective,\n      selectors: [[\"\", \"disable-row\", \"\"]],\n      inputs: {\n        disabled: [1, \"disabled\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DisableRowDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[disable-row]'\n    }]\n  }], () => [], null);\n})();\nclass NgxDatatableModule {\n  /**\n   * Configure global configuration via INgxDatatableConfig\n   * @param configuration\n   */\n  static forRoot(configuration) {\n    return {\n      ngModule: NgxDatatableModule,\n      providers: [providedNgxDatatableConfig(configuration)]\n    };\n  }\n  static {\n    this.ɵfac = function NgxDatatableModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgxDatatableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgxDatatableModule,\n      imports: [DataTableFooterTemplateDirective, DatatableComponent, DataTableColumnDirective, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnGhostCellDirective, DataTableColumnCellTreeToggle, DatatableFooterDirective, DatatableGroupHeaderTemplateDirective, DisableRowDirective, DatatableRowDefComponent, DatatableRowDefDirective],\n      exports: [DatatableComponent, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableColumnDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnGhostCellDirective, DataTableColumnCellTreeToggle, DataTableFooterTemplateDirective, DatatableFooterDirective, DatatableGroupHeaderTemplateDirective, DisableRowDirective, DatatableRowDefComponent, DatatableRowDefDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxDatatableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DataTableFooterTemplateDirective, DatatableComponent, DataTableColumnDirective, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnGhostCellDirective, DataTableColumnCellTreeToggle, DatatableFooterDirective, DatatableGroupHeaderTemplateDirective, DisableRowDirective, DatatableRowDefComponent, DatatableRowDefDirective],\n      exports: [DatatableComponent, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableColumnDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnGhostCellDirective, DataTableColumnCellTreeToggle, DataTableFooterTemplateDirective, DatatableFooterDirective, DatatableGroupHeaderTemplateDirective, DisableRowDirective, DatatableRowDefComponent, DatatableRowDefDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-datatable\n */\n// components\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ColumnChangesService, ColumnMode, ContextmenuType, DataTableColumnCellDirective, DataTableColumnCellTreeToggle, DataTableColumnDirective, DataTableColumnGhostCellDirective, DataTableColumnHeaderDirective, DataTableFooterTemplateDirective, DatatableComponent, DatatableFooterDirective, DatatableGroupHeaderDirective, DatatableGroupHeaderTemplateDirective, DatatableRowDefComponent, DatatableRowDefDirective, DatatableRowDefInternalDirective, DatatableRowDetailDirective, DatatableRowDetailTemplateDirective, DisableRowDirective, NgxDatatableModule, SelectionType, SortDirection, SortType, isNullOrUndefined, providedNgxDatatableConfig, toInternalColumn };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,aAAa;AACf;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;AAC9C,IAAG,WAAW,SAAS,SAAS,yEAAyE,QAAQ;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,WAAW,OAAO,UAAU;AAAA,EACzE;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,CAAC;AAAA,EACxB;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,CAAC;AAAA,EACxB;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,CAAC;AAAA,EACxB;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,0FAA0F;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,oBAAoB,GAAG,+EAA+E,GAAG,GAAG,KAAK,CAAC;AACrH,IAAG,oBAAoB,GAAG,+EAA+E,GAAG,GAAG,KAAK,CAAC;AACrH,IAAG,oBAAoB,GAAG,+EAA+E,GAAG,GAAG,KAAK,CAAC;AACrH,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,eAAe,UAAU;AAC1D,IAAG,YAAY,cAAc,OAAO,UAAU;AAC9C,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,eAAe,YAAY,IAAI,EAAE;AACzD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,eAAe,cAAc,IAAI,EAAE;AAC3D,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,eAAe,cAAc,OAAO,eAAe,aAAa,IAAI,EAAE;AAAA,EAChG;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAAC;AACjG,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,eAAe,CAAC;AAAA,EACxH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,OAAO,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC/I;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,iEAAiE,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,iEAAiE,GAAG,GAAG,MAAM,CAAC;AAAA,EACjM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,CAAC,OAAO,OAAO,qBAAqB,IAAI,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,OAAO,cAAc,EAAE,aAAa,OAAO,OAAU,cAAc;AAAA,EAC5F;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,OAAO,cAAc;AAC5C,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,iEAAiE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,iEAAiE,GAAG,GAAG,QAAQ,EAAE;AAAA,EAClM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,OAAO,mBAAmB,IAAI,CAAC;AAAA,EACzD;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAAC;AACnF,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC1G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,OAAO,YAAY,EAAE,2BAA2B,OAAO,WAAW;AAAA,EAC7G;AACF;AACA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,uBAAuB,CAAC;AAC7C,IAAG,WAAW,YAAY,SAAS,qGAAqG,QAAQ;AAC9I,YAAM,eAAkB,cAAc,GAAG,EAAE;AAC3C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,QAAQ,YAAY,CAAC;AAAA,IAC/D,CAAC,EAAE,cAAc,SAAS,yGAAyG;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,OAAO,GAAG,EAAE,SAAS,OAAO,KAAK,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,YAAY,OAAO,QAAQ,EAAE,UAAU,SAAS,EAAE,aAAa,OAAO,SAAS,EAAE,gBAAgB,OAAO,YAAY,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU;AAAA,EAC1S;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,iBAAiB,GAAG,8DAA8D,GAAG,IAAI,uBAAuB,GAAG,UAAU;AAChI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,EAAE;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAc,eAAe,kBAAkB,YAAY,MAAM,sBAAsB,CAAC;AAC3F,IAAG,YAAY,SAAS,OAAO,mBAAmB,YAAY,IAAI,GAAG,IAAI;AACzE,IAAG,YAAY,gBAAgB,OAAO,QAAQ;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO;AAAA,EACnC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,wDAAwD,GAAG,GAAG,OAAO,CAAC;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,cAAc,YAAY,QAAQ,SAAS,IAAI,EAAE;AAAA,EACtD;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,OAAO,eAAe,WAAW,EAAE,2BAA2B,OAAO,UAAU;AAAA,EAC1H;AACF;AACA,IAAM,MAAM,CAAC,QAAQ;AACrB,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC;AAC3D,IAAG,WAAW,UAAU,SAAS,4FAA4F;AAC3H,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,YAAY,CAAC;AAClC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,UAAU,SAAS,OAAO,GAAG,CAAC;AAAA,IAC9E,CAAC;AACD,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,kBAAkB,EAAE,WAAW,OAAO,IAAI,MAAM,MAAM;AAAA,EACxF;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,oBAAoB,GAAG,mEAAmE,GAAG,GAAG,KAAK;AACxG,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,eAAe,CAAC;AAC1G,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,sBAAsB,IAAI,EAAE,SAAS,OAAO,YAAY,IAAI;AAC5F,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,YAAY,eAAe,IAAI,EAAE;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,QAAQ,EAAE,2BAA2B,OAAO,OAAO;AAAA,EAC1G;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,eAAe,CAAC;AAC1G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,iBAAiB,IAAI;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,UAAU,QAAQ,EAAE,2BAA2B,OAAO,OAAO;AAAA,EACxG;AACF;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,sBAAsB,CAAC;AAAA,EACzC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,gBAAgB,EAAE,aAAa,OAAO,SAAS,EAAE,OAAO,OAAO,UAAU,EAAE,YAAe,gBAAgB,GAAG,KAAK,EAAE,CAAC;AAAA,EACxL;AACF;AACA,IAAM,MAAM,MAAM,CAAC;AACnB,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAAC;AAClG,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,eAAe,CAAC;AAAA,EACzH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,WAAW,oBAAoB,OAAO,iBAAiB;AAAA,EAC5D;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,oBAAoB,GAAG,kEAAkE,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,MAAM,CAAC;AAC9L,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,OAAO,IAAI;AAC1C,IAAG,YAAY,uBAAuB,OAAO,QAAQ;AACrD,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,oBAAoB,IAAI,CAAC;AAAA,EACpD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,oDAAoD,GAAG,GAAG,OAAO,GAAM,yBAAyB;AACvH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,WAAW,IAAI;AAC/C,IAAG,YAAY,sBAAsB,OAAO,QAAQ;AACpD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,OAAO;AAAA,EAC9B;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAC;AACzE,IAAM,MAAM,CAAC,uBAAuB,iBAAiB;AACrD,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,cAAc;AAChB;AACA,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,QAAQ;AAAA,EAC/B,UAAU;AAAA,EACV,aAAa;AAAA,EACb,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AACZ;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACpC,KAAK;AAAA,EACL,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,EACd,UAAU;AACZ;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ,EAAE,aAAa,OAAO,SAAS,EAAE,mBAAmB,OAAO,UAAU;AAAA,EAC3I;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,EAAE,cAAc,OAAO,UAAU,EAAE,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO;AAAA,EAClI;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,sBAAsB,IAAI,CAAC;AAChD,IAAG,WAAW,cAAc,SAAS,uGAAuG;AAC1I,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC,EAAE,YAAY,SAAS,mGAAmG,QAAQ;AACjI,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,QAAQ,QAAQ,CAAC;AAAA,IAC3D,CAAC,EAAE,QAAQ,SAAS,+FAA+F,QAAQ;AACzH,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,gBAAmB,YAAY,CAAC;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,QAAQ,QAAQ,aAAa,CAAC;AAAA,IAClE,CAAC,EAAE,YAAY,SAAS,mGAAmG,QAAQ;AACjI,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,QAAQ,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,aAAa,SAAS,oGAAoG,QAAQ;AACnI,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,gBAAmB,YAAY,CAAC;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,QAAQ,aAAa,CAAC;AAAA,IACvE,CAAC,EAAE,aAAa,SAAS,oGAAoG,QAAQ;AACnI,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,gBAAmB,YAAY,CAAC;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,QAAQ,aAAa,CAAC;AAAA,IACvE,CAAC,EAAE,aAAa,SAAS,oGAAoG,QAAQ;AACnI,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,gBAAmB,YAAY,CAAC;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,QAAQ,QAAQ,aAAa,CAAC;AAAA,IAClE,CAAC,EAAE,WAAW,SAAS,kGAAkG,QAAQ;AAC/H,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,WAAW,IAAI;AACrB,UAAM,kBAAkB,IAAI;AAC5B,UAAM,iBAAiB,IAAI;AAC3B,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,WAAW,EAAE,cAAc,OAAO,eAAe,MAAM,CAAC,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,aAAa,OAAO,aAAa,MAAM,CAAC,EAAE,OAAO,MAAM,EAAE,SAAS,cAAc,EAAE,YAAe,gBAAgB,IAAI,KAAK,UAAU,eAAe,CAAC,EAAE,YAAY,OAAO,eAAe,MAAM,CAAC,EAAE,YAAY,OAAO,QAAQ,EAAE,gBAAgB,OAAO,YAAY,EAAE,cAAc,UAAU,OAAO,OAAO,OAAO,UAAU,EAAE,aAAa,OAAO,YAAY,EAAE,yBAAyB,OAAO,qBAAqB;AAAA,EACriB;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB,EAAE;AAAA,EACpC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,YAAY,CAAC,EAAE,aAAa,OAAO,SAAS;AAAA,EACvF;AACF;AACA,SAAS,+FAA+F,IAAI,KAAK;AAC/G,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAChG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gGAAgG,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAC3I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,eAAkB,iBAAiB,CAAC;AAC1C,UAAM,UAAa,cAAc;AACjC,UAAM,YAAY,QAAQ;AAC1B,UAAM,iBAAiB,QAAQ;AAC/B,IAAG,cAAc;AACjB,UAAM,cAAiB,YAAY,CAAC;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,kBAAqB,gBAAgB,GAAG,KAAK,OAAO,gBAAgB,aAAa,WAAW,cAAc,CAAC,EAAE,0BAA0B,YAAY;AAAA,EACnK;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,EAAE;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,eAAkB,iBAAiB,CAAC;AAC1C,UAAM,UAAa,cAAc;AACjC,UAAM,YAAY,QAAQ;AAC1B,UAAM,iBAAiB,QAAQ;AAC/B,IAAG,cAAc;AACjB,UAAM,cAAiB,YAAY,CAAC;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,WAAW,OAAO,QAAQ,EAAE,QAAQ,gBAAgB,YAAY,CAAC;AAAA,EACxK;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAChG,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,+FAA+F,GAAG,GAAG,gBAAgB,EAAE;AAAA,EACnJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,OAAO,MAAM,SAAS,IAAI,IAAI,EAAE;AAAA,EACnD;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,EAAE;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,aAAa,IAAI;AACvB,UAAM,UAAa,cAAc,CAAC;AAClC,UAAM,YAAY,QAAQ;AAC1B,UAAM,iBAAiB,QAAQ;AAC/B,IAAG,cAAc;AACjB,UAAM,cAAiB,YAAY,CAAC;AACpC,UAAM,SAAY,cAAc;AAChC,UAAM,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,OAAO;AAC7E,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,SAAS,aAAa,OAAO,OAAO,UAAU,OAAO,OAAO,QAAQ,EAAE,QAAQ,gBAAgB,YAAY,YAAY,CAAC;AAAA,EAC/N;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAChG,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,uFAAuF,GAAG,GAAG,gBAAgB,IAAO,oBAAoB,EAAE,eAAe,IAAI;AAAA,EACtL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,WAAW,UAAU,KAAK;AAAA,EAC/B;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,aAAa,CAAC;AACjB,IAAG,eAAe,GAAG,yBAAyB,EAAE;AAChD,IAAG,WAAW,kBAAkB,SAAS,kHAAkH,QAAQ;AACjK,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,KAAK,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,oBAAoB,GAAG,iFAAiF,GAAG,GAAG,cAAc,EAAE,GAAG,iFAAiF,GAAG,CAAC;AACzN,IAAG,oBAAoB,GAAG,iFAAiF,GAAG,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc;AACjC,UAAM,YAAY,QAAQ;AAC1B,UAAM,iBAAiB,QAAQ;AAC/B,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,eAAkB,WAAW,OAAO,MAAM,SAAS,KAAK,OAAO,mBAAmB,OAAO,gBAAgB,SAAS,CAAC;AACzH,IAAG,UAAU;AACb,IAAG,YAAY,SAAS,OAAO,cAAc,OAAO,kBAAkB,QAAQ,MAAS;AACvF,IAAG,WAAW,eAAe,OAAO,WAAW,EAAE,cAAc,OAAO,UAAU,EAAE,aAAa,OAAO,SAAS,EAAE,eAAe,OAAO,WAAW,EAAE,WAAW,OAAO,OAAO,EAAE,mBAAmB,OAAO,mBAAmB,aAAa,UAAU,cAAc,GAAG,cAAc,CAAC,EAAE,wBAAwB,OAAO,wBAAwB,aAAa,UAAU,cAAc,GAAG,cAAc,CAAC,EAAE,OAAO,SAAS,EAAE,YAAY,YAAY,EAAE,YAAY,OAAO,eAAe,SAAS,CAAC,EAAE,YAAY,OAAO,QAAQ,EAAE,QAAQ,cAAc,EAAE,YAAY,OAAO,QAAQ;AAC/iB,IAAG,YAAY,UAAU,OAAO,0BAA0B,CAAC,OAAO,YAAY,CAAC,OAAO,kBAAkB,CAAC,OAAO,cAAc,OAAO,IAAI;AACzI,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,iBAAiB,IAAI,CAAC;AAC9C,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,QAAQ,SAAS,IAAI,IAAI,EAAE;AAAA,EACrD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,mEAAmE,GAAG,IAAI,yBAAyB,EAAE;AAAA,EACjO;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,CAAC,aAAa,OAAO,wBAAwB,IAAI,YAAY,IAAI,EAAE;AAAA,EACtF;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,EAAE,cAAc,OAAO,UAAU,EAAE,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO;AAAA,EAClI;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,sBAAsB,CAAC;AAC5C,IAAG,WAAW,UAAU,SAAS,mFAAmF,QAAQ;AAC1H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,oBAAoB,GAAG,6DAA6D,GAAG,GAAG,yBAAyB,CAAC;AACvH,IAAG,WAAW,GAAG,6DAA6D,GAAG,IAAI,eAAe,GAAG,GAAM,sBAAsB;AACnI,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,iBAAiB,GAAG,qDAAqD,GAAG,GAAG,MAAM,MAAS,oBAAoB,EAAE,eAAe,IAAI;AAC1I,IAAG,aAAa,EAAE;AAClB,IAAG,oBAAoB,GAAG,6DAA6D,GAAG,GAAG,yBAAyB,CAAC;AAAA,EACzH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,cAAc,OAAO,UAAU,EAAE,gBAAgB,OAAO,aAAa,CAAC,EAAE,eAAe,OAAO,qBAAqB,OAAO,OAAO,OAAO,kBAAkB,KAAK;AAC9M,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,cAAc,OAAO,oBAAoB,QAAQ,IAAI,EAAE;AAC/E,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,aAAa,OAAO,aAAa,CAAC;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,aAAa,CAAC;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,cAAc,OAAO,oBAAoB,WAAW,IAAI,EAAE;AAAA,EACpF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,sBAAsB,EAAE;AAC7C,IAAG,WAAW,UAAU,SAAS,mFAAmF,QAAQ;AAC1H,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,SAAS,OAAO,cAAc,OAAO,qBAAqB,OAAO,OAAO,OAAO,kBAAkB,SAAS,OAAO,MAAM;AACtI,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,cAAc,OAAO,UAAU,EAAE,gBAAgB,OAAO,aAAa,CAAC;AAAA,EACvH;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC5G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,oBAAoB,EAAE,2BAA2B,OAAO,mBAAmB;AAAA,EACtH;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;AAC9C,IAAG,WAAW,UAAU,SAAS,8EAA8E;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,KAAK,CAAC;AAAA,IAC5C,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,eAAe;AAAA,EACjD;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC5G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,OAAO,cAAc,EAAE,2BAA2B,OAAO,WAAW;AAAA,EAC/G;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,IAAG,WAAW,SAAS,SAAS,4EAA4E;AAC1G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,MAAM,GAAG;AAAA,EAC7C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,aAAa,SAAS,8EAA8E,QAAQ;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,cAAc,SAAS,+EAA+E,QAAQ;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,yBAAyB,CAAC;AAC/C,IAAG,WAAW,UAAU,SAAS,oGAAoG,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC,EAAE,YAAY,SAAS,sGAAsG,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,kBAAkB,SAAS,4GAA4G,QAAQ;AAChJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,gBAAgB,SAAS,0GAA0G,QAAQ;AAC5I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,QAAQ,SAAS,kGAAkG,QAAQ;AAC5H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,OAAO,MAAM,CAAC;AAAA,IAC7C,CAAC,EAAE,UAAU,SAAS,oGAAoG,QAAQ;AAChI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,OAAO,KAAK,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,qBAAqB,SAAS,+GAA+G,QAAQ;AACtJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,KAAK,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,SAAS,EAAE,gBAAgB,OAAO,eAAe,UAAU,SAAS,EAAE,SAAS,OAAO,eAAe,UAAU,aAAa,UAAU,QAAQ,EAAE,SAAS,KAAK,EAAE,aAAa,SAAS,EAAE,mBAAmB,OAAO,eAAe,EAAE,gBAAgB,OAAO,YAAY,EAAE,YAAY,UAAU,QAAQ,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,uBAAuB,UAAU,mBAAmB,EAAE,UAAU,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,KAAK,EAAE,iBAAiB,OAAO,aAAa,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,OAAO,eAAe,EAAE,2BAA2B,OAAO,uBAAuB;AAAA,EAC5wB;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,6DAA6D,GAAG,IAAI,yBAAyB,GAAG,UAAU;AACjI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,EAAE;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,mBAAmB,YAAY,IAAI,EAAE,WAAW,OAAO,cAAc,YAAY,IAAI,CAAC;AAC/G,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO;AAAA,EACnC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,uDAAuD,GAAG,GAAG,OAAO,CAAC;AAAA,EACjG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,cAAc,YAAY,QAAQ,SAAS,IAAI,EAAE;AAAA,EACtD;AACF;AACA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,kBAAkB,GAAG,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC;AAC1C,IAAG,cAAc,SAAS,SAAS,4DAA4D;AAC7F,YAAM,QAAW,cAAc,GAAG,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,gBAAgB,EAAE;AAAA,EACvB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,QAAQ,IAAI;AAClB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,MAAM,WAAW,OAAO,IAAI;AACrD,IAAG,YAAY,eAAe,OAAO,SAAS,oBAAoB,UAAU,MAAM,MAAM,MAAM;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,MAAM,MAAM,GAAG;AAAA,EAC5C;AACF;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,kBAAkB;AACpB;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACpC,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,SAAS;AAAA,EACT,QAAQ;AACV;AACA,SAAS,8DAA8D,IAAI,KAAK;AAAC;AACjF,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,eAAe,CAAC;AAAA,EACxG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,eAAe,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,OAAO,UAAU,OAAO,UAAU,OAAO,eAAe,OAAO,SAAS,OAAO,MAAM,CAAC;AAAA,EACjN;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,iBAAiB,OAAO,OAAO,OAAO,cAAc,eAAe,GAAG,KAAK,OAAO,iBAAiB,KAAK;AAAA,EAC5I;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mBAAmB,CAAC;AACzC,IAAG,WAAW,UAAU,SAAS,gGAAgG,QAAQ;AACvI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,KAAK,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,sBAAsB,OAAO,kBAAkB,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,iBAAiB,OAAO,aAAa,EAAE,QAAQ,OAAO,OAAO,EAAE,QAAQ,OAAO,QAAQ,EAAE,SAAS,OAAO,QAAQ;AAAA,EACnR;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,oBAAoB,GAAG,+DAA+D,GAAG,GAAG,MAAM;AACrG,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,oBAAoB,GAAG,+DAA+D,GAAG,GAAG,mBAAmB,CAAC;AAAA,EACrH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,kBAAkB,IAAI,EAAE;AAChD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,YAAY,OAAO,OAAO,OAAO,SAAS,eAAe,GAAG,KAAK,OAAO,cAAc,GAAG;AAC3H,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,IAAI,EAAE;AAAA,EAC5C;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,WAAW,QAAQ,SAAS,2EAA2E,QAAQ;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC,EAAE,UAAU,SAAS,6EAA6E,QAAQ;AACzG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,YAAY,SAAS,+EAA+E,QAAQ;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,WAAW,SAAS,8EAA8E,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC,EAAE,UAAU,SAAS,+EAA+E;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC,EAAE,qBAAqB,SAAS,wFAAwF,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,cAAc,OAAO,WAAW,EAAE,WAAW,OAAO,QAAQ,EAAE,kBAAkB,OAAO,gBAAgB,MAAS,EAAE,WAAW,OAAO,gBAAgB,EAAE,gBAAgB,OAAO,YAAY,EAAE,eAAe,OAAO,WAAW,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,qBAAqB,OAAO,WAAW,aAAa,EAAE,sBAAsB,OAAO,WAAW,cAAc,EAAE,iBAAiB,OAAO,WAAW,SAAS,EAAE,mBAAmB,OAAO,eAAe,EAAE,iBAAiB,OAAO,aAAa,EAAE,yBAAyB,OAAO,qBAAqB,EAAE,2BAA2B,OAAO,uBAAuB;AAAA,EACttB;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB;AAAA,EACtC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAS,gBAAgB,sBAAyB,cAAc;AAAA,EACpG;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,WAAW,QAAQ,SAAS,2EAA2E,QAAQ;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,gBAAgB,SAAY,OAAO,cAAc,SAAS,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,MAAM,EAAE,gBAAgB,OAAO,SAAS,gBAAgB,OAAO,EAAE,sBAAsB,OAAO,WAAW,cAAc,EAAE,uBAAuB,OAAO,WAAW,eAAe,EAAE,qBAAqB,OAAO,WAAW,aAAa,EAAE,iBAAiB,OAAO,SAAS,MAAM,EAAE,mBAAmB,CAAC,CAAC,OAAO,kBAAkB,OAAO,SAAS,mBAAmB,WAAW,EAAE,iBAAiB,OAAO,WAAW,SAAS;AAAA,EAC9nB;AACF;AACA,IAAM,oCAAN,MAAM,kCAAiC;AAAA,EACrC,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,kCAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,mCAAkC;AACrE;AAGA,kCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,iCAAiC,EAAE,CAAC;AACvD,CAAC;AAbL,IAAM,mCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yCAAN,MAAM,uCAAsC;AAAA,EAC1C,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,uCAAK,OAAO,SAAS,8CAA8C,mBAAmB;AACpF,SAAO,KAAK,qBAAqB,wCAAuC;AAC1E;AAGA,uCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,uCAAuC,EAAE,CAAC;AAC7D,CAAC;AAbL,IAAM,wCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uCAAuC,CAAC;AAAA,IAC9G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AAIZ,SAAK,YAAY;AAIjB,SAAK,eAAe;AAIpB,SAAK,SAAS,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,kBAAkB,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,OAAO;AACvB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AA6BF;AA3BI,+BAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,SAAO,KAAK,qBAAqB,gCAA+B;AAClE;AAGA,+BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,EAC1C,gBAAgB,SAAS,6CAA6C,IAAI,KAAK,UAAU;AACvF,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,uCAAuC,GAAG,WAAW;AAAA,IACnF;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,cAAc;AAAA,IACd,gBAAgB,CAAC,GAAG,YAAY,gBAAgB;AAAA,EAClD;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AACF,CAAC;AAvEL,IAAM,gCAAN;AAAA,CA0EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,uCAAuC;AAAA,QAC5C,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,oBAAoB;AAC3B,SAAO;AACT;AAKA,SAAS,cAAc,MAAM;AAE3B,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT,OAAO;AAEL,QAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAOA,SAAS,mBAAmB,KAAK,OAAO;AACtC,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,OAAO,SAAS,MAAM;AACzB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,IAAI,KAAK;AACvB,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOA,SAAS,mBAAmB,KAAK,WAAW;AAC1C,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO,CAAC,WAAW;AACtB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,IAAI,SAAS;AAC3B,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,KAAK,MAAM;AAClC,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO,CAAC,MAAM;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,IAAI,IAAI;AACtB,MAAI,YAAY,QAAW;AACzB,WAAO;AAAA,EACT;AACA,YAAU;AACV,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,QAAQ;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAU,QAAQ,MAAM,CAAC,CAAC;AAE1B,UAAI,YAAY,UAAa,YAAY,MAAM;AAC7C,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,OAAO,SAAO,cAAc,IAAI,EAAE,KAAK,IAAI,IAAI;AACxD;AAqCA,SAAS,mBAAmB,MAAM,MAAM,IAAI;AAC1C,MAAI,QAAQ,IAAI;AACd,UAAM,WAAW,KAAK,OAAO,SAAO,CAAC,CAAC,GAAG,EAAE,IAAI,SAAO,IAAI,SAAS,GAAG,CAAC;AACvE,UAAM,UAAU,IAAI,IAAI,SAAS,IAAI,UAAQ,CAAC,GAAG,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC;AAClE,UAAM,YAAY,SAAS,OAAO,CAAC,MAAM,SAAS;AAChD,YAAM,YAAY,KAAK,KAAK,GAAG;AAC/B,YAAM,SAAS,QAAQ,IAAI,SAAS;AACpC,UAAI,QAAQ;AACV,aAAK,IAAI,QAAQ,OAAO,IAAI,QAAQ;AACpC,aAAK,SAAS;AACd,eAAO,SAAS,KAAK,IAAI;AAAA,MAC3B,OAAO;AACL,aAAK,IAAI,QAAQ;AACjB,aAAK,KAAK,IAAI;AAAA,MAChB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,WAAO,UAAU,QAAQ,WAAS,MAAM,QAAQ,CAAC;AAAA,EACnD,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,IAAI,eAAe,YAAY;AACtC,aAAO,CAAC,KAAK,KAAK,GAAG,KAAK,SAAS,QAAQ,WAAS,MAAM,QAAQ,CAAC,CAAC;AAAA,IACtE,OAAO;AACL,aAAO,CAAC,KAAK,GAAG;AAAA,IAClB;AAAA,EACF;AACF;AACA,IAAM,kCAAN,MAAM,gCAA+B;AAAA,EACnC,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,gCAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,SAAO,KAAK,qBAAqB,iCAAgC;AACnE;AAGA,gCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,iCAAiC,EAAE,CAAC;AACvD,CAAC;AAbL,IAAM,iCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gCAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,WAAW,OAAO,WAAW;AAAA,EACpC;AAAA,EACA,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAYF;AAVI,8BAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,+BAA8B;AACjE;AAGA,8BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,+BAA+B,EAAE,CAAC;AACrD,CAAC;AAhBL,IAAM,+BAAN;AAAA,CAmBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,WAAW,OAAO,WAAW;AAAA,EACpC;AAYF;AAVI,+BAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,SAAO,KAAK,qBAAqB,gCAA+B;AAClE;AAGA,+BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,6BAA6B,EAAE,CAAC;AACnD,CAAC;AAbL,IAAM,gCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,wBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,qBAAqB,IAAI,QAAQ;AAAA,EACxC;AAAA,EACA,IAAI,sBAAsB;AACxB,WAAO,KAAK,mBAAmB,aAAa;AAAA,EAC9C;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB,KAAK,MAAS;AAAA,EACxC;AAYF;AAVI,sBAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AAGA,sBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,sBAAqB;AAChC,CAAC;AAnBL,IAAM,uBAAN;AAAA,CAsBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qCAAN,MAAM,mCAAkC;AAAA,EACtC,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,mCAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,SAAO,KAAK,qBAAqB,oCAAmC;AACtE;AAGA,mCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qCAAqC,EAAE,CAAC;AAC3D,CAAC;AAbL,IAAM,oCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,sBAAsB,KAAK;AAAA,EACzC;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,wBAAwB,KAAK;AAAA,EAC3C;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,4BAA4B,KAAK;AAAA,EAC/C;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,2BAA2B,KAAK;AAAA,EAC9C;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,qBAAqB,cAAc;AAAA,IAC1C;AAAA,EACF;AAyDF;AAvDI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,EACpC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,8BAA8B,GAAG,WAAW;AACxE,MAAG,eAAe,UAAU,gCAAgC,GAAG,WAAW;AAC1E,MAAG,eAAe,UAAU,+BAA+B,GAAG,WAAW;AACzE,MAAG,eAAe,UAAU,mCAAmC,GAAG,WAAW;AAAA,IAC/E;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAC/E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,0BAA0B,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,IAC9E,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IAC/D,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACrD,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACtD,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,IACzD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACrE,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACrD,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,IAC5C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACrD,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,IAClE,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,IACpF,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,IAClE,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,oBAAoB,CAAC,GAAG,gBAAgB,oBAAoB;AAAA,IAC5D,sBAAsB,CAAC,GAAG,kBAAkB,sBAAsB;AAAA,IAClE,0BAA0B,CAAC,GAAG,sBAAsB,0BAA0B;AAAA,IAC9E,yBAAyB,CAAC,GAAG,qBAAqB,yBAAyB;AAAA,EAC7E;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AA9EL,IAAM,2BAAN;AAAA,CAiFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,QACnC,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,QACrC,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,QACxC,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uCAAN,MAAM,qCAAoC;AAAA,EACxC,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,qCAAK,OAAO,SAAS,4CAA4C,mBAAmB;AAClF,SAAO,KAAK,qBAAqB,sCAAqC;AACxE;AAGA,qCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qCAAqC,EAAE,CAAC;AAC3D,CAAC;AAbL,IAAM,sCAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,+BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AAKZ,SAAK,YAAY;AAIjB,SAAK,SAAS,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,kBAAkB,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,KAAK;AACnB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AA4BF;AA1BI,6BAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,SAAO,KAAK,qBAAqB,8BAA6B;AAChE;AAGA,6BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,EACxC,gBAAgB,SAAS,2CAA2C,IAAI,KAAK,UAAU;AACrF,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,qCAAqC,GAAG,WAAW;AAAA,IACjF;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,gBAAgB,CAAC,GAAG,YAAY,gBAAgB;AAAA,EAClD;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AACF,CAAC;AAnEL,IAAM,8BAAN;AAAA,CAsEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,QAC1C,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,IAAI,WAAW;AACb,WAAO,KAAK,kBAAkB,KAAK;AAAA,EACrC;AAwBF;AAtBI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,EACpC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,kCAAkC,GAAG,WAAW;AAAA,IAC9E;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,gBAAgB,CAAC,GAAG,YAAY,gBAAgB;AAAA,EAClD;AACF,CAAC;AAzBL,IAAM,2BAAN;AAAA,CA4BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,QACvC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,UAAU,OAAO,UAAU,EAAE;AAClC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,WAAW;AAET,QAAI,KAAK,cAAc,KAAK,YAAY;AACtC,YAAM,WAAW,KAAK;AACtB,WAAK,gBAAgB,SAAS,WAAW,KAAK,OAAO;AACrD,WAAK,uBAAuB,KAAK,WAAW,KAAK,IAAI;AACrD,WAAK,eAAe,iBAAiB,UAAU,KAAK,oBAAoB;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,sBAAsB;AAC7B,WAAK,eAAe,oBAAoB,UAAU,KAAK,oBAAoB;AAC3E,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,UAAU,SAAS;AACjB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,MAAM,MAAM;AAClB,0BAAsB,MAAM;AAC1B,WAAK,aAAa,IAAI;AACtB,WAAK,aAAa,IAAI;AACtB,WAAK,aAAa;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,QAAI;AACJ,QAAI,KAAK,aAAa,KAAK,gBAAgB;AACzC,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY;AAAA,IACd;AACA,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,IACnB,CAAC;AACD,SAAK,iBAAiB,KAAK;AAC3B,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAuCF;AArCI,mBAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,oBAAmB;AACtD;AAGA,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,EAClC,WAAW,CAAC,GAAG,kBAAkB;AAAA,EACjC,UAAU;AAAA,EACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,UAAU,IAAI,cAAc,IAAI,EAAE,SAAS,IAAI,aAAa,IAAI;AAAA,IACjF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA1FL,IAAM,oBAAN;AAAA,CA6FC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,aAAa,MAAM;AAC1B,QAAM,MAAM;AAAA,IACV,MAAM,CAAC;AAAA,IACP,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,EACV;AACA,MAAI,MAAM;AACR,eAAW,OAAO,MAAM;AACtB,UAAI,IAAI,YAAY;AAClB,YAAI,KAAK,KAAK,GAAG;AAAA,MACnB,WAAW,IAAI,aAAa;AAC1B,YAAI,MAAM,KAAK,GAAG;AAAA,MACpB,OAAO;AACL,YAAI,OAAO,KAAK,GAAG;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,SAAO;AAAA,IACL,MAAM,iBAAiB,OAAO,IAAI;AAAA,IAClC,QAAQ,iBAAiB,OAAO,MAAM;AAAA,IACtC,OAAO,iBAAiB,OAAO,KAAK;AAAA,IACpC,OAAO,KAAK,MAAM,iBAAiB,GAAG,CAAC;AAAA,EACzC;AACF;AAIA,SAAS,iBAAiB,SAAS;AACjC,SAAO,SAAS,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,OAAO,CAAC,KAAK;AACxE;AACA,SAAS,gBAAgB,KAAK;AAC5B,QAAM,YAAY,aAAa,GAAG;AAClC,SAAO,CAAC;AAAA,IACN,MAAM;AAAA,IACN,SAAS,UAAU;AAAA,EACrB,GAAG;AAAA,IACD,MAAM;AAAA,IACN,SAAS,UAAU;AAAA,EACrB,GAAG;AAAA,IACD,MAAM;AAAA,IACN,SAAS,UAAU;AAAA,EACrB,CAAC;AACH;AAWA,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AAMZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,SAAS;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,OAAO,cAAc;AAClC,UAAM,aAAa,OAAO,oBAAoB;AAC9C,QAAI,CAAC,QAAQ,MAAM,SAAS,GAAG;AAC7B,YAAM,IAAI,MAAM;AAAA,2CACqB,SAAS,iCAAiC;AAAA,IACjF;AAEA,QAAI,CAAC,cAAc,MAAM,eAAe,GAAG;AACzC,YAAM,IAAI,MAAM;AAAA,2CACqB,eAAe,iCAAiC;AAAA,IACvF;AACA,UAAM,IAAI,kBAAkB,WAAW,KAAK;AAC5C,SAAK,YAAY,IAAI,MAAM,CAAC;AAC5B,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,WAAK,UAAU,CAAC,IAAI;AAAA,IACtB;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,mBAAmB;AACvB,UAAI,MAAM;AACR,2BAAmB,UAAU,GAAG;AAAA,MAClC;AAGA,YAAM,WAAW,cAAc,IAAI,GAAG;AACtC,UAAI,OAAO,UAAU;AACnB,YAAI,YAAY;AACd,gBAAM,QAAQ,cAAc;AAC5B,8BAAoB,gBAAgB,KAAK,KAAK;AAAA,QAChD,OAAO;AACL,8BAAoB;AAAA,QACtB;AAAA,MACF;AACA,WAAK,OAAO,GAAG,gBAAgB;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AACnB,QAAI,YAAY,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,YAAY,aAAa;AAC9B,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,YAAM,IAAI,MAAM,mBAAmB,UAAU,eAAe,WAAW;AAAA,0CACnC;AAAA,IACtC;AACA,UAAM,IAAI,KAAK,UAAU;AACzB,kBAAc;AACd,WAAO,aAAa,GAAG;AACrB,WAAK,UAAU,UAAU,KAAK;AAC9B,oBAAc,aAAa;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS;AACb,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,YAAM,IAAI,MAAM,kBAAkB,OAAO,8CAA8C;AAAA,IACzF;AACA,QAAI,MAAM;AACV,eAAW;AACX,WAAO,WAAW,GAAG;AACnB,aAAO,KAAK,UAAU,OAAO;AAC7B,iBAAW,UAAU,UAAU,KAAK;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,UAAU,UAAU;AAC/B,WAAO,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM,WAAW,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAK;AAChB,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AACV,UAAM,aAAa,KAAK,UAAU;AAElC,UAAM,aAAa,KAAK,IAAI,GAAG,WAAW,SAAS,CAAC,EAAE,SAAS,CAAC;AAChE,aAAS,YAAY,YAAY,cAAc,GAAG,cAAc,GAAG;AACjE,YAAM,UAAU,MAAM;AACtB,UAAI,UAAU,cAAc,OAAO,KAAK,UAAU,OAAO,GAAG;AAC1D,eAAO,KAAK,UAAU,OAAO;AAC7B,cAAM;AAAA,MACR;AAAA,IACF;AACA,WAAO,MAAM;AAAA,EACf;AACF;AACA,IAAI;AAAA,CACH,SAAUA,OAAM;AACf,EAAAA,MAAK,IAAI,IAAI;AACb,EAAAA,MAAK,MAAM,IAAI;AACf,EAAAA,MAAK,QAAQ,IAAI;AACjB,EAAAA,MAAK,QAAQ,IAAI;AACjB,EAAAA,MAAK,MAAM,IAAI;AACf,EAAAA,MAAK,OAAO,IAAI;AAClB,GAAG,SAAS,OAAO,CAAC,EAAE;AACtB,IAAI;AAAA,CACH,SAAUC,gBAAe;AACxB,EAAAA,eAAc,KAAK,IAAI;AACvB,EAAAA,eAAc,MAAM,IAAI;AAC1B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAUC,WAAU;AACnB,EAAAA,UAAS,QAAQ,IAAI;AACrB,EAAAA,UAAS,OAAO,IAAI;AACtB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAUC,aAAY;AACrB,EAAAA,YAAW,UAAU,IAAI;AACzB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,OAAO,IAAI;AACxB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgB,QAAQ,IAAI;AAC5B,EAAAA,iBAAgB,MAAM,IAAI;AAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAUC,gBAAe;AACxB,EAAAA,eAAc,QAAQ,IAAI;AAC1B,EAAAA,eAAc,OAAO,IAAI;AACzB,EAAAA,eAAc,YAAY,IAAI;AAC9B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,UAAU,IAAI;AAC9B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,8BAAN,MAAM,4BAA2B;AAAA,EAC/B,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,WAAW;AAC5B,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AACd,SAAK,YAAY,QAAQ;AACzB,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,KAAK;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY,YAAY;AAC7B,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AACnB,SAAK,YAAY,aAAa;AAC9B,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY,WAAW;AAC5B,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY,WAAW,KAAK;AACjC,SAAK,YAAY,kBAAkB,KAAK;AACxC,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AACf,SAAK,YAAY,SAAS;AAC1B,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,OAAO;AACZ,SAAK,YAAY,MAAM;AACvB,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AACd,SAAK,UAAU,KAAK,YAAY,GAAG;AAAA,EACrC;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,QAAQ;AACrB,QAAI,WAAW,eAAe,WAAW,cAAc,WAAW,aAAa,WAAW,YAAY;AACpG,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,YAAY,aAAa,KAAK;AACnC,SAAK,kBAAkB;AACvB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB;AACrB,QAAI,MAAM;AACV,QAAI,KAAK,OAAO,WAAW;AACzB,UAAI,OAAO,KAAK,OAAO,cAAc,UAAU;AAC7C,eAAO,MAAM,KAAK,OAAO;AAAA,MAC3B,WAAW,OAAO,KAAK,OAAO,cAAc,YAAY;AACtD,cAAM,MAAM,KAAK,OAAO,UAAU;AAAA,UAChC,KAAK,KAAK;AAAA,UACV,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,WAAW,KAAK;AAAA,QAClB,CAAC;AACD,YAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAO,MAAM;AAAA,QACf,WAAW,OAAO,QAAQ,UAAU;AAClC,gBAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,qBAAW,KAAK,MAAM;AACpB,gBAAI,IAAI,CAAC,MAAM,MAAM;AACnB,qBAAO,IAAI,CAAC;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,CAAC,KAAK,WAAW;AACrC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,cAAc,KAAK;AACtC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,cAAc,MAAM;AACvC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,SAAS;AACX,UAAM,SAAS,KAAK;AACpB,QAAI,MAAM,MAAM,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,YAAY;AACjB,SAAK,WAAW,OAAO,UAAU,EAAE;AACnC,SAAK,cAAc;AAAA,MACjB,oBAAoB,WAAS,KAAK,iBAAiB,KAAK;AAAA,MACxD,YAAY,WAAS,KAAK,SAAS,KAAK,KAAK;AAAA,MAC7C,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK,UAAU;AAAA,MACzB,iBAAiB,KAAK,UAAU;AAAA,MAChC,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,cAAc,MAAM,KAAK,aAAa;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,QAAI,QAAQ;AACZ,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,UAAU,KAAK,OAAO,QAAQ,QAAW;AAC9D,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,MAAM,KAAK,OAAO,cAAc,KAAK,KAAK,KAAK,OAAO,IAAI;AAChE,YAAM,WAAW,KAAK,OAAO;AAC7B,UAAI,UAAU;AACZ,gBAAQ,SAAS,UAAU,GAAG;AAAA,MAChC,WAAW,UAAU,QAAW;AAC9B,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,QAAQ;AACb,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,WAAW,KAAK;AACjC,WAAK,iBAAiB,UAAU,QAAQ,UAAU,SAAY,KAAK,UAAU,KAAK,IAAI;AACtF,WAAK,GAAG,aAAa;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,MACZ,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,MACZ,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,UAAM,eAAe,MAAM,WAAW,KAAK;AAC3C,UAAM,WAAW,QAAQ,KAAK,UAAU,QAAQ,KAAK,QAAQ,QAAQ,KAAK,MAAM,QAAQ,KAAK,QAAQ,QAAQ,KAAK;AAClH,QAAI,YAAY,cAAc;AAC5B,YAAM,eAAe;AACrB,YAAM,gBAAgB;AACtB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN;AAAA,QACA,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,WAAW,KAAK;AAAA,QAChB,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,aAAa,KAAK;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,MACZ,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,UAAM,OAAO,MAAM,KAAK,OAAK,EAAE,SAAS,KAAK,OAAO,IAAI;AACxD,WAAO,MAAM;AAAA,EACf;AAAA,EACA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,mBAAmB,EAAE;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,SAAK,WAAW,KAAK,KAAK,GAAG;AAAA,EAC/B;AAAA,EACA,eAAe,QAAQ,KAAK;AAC1B,UAAM,cAAc,OAAO,mBAAmB,OAAO,OAAO,kBAAkB;AAC9E,WAAO,OAAO,eAAe,IAAI,QAAQ,cAAc;AAAA,EACzD;AAyEF;AAvEI,4BAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,6BAA4B;AAC/D;AAGA,4BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,EACnC,UAAU;AAAA,EACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,eAAO,IAAI,QAAQ;AAAA,MACrB,CAAC,EAAE,QAAQ,SAAS,qDAAqD;AACvE,eAAO,IAAI,OAAO;AAAA,MACpB,CAAC,EAAE,SAAS,SAAS,oDAAoD,QAAQ;AAC/E,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B,CAAC,EAAE,YAAY,SAAS,uDAAuD,QAAQ;AACrF,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,WAAW,SAAS,sDAAsD,QAAQ;AACnF,eAAO,IAAI,UAAU,MAAM;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,gBAAgB;AAClC,MAAG,YAAY,SAAS,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,UAAU,IAAI,EAAE,aAAa,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI,MAAM;AAAA,IACjI;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,cAAc;AAAA,IACd,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,YAAY,GAAG,SAAS,YAAY,SAAS,GAAG,CAAC,GAAG,yBAAyB,GAAG,UAAU,GAAG,CAAC,GAAG,yBAAyB,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,QAAQ,yBAAyB,GAAG,CAAC,GAAG,QAAQ,mBAAmB,GAAG,CAAC,GAAG,QAAQ,qBAAqB,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,CAAC,GAAG,OAAO,CAAC;AAAA,EAC3a,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,oBAAoB,GAAG,mDAAmD,GAAG,GAAG,SAAS,CAAC;AAC7F,MAAG,oBAAoB,GAAG,mDAAmD,GAAG,CAAC;AACjF,MAAG,oBAAoB,GAAG,mDAAmD,GAAG,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,MAAM,CAAC;AACtJ,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,eAAe,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,GAAG,IAAI;AAC3E,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,OAAO,iBAAiB,CAAC,IAAI,gBAAgB,IAAI,aAAa,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC5H,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,OAAO,eAAe,IAAI,EAAE;AACjD,MAAG,UAAU;AACb,MAAG,cAAc,CAAC,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,QAAQ,CAAC,wOAAwO;AAAA,EACjP,iBAAiB;AACnB,CAAC;AA1VL,IAAM,6BAAN;AAAA,CA6VC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4CV,SAAS,CAAC,gBAAgB;AAAA,MAC1B,QAAQ,CAAC,sNAAsN;AAAA,IACjO,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAAA,IAC/B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,aAAa;AAClB,SAAK,wBAAwB;AAC7B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,WAAW,OAAO,UAAU,EAAE;AACnC,SAAK,aAAa,OAAO,eAAe,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EAC5D;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAChB,SAAK,mBAAmB,GAAG;AAAA,EAC7B;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,QAAI,KAAK,UAAU;AACjB,YAAM,WAAW,aAAa,KAAK,QAAQ;AAC3C,WAAK,qBAAqB,kBAAkB,UAAU,KAAK,QAAQ;AAAA,IACrE;AACA,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,QAAI,MAAM;AACV,QAAI,KAAK,YAAY;AACnB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,gBAAgB,MAAM,GAAG;AAChC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,gBAAgB,MAAM,GAAG;AAChC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,MAAM,KAAK,SAAS,KAAK,GAAG;AAClC,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,IAAI,GAAG;AAAA,MAChB,WAAW,OAAO,QAAQ,UAAU;AAClC,cAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,mBAAW,KAAK,MAAM;AACpB,cAAI,IAAI,CAAC,MAAM,MAAM;AACnB,mBAAO,IAAI,CAAC;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,uBAAuB;AACjC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW,KAAK,KAAK,GAAG,GAAG;AAClC,WAAK,GAAG,aAAa;AAAA,IACvB;AAAA,EACF;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,SAAK,SAAS,KAAK,iCACd,QADc;AAAA,MAEjB,YAAY,KAAK;AAAA,MACjB,WAAW;AAAA,IACb,EAAC;AAAA,EACH;AAAA,EACA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,UAAM,cAAc,MAAM,WAAW,KAAK;AAC1C,UAAM,WAAW,QAAQ,KAAK,UAAU,QAAQ,KAAK,QAAQ,QAAQ,KAAK,MAAM,QAAQ,KAAK,QAAQ,QAAQ,KAAK;AAClH,UAAM,UAAU,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM;AAC7D,QAAI,YAAY,eAAe,SAAS;AACtC,YAAM,eAAe;AACrB,YAAM,gBAAgB;AACtB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN;AAAA,QACA,KAAK,KAAK;AAAA,QACV,YAAY,KAAK;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,KAAK;AAAA,MACV,YAAY,KAAK;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,MAAM,KAAK,SAAS;AACrC,SAAK,WAAW;AAChB,UAAM,YAAY,aAAa,KAAK,QAAQ;AAC5C,SAAK,gBAAgB,gBAAgB,KAAK,QAAQ;AAClD,SAAK,qBAAqB,kBAAkB,WAAW,KAAK,QAAQ;AAAA,EACtE;AAAA,EACA,eAAe;AACb,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,gBAAgB,KAAK,UAAU,SAAS;AAAA,EAChE;AA4DF;AA1DI,2BAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,4BAA2B;AAC9D;AAGA,2BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,EAClC,UAAU;AAAA,EACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,WAAW,SAAS,qDAAqD,QAAQ;AAC7F,eAAO,IAAI,UAAU,MAAM;AAAA,MAC7B,CAAC,EAAE,cAAc,SAAS,wDAAwD,QAAQ;AACxF,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,QAAQ;AAC1B,MAAG,YAAY,UAAU,IAAI,WAAW,IAAI,EAAE,SAAS,IAAI,oBAAoB,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,uBAAuB;AAAA,IACvB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,SAAS,SAAS,cAAc,GAAG,CAAC,QAAQ,QAAQ,YAAY,MAAM,GAAG,OAAO,SAAS,YAAY,cAAc,YAAY,UAAU,aAAa,gBAAgB,YAAY,YAAY,GAAG,CAAC,QAAQ,QAAQ,YAAY,MAAM,GAAG,YAAY,cAAc,OAAO,SAAS,YAAY,cAAc,YAAY,UAAU,aAAa,gBAAgB,YAAY,YAAY,CAAC;AAAA,EACpY,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,MAAG,iBAAiB,GAAG,0CAA0C,GAAG,GAAG,MAAM,MAAM,UAAU;AAAA,IAC/F;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,aAAa;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,0BAA0B;AAAA,EACzC,QAAQ,CAAC,kLAAkL;AAAA,EAC3L,iBAAiB;AACnB,CAAC;AA3KL,IAAM,4BAAN;AAAA,CA8KC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,SAAS,CAAC,0BAA0B;AAAA,MACpC,QAAQ,CAAC,wJAAwJ;AAAA,IACnK,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,qBAAqB,OAAO;AACnC,SAAO,iBAAiB,aAAa,QAAQ,MAAM,eAAe,CAAC;AACrE;AAUA,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,UAAU,OAAO,UAAU,EAAE;AAClC,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,mBAAmB,QAAQ,gBAAgB,gBAAgB,KAAK,UAAU,UAAU;AAC9F,WAAK,YAAY,QAAQ,gBAAgB,YAAY;AAAA,IACvD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,SAAK,aAAa;AAClB,SAAK,QAAQ,UAAU,OAAO,UAAU;AACxC,QAAI,KAAK,cAAc;AACrB,WAAK,qBAAqB;AAC1B,WAAK,QAAQ,KAAK;AAAA,QAChB;AAAA,QACA,SAAS,KAAK;AAAA,QACd,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,UAAU,iBAAiB;AAEjC,UAAM,YAAY,MAAM,OAAO,UAAU,SAAS,WAAW;AAC7D,QAAI,cAAc,KAAK,SAAS,KAAK,QAAQ;AAC3C,YAAM,eAAe;AACrB,WAAK,aAAa;AAClB,YAAM,eAAe,qBAAqB,KAAK;AAC/C,YAAM,UAAU,UAAU,UAAU,UAAU,YAAY,UAAU;AACpE,WAAK,eAAe,QAAQ,UAAU,QAAM,KAAK,UAAU,EAAE,CAAC;AAC9D,YAAM,eAAe,UAAU,UAAU,UAAU,cAAc,WAAW,EAAE,KAAK,UAAU,OAAO,CAAC,EAAE,UAAU,QAAM,KAAK,KAAK,IAAI,YAAY,CAAC;AAClJ,WAAK,aAAa,IAAI,YAAY;AAClC,WAAK,UAAU,KAAK;AAAA,QAClB;AAAA,QACA,SAAS,KAAK;AAAA,QACd,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,KAAK,OAAO,cAAc;AACxB,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,qBAAqB,KAAK;AAC9B,UAAM,IAAI,UAAU,aAAa;AACjC,UAAM,IAAI,UAAU,aAAa;AACjC,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ,MAAM,OAAO,GAAG,CAAC;AAAA,IAChC;AACA,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ,MAAM,MAAM,GAAG,CAAC;AAAA,IAC/B;AACA,SAAK,QAAQ,UAAU,IAAI,UAAU;AACrC,SAAK,SAAS,KAAK;AAAA,MACjB;AAAA,MACA,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAwBF;AAtBI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,EACjC,QAAQ;AAAA,IACN,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,IAC7C,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,EAC/C;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAtGL,IAAM,qBAAN;AAAA,CAyGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,SAAS,OAAO,WAAW;AAChC,SAAK,aAAa,iCACb,KAAK,OAAO,iBADC;AAAA,MAEhB,UAAU,KAAK,OAAO;AAAA,IACxB;AAAA,EACF;AAyBF;AAvBI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,EACjC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,EAC3D,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,oBAAoB,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAAA,IACpG;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,OAAO,eAAe,cAAc,IAAI,EAAE;AAAA,IACjE;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,eAAe;AACjB,CAAC;AA9BL,IAAM,2BAAN;AAAA,CAiCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,uBAAuB,MAAM,KAAK;AACvC,WAAO;AAAA,EACT;AAYF;AAVI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;AAChC,CAAC;AAbL,IAAM,2BAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,oCAAN,MAAM,kCAAiC;AAAA,EACrC,cAAc;AACZ,SAAK,KAAK,OAAO,gBAAgB;AAAA,EACnC;AAAA,EACA,WAAW;AACT,SAAK,GAAG,mBAAmB,KAAK,eAAe,UAAU,mBACpD,KAAK,iBACP;AAAA,MACD,UAAU,SAAS,OAAO;AAAA,QACxB,WAAW,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAgBF;AAdI,kCAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,mCAAkC;AACrE;AAGA,kCAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,EACtC,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF,CAAC;AA7BL,IAAM,mCAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAc,IAAI,eAAe,QAAQ;AAM/C,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAC5E,IAAM,gCAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,iBAAiB,IAAI,aAAa,KAAK;AAC5C,SAAK,oBAAoB,OAAO,CAAC,CAAC;AAClC,SAAK,WAAW;AAChB,SAAK,YAAY,OAAO,eAAe,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO;AACzD,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,iBAAiB,OAAO,uBAAuB;AACpD,SAAK,KAAK,OAAO,iBAAiB;AAAA,EACpC;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,KAAK,gBAAgB,KAAK,KAAK,YAAY,CAAC,CAAC,EAAE,OAAO;AAAA,EAClF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,KAAK,GAAG;AAElB,UAAI,KAAK,QAAQ,KAAK,GAAG,GAAG;AAC1B,aAAK,UAAU;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACjB;AAAA,MACF,OAAO;AACL,aAAK,UAAU;AAAA,UACb,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,QAAQ,WAAW,KAAK;AAAA,IAC/B;AACA,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,QAAQ,WAAW,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU,KAAK,KAAK,GAAG,GAAG;AACjC,UAAI,WAAW,KAAK,SAAS;AAC3B,aAAK,QAAQ,QAAQ,KAAK;AAAA,MAC5B,OAAO;AACL,aAAK,QAAQ,MAAM,KAAK;AAAA,MAC1B;AACA,WAAK,GAAG,aAAa;AAAA,IACvB;AAKA,QAAI,KAAK,QAAQ,KAAK,GAAG,KAAK,KAAK,aAAa,gBAAgB,KAAK,mBAAmB,KAAK,KAAK,QAAQ,GAAG;AAC3G,YAAM,UAAU,KAAK;AACrB,YAAM,eAAe,KAAK,SAAS,OAAO,SAAO,QAAQ,MAAM,KAAK,UAAQ,SAAS,GAAG,CAAC;AACzF,UAAI,KAAK,eAAe;AACtB,YAAI,aAAa,UAAU,aAAa,WAAW,KAAK,IAAI,MAAM,QAAQ;AACxE,eAAK,cAAc,cAAc,gBAAgB;AAAA,QACnD,OAAO;AACL,eAAK,cAAc,cAAc,gBAAgB;AAAA,QACnD;AAAA,MACF;AACA,WAAK,kBAAkB,IAAI,YAAY;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc,QAAQ;AACpB,SAAK,eAAe,KAAK;AAAA,MACvB,OAAO;AAAA,MACP,KAAK,KAAK;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,eAAe,OAAO;AAErC,SAAK,WAAW,CAAC,GAAG,KAAK,SAAS,OAAO,SAAO,CAAC,MAAM,MAAM,KAAK,UAAQ,SAAS,GAAG,CAAC,CAAC;AAExF,QAAI,eAAe;AACjB,WAAK,WAAW,CAAC,GAAG,KAAK,UAAU,GAAG,MAAM,KAAK;AAAA,IACnD;AAEA,SAAK,eAAe,WAAW,CAAC,GAAG,KAAK,QAAQ;AAEhD,SAAK,eAAe,aAAa;AAAA,MAC/B,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAqEF;AAnEI,8BAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,+BAA8B;AACjE;AAGA,8BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,EACrC,WAAW,SAAS,mCAAmC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,IACtE;AAAA,EACF;AAAA,EACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,EACtC,cAAc,SAAS,0CAA0C,IAAI,KAAK;AACxE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,eAAe,SAAS,4DAA4D,QAAQ;AACxG,eAAO,IAAI,cAAc,MAAM;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,KAAK;AAAA,IACL,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,EACxD;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,0BAA0B,GAAG,UAAU,OAAO,GAAG,CAAC,GAAG,wBAAwB,GAAG,QAAQ,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,QAAQ,YAAY,GAAG,UAAU,SAAS,GAAG,CAAC,GAAG,sBAAsB,CAAC;AAAA,EAC5U,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,OAAO,CAAC;AAC7F,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,CAAC;AACnF,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,OAAO,CAAC;AAAA,IAC/F;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,eAAe,OAAO,OAAO,IAAI,YAAY,YAAY,IAAI,EAAE;AAC7G,MAAG,UAAU;AACb,MAAG,eAAe,IAAI,eAAe,OAAO,OAAO,IAAI,YAAY,aAAa,IAAI,YAAY,CAAC,IAAI,eAAe,CAAC,IAAI,YAAY,WAAW,IAAI,EAAE;AACtJ,MAAG,UAAU;AACb,MAAG,eAAe,IAAI,aAAa,OAAO,OAAO,IAAI,UAAU,aAAa,IAAI,WAAW,IAAI,EAAE;AAAA,IACnG;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,QAAQ,CAAC,iHAAiH;AAAA,EAC1H,iBAAiB;AACnB,CAAC;AAzJL,IAAM,+BAAN;AAAA,CA4JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,gBAAgB;AAAA,MAC1B,QAAQ,CAAC,qFAAqF;AAAA,IAChG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;AAAA,IAClC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,OAAO;AAC7B,QAAM,kBAAkB,MAAM,OAAO,UAAQ,CAAC,CAAC,IAAI;AACnD,MAAI,CAAC,gBAAgB,QAAQ;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,KAAK,UAAQ,OAAO,SAAS,QAAQ,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI;AACzD;AACA,SAAS,YAAY,OAAO;AAC1B;AACF;AACA,IAAM,gCAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,QAAQ,UAAU,CAAC,KAAK,KAAK,QAAQ;AAC7C;AAAA,IACF;AACA,SAAK,sBAAsB;AAC3B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,wBAAwB;AACtB,SAAK,mBAAmB,KAAK,QAAQ,IAAI,SAAQ,iCAC5C,MAD4C;AAAA,MAE/C,cAAc,IAAI;AAAA,IACpB,EAAE;AAAA,EACJ;AAAA,EACA,eAAe;AACb,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ,OAAO,SAAO,CAAC,IAAI,mBAAmB,IAAI,IAAI,EAAE,QAAQ,SAAO;AAC1E,YAAM,wBAAwB,KAAK,KAAK,IAAI,SAAO,IAAI,IAAI,IAAI,CAAC;AAChE,YAAM,UAAU,KAAK,mBAAmB,GAAG;AAC3C,WAAK,WAAW,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,UAAU,QAAQ,qBAAqB,CAAC,IAAI,QAAQ,qBAAqB;AAAA,IAC3H,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,QAAQ;AACzB,QAAI,OAAO,gBAAgB,QAAW;AACpC,aAAO;AAAA,IACT,WAAW,OAAO,gBAAgB,MAAM;AACtC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAiCF;AA/BI,8BAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,+BAA8B;AACjE;AAGA,8BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,EACrC,WAAW,CAAC,GAAG,uBAAuB;AAAA,EACtC,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,YAAY,MAAM,GAAG,cAAc,WAAW,aAAa,OAAO,UAAU,CAAC;AAAA,EACvF,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,QAAI,KAAK,GAAG;AACV,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,sBAAsB,CAAC;AAAA,IAC9G;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,cAAc,IAAI,mBAAmB,IAAI,EAAE;AAAA,IAClE;AAAA,EACF;AAAA,EACA,cAAc,CAAC,yBAAyB;AAAA,EACxC,eAAe;AACjB,CAAC;AAhEL,IAAM,+BAAN;AAAA,CAmEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,WAAW;AAAA,EAClB;AAqCF;AAnCI,+BAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,SAAO,KAAK,qBAAqB,gCAA+B;AAClE;AAGA,+BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACrD,WAAW;AAAA,IACX,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,eAAe;AAAA,IAC1E,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,EACxD;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,sBAAsB,GAAG,CAAC,GAAG,iBAAiB,GAAG,UAAU,oBAAoB,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,uBAAuB,OAAO,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,QAAQ,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,EACtQ,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,iBAAiB,GAAG,8CAA8C,GAAG,GAAG,OAAO,GAAM,yBAAyB;AACjH,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,UAAU,IAAI,iBAAiB,IAAI;AAClD,MAAG,UAAU;AACb,MAAG,WAAc,gBAAgB,GAAG,GAAG,EAAE,YAAY,IAAI,QAAQ,CAAC;AAAA,IACpE;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,QAAQ,CAAC,okBAAokB;AAAA,EAC7kB,iBAAiB;AACnB,CAAC;AAtCL,IAAM,gCAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,oZAAoZ;AAAA,IAC/Z,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,2BAA0B;AAAA,EAC9B,OAAO,uBAAuB,WAAW,SAAS;AAChD,WAAO;AAAA,EACT;AAYF;AAVI,2BAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,4BAA2B;AAC9D;AAGA,2BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAChD,CAAC;AAbL,IAAM,4BAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,WAAW,UAAU,KAAK,WAAW;AAC5C,QAAM,gBAAgB,UAAU,KAAK,QAAQ;AAC7C,MAAI,gBAAgB,IAAI;AACtB,aAAS,OAAO,eAAe,CAAC;AAAA,EAClC,OAAO;AACL,aAAS,KAAK,GAAG;AAAA,EACnB;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,UAAU,MAAM,OAAO,WAAW;AAC3D,QAAM,UAAU,QAAQ;AACxB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,UAAU,KAAK,aAAa,KAAK;AACvC,UAAM,SAAS,KAAK,aAAa,KAAK;AACtC,QAAI,QAAQ;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AACA,QAAI,SAAS;AACX,cAAQ;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,QACN,OAAO;AAAA,QACP,KAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,QAAI,WAAW,UAAU,CAAC,WAAW,SAAS;AAG5C,UAAI,KAAK,MAAM,SAAS,KAAK,MAAM,OAAO,KAAK;AAC7C,iBAAS,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,0BAAN,MAAM,wBAAuB;AAAA,EAC3B,IAAI,SAAS,KAAK;AAChB,QAAI,QAAQ,KAAK,WAAW;AAC1B,WAAK,YAAY;AACjB,WAAK,aAAa;AAElB,WAAK,eAAe;AACpB,WAAK,WAAW,IAAI;AACpB,WAAK,WAAW,MAAM;AAAA,IACxB;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,QAAI,QAAQ,KAAK,OAAO;AACtB,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,QAAI,QAAQ,KAAK,UAAU;AACzB,WAAK,WAAW;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,KAAK;AACd,QAAI,QAAQ,KAAK,SAAS;AACxB,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,cAAc,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAC/D,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,QAAQ,KAAK,WAAW;AAC1B,WAAK,YAAY;AACjB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,aAAa;AAAA,IAC3B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,QAAI,KAAK,YAAY;AACnB,WAAK,cAAc,MAAM;AAAA,IAC3B,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,gBAAgB;AAClB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,WAAW,CAAC;AACjB,SAAK,wBAAwB;AAC7B,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,iBAAiB,IAAI,aAAa,KAAK;AAC5C,SAAK,aAAa,IAAI,aAAa;AAMnC,SAAK,eAAe,SAAS,MAAM;AACjC,UAAI,KAAK,gBAAgB,KAAK,KAAK,cAAc,KAAK,kBAAkB,KAAK,UAAU;AACrF,eAAO,KAAK,gBAAgB,EAAE,MAAM,KAAK,WAAW,CAAC;AAAA,MACvD;AAEA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,eAAe,SAAS,MAAM;AACjC,aAAO,KAAK,WAAW;AAAA,IACzB,CAAC;AACD,SAAK,kBAAkB,OAAO,IAAI,eAAe,CAAC;AAClD,SAAK,UAAU;AACf,SAAK,UAAU,OAAO;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AACD,SAAK,gBAAgB,CAAC;AACtB,SAAK,eAAe;AAIpB,SAAK,qBAAqB,CAAC,KAAK,UAAU;AACxC,UAAI,CAAC,KAAK,WAAW;AACnB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,KAAK,UAAU;AACjC,aAAO,OAAO,cAAc,aAAa,UAAU,KAAK,KAAK,IAAI;AAAA,IACnE;AACA,SAAK,0BAA0B,CAAC,KAAK,UAAU;AAC7C,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,KAAK,aAAa,cAAc,IAAI,KAAK,YAAY,KAAK,aAAa;AACzF,aAAO,OAAO,cAAc,aAAa,UAAU,KAAK,KAAK,IAAI;AAAA,IACnE;AAQA,SAAK,eAAe,SAAS,MAAM;AACjC,UAAI,KAAK,cAAc,KAAK,gBAAgB;AAC1C,eAAO,cAAc,KAAK,gBAAgB,EAAE,MAAM,KAAK,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAAA,MAC7E,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,SAAK,gBAAgB,CAAC,OAAO,QAAQ;AACnC,UAAI,KAAK,uBAAuB;AAC9B,eAAO;AAAA,MACT;AACA,UAAI,KAAK,eAAe,KAAK;AAC3B,eAAO,IAAI,KAAK,WAAW;AAAA,MAC7B,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,QAAI,KAAK,WAAW;AAClB,WAAK,WAAW,KAAK,UAAU,OAAO,UAAU,CAAC;AAAA,QAC/C;AAAA,QACA;AAAA,MACF,MAAM,KAAK,kBAAkB,MAAM,KAAK,CAAC;AAAA,IAC3C;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,KAAK,YAAY,OAAO,UAAU,CAAC;AAAA,QACjD;AAAA,QACA;AAAA,MACF,MAAM;AAEJ,aAAK,wBAAwB;AAC7B,aAAK,kBAAkB,MAAM,KAAK;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB,MAAM,OAAO;AAC7B,QAAI,SAAS,WAAW,SAAS,OAAO;AACtC,WAAK,mBAAmB,KAAK;AAAA,IAC/B;AACA,QAAI,SAAS,OAAO;AAClB,WAAK,cAAc,KAAK;AAAA,IAC1B;AAGA,SAAK,cAAc;AACnB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,KAAK,aAAa,KAAK,aAAa;AACtC,WAAK,SAAS,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ;AAEpB,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,QAAI,KAAK,cAAc,KAAK,kBAAkB,QAAQ;AAEpD,YAAM,WAAW,KAAK,WAAW;AACjC,eAAS,KAAK,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAAA,IACpD,WAAW,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAClD,eAAS;AAAA,IACX;AACA,SAAK,SAAS,UAAU,UAAU,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,UAAM,aAAa,MAAM;AACzB,UAAM,aAAa,MAAM;AAGzB,QAAI,KAAK,YAAY,cAAc,KAAK,YAAY,YAAY;AAC9D,WAAK,OAAO,KAAK;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,WAAW,MAAM,SAAS;AAC/B,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,WAAW;AACpB,QAAI,SAAS,KAAK,QAAQ,EAAE,QAAQ,KAAK;AACzC,UAAM,kBAAkB,CAAC,OAAO,UAAU,MAAM;AAChD,QAAI,cAAc,MAAM;AACtB,eAAS,KAAK,KAAK,MAAM;AAAA,IAC3B,WAAW,cAAc,QAAQ;AAC/B,eAAS,KAAK,MAAM,MAAM;AAAA,IAC5B;AACA,QAAI,cAAc,UAAa,CAAC,MAAM,MAAM,KAAK,WAAW,KAAK,cAAc;AAC7E,WAAK,eAAe;AAEpB,UAAI,mBAAmB,KAAK,cAAc,KAAK,kBAAkB,KAAK,gBAAgB;AACpF,cAAM,QAAQ,KAAK,KAAK,KAAK,QAAQ,EAAE,QAAQ,CAAC;AAChD,YAAI,CAAC,SAAS,cAAc,MAAM;AAChC,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC3B;AACA,cAAM,UAAU,KAAK,KAAK,KAAK,QAAQ,EAAE,QAAQ,KAAK,QAAQ;AAC9D,YAAI,CAAC,WAAW,cAAc,QAAQ;AACpC,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC3B;AAAA,MACF;AACA,WAAK,KAAK,KAAK,MAAM;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,QAAQ;AAIjB,UAAM,OAAO,KAAK,cAAc,KAAK,YAAY,MAAM,OAAO,KAAK,IAAI,MAAM,KAAK,YAAY,MAAM,CAAC,IAAI,KAAK,KAAK,MAAM,OAAO,KAAK,IAAI,MAAM,KAAK,QAAQ,CAAC;AAC7J,SAAK,SAAS,OAAO;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK;AAEhB,QAAI,OAAO,KAAK,cAAc,YAAY;AACxC,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO;AACpB,QAAI,YAAY;AAChB,QAAI,MAAM,OAAO;AAEf,eAAS,QAAQ,GAAG,QAAQ,MAAM,MAAM,QAAQ,SAAS;AACvD,qBAAa,KAAK,sBAAsB,MAAM,MAAM,KAAK,CAAC;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,KAAK;AACzB,QAAI,YAAY,KAAK,aAAa,GAAG;AACrC,UAAM,WAAW,KAAK,eAAe,GAAG;AAExC,QAAI,UAAU;AACZ,mBAAa,KAAK,mBAAmB,GAAG;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,gBAAgB;AAIvB,cAAM,SAAS,SAAS,KAAK,aAAa,EAAE;AAC5C,gBAAQ,KAAK,gBAAgB,EAAE,YAAY,KAAK,OAAO;AACvD,eAAO,KAAK,gBAAgB,EAAE,YAAY,SAAS,KAAK,OAAO,IAAI;AAAA,MACrE,OAAO;AAGL,gBAAQ;AACR,eAAO,KAAK;AAAA,MACd;AAAA,IACF,OAAO;AAGL,UAAI,CAAC,KAAK,gBAAgB;AACxB,gBAAQ,KAAK,IAAI,KAAK,SAAS,KAAK,UAAU,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,IAAI,QAAQ,KAAK,UAAU,KAAK,QAAQ;AAAA,IACtD;AACA,SAAK,QAAQ,IAAI;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,cAAc,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAC/D;AAAA,IACF;AAIA,SAAK,gBAAgB,EAAE,WAAW;AAElC,QAAI,KAAK,KAAK,QAAQ;AACpB,YAAM,gBAAgB,oBAAI,IAAI;AAC9B,UAAI,KAAK,WAAW;AAClB,mBAAW,OAAO,KAAK,MAAM;AAC3B,cAAI,OAAO,KAAK,eAAe,GAAG,GAAG;AACnC,0BAAc,IAAI,GAAG;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,WAAK,gBAAgB,EAAE,UAAU;AAAA,QAC/B,MAAM,KAAK;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB,KAAK,cAAc,KAAK;AAAA,QACzC,aAAa,KAAK,QAAQ,EAAE;AAAA,QAC5B,UAAU,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AACD,WAAK,gBAAgB,IAAI,OAAO,OAAO,KAAK,gBAAgB,CAAC,CAAC;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,KAAK;AACtB,UAAM,iBAAiB,KAAK,kBAAkB,KAAK,KAAK,aAAa;AACrE,UAAM,WAAW,iBAAiB;AAElC,QAAI,UAAU;AACZ,WAAK,cAAc,OAAO,gBAAgB,CAAC;AAAA,IAC7C,OAAO;AACL,WAAK,cAAc,KAAK,GAAG;AAAA,IAC7B;AAEA,QAAI,KAAK,cAAc,KAAK,gBAAgB;AAC1C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,UAAU;AAEtB,SAAK,gBAAgB,CAAC;AACtB,UAAM,OAAO,KAAK,eAAe,KAAK;AACtC,QAAI,UAAU;AACZ,iBAAW,OAAO,MAAM;AACtB,aAAK,cAAc,KAAK,GAAG;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AAEnB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,SAAK,sBAAsB;AAC3B,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,KAAK;AAClB,QAAI,KAAK,cAAc,WAAW,KAAK,KAAK,uBAAuB;AACjE,iBAAW,SAAS,KAAK,aAAa;AACpC,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,KAAK,kBAAkB,KAAK,KAAK,aAAa,IAAI;AAAA,EAC3D;AAAA,EACA,kBAAkB,KAAK,UAAU;AAC/B,QAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AACjC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,WAAO,SAAS,UAAU,OAAK;AAC7B,YAAMC,MAAK,KAAK,YAAY,CAAC;AAC7B,aAAOA,QAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,aAAa,KAAK;AAChB,SAAK,WAAW,KAAK;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO,SAAS;AACvB,UAAM,eAAe;AACrB,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,WAAW;AAAA,MACX,SAAS,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,KAAK,OAAO,SAAS,cAAc;AACjC,SAAK,cAAc;AACnB,SAAK,qBAAqB,aAAa;AACvC,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,KAAK,OAAO,SAAS,cAAc;AACjC,UAAM,eAAe;AACrB,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,eAAe,aAAa;AAAA,MAC5B,WAAW;AAAA,MACX,SAAS,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,OAAO,SAAS,cAAc;AACtC,UAAM,eAAe;AACrB,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,eAAe,aAAa;AAAA,MAC5B,WAAW;AAAA,MACX,SAAS,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,OAAO,SAAS,cAAc;AACtC,UAAM,eAAe;AACrB,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,eAAe,aAAa;AAAA,MAC5B,WAAW;AAAA,MACX,SAAS,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,OAAO,SAAS;AACtB,UAAM,eAAe;AACrB,SAAK,cAAc,KAAK;AAAA,MACtB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,0BAA0B;AACxB,UAAM,YAAY,aAAa,KAAK,QAAQ;AAC5C,SAAK,oBAAoB,kBAAkB,WAAW,KAAK,QAAQ;AAAA,EACrE;AAAA,EACA,UAAU,OAAO,OAAO,KAAK;AAC3B,QAAI,CAAC,KAAK,eAAe;AACvB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,kBAAkB,cAAc;AACpD,UAAM,QAAQ,KAAK,kBAAkB,cAAc;AACnD,UAAM,aAAa,KAAK,kBAAkB,cAAc;AACxD,QAAI,WAAW,CAAC;AAEhB,QAAI,SAAS,UAAU,YAAY;AACjC,UAAI,MAAM,UAAU;AAClB,mBAAW,kBAAkB,CAAC,GAAG,KAAK,MAAM,OAAO,KAAK,SAAS;AAAA,MACnE,WAAW,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM,UAAU;AAEhE,mBAAW,KAAK,KAAK,OAAO,aAAW,CAAC,CAAC,OAAO;AAAA,MAClD,WAAW,MAAM,WAAW,MAAM,WAAW,cAAc,QAAQ;AACjE,mBAAW,WAAW,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,MAClF,OAAO;AACL,mBAAW,WAAW,CAAC,GAAG,KAAK,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,MAClE;AAAA,IACF,OAAO;AACL,iBAAW,WAAW,CAAC,GAAG,KAAK,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,IAClE;AACA,QAAI,OAAO,KAAK,gBAAgB,YAAY;AAC1C,iBAAW,SAAS,OAAO,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,IACxD;AACA,QAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,iBAAW,SAAS,OAAO,aAAW,CAAC,KAAK,gBAAgB,OAAO,CAAC;AAAA,IACtE;AACA,SAAK,SAAS,OAAO,GAAG,KAAK,SAAS,MAAM;AAC5C,SAAK,SAAS,KAAK,GAAG,QAAQ;AAC9B,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,KAAK,kBAAkB,cAAc;AACpD,UAAM,SAAS,CAAC,WAAW,SAAS,WAAW,SAAS,eAAe,UAAU,SAAS;AAC1F,QAAI,QAAQ;AACV,WAAK,UAAU,OAAO,OAAO,GAAG;AAAA,IAClC,WAAW,SAAS,WAAW;AAC7B,UAAI,MAAM,QAAQ,KAAK,QAAQ;AAC7B,aAAK,UAAU,OAAO,OAAO,GAAG;AAAA,MAClC,WAAW,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM,UAAU;AAChE,aAAK,UAAU,OAAO,GAAG,GAAG;AAAA,MAC9B,OAAO;AACL,aAAK,gBAAgB,KAAK;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,MAAM;AACV,UAAM,cAAc,QAAQ,KAAK,MAAM,QAAQ,KAAK,QAAQ,QAAQ,KAAK,SAAS,QAAQ,KAAK;AAC/F,QAAI,aAAa;AACf,YAAM,kBAAkB,KAAK,kBAAkB,cAAc;AAC7D,UAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,cAAM,gBAAgB,KAAK,gBAAgB,MAAM,GAAG;AACpD,YAAI,eAAe;AACjB;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,MAAM,eAAe,CAAC,iBAAiB;AAC1C,aAAK,SAAS,MAAM,YAAY,GAAG;AAAA,MACrC,WAAW,mBAAmB,MAAM,cAAc,QAAW;AAC3D,aAAK,UAAU,MAAM,aAAa,MAAM,YAAY,KAAK,MAAM,SAAS;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,YAAY,KAAK;AACxB,UAAM,iBAAiB,KAAK,eAAe,YAAY,GAAG;AAC1D,QAAI,gBAAgB;AAClB,qBAAe,MAAM;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe,YAAY,KAAK;AAC9B,UAAM,gBAAgB,WAAW;AACjC,QAAI,eAAe;AACjB,UAAI,eAAe;AACnB,UAAI,QAAQ,KAAK,IAAI;AACnB,uBAAe,cAAc;AAAA,MAC/B,WAAW,QAAQ,KAAK,MAAM;AAC5B,uBAAe,cAAc;AAAA,MAC/B;AACA,UAAI,gBAAgB,aAAa,SAAS,QAAQ;AAChD,eAAO,aAAa,SAAS,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,aAAa,YAAY,KAAK,WAAW;AACjD,QAAI,kBAAkB;AACtB,QAAI,QAAQ,KAAK,MAAM;AACrB,wBAAkB,YAAY;AAAA,IAChC,WAAW,QAAQ,KAAK,OAAO;AAC7B,wBAAkB,YAAY;AAAA,IAChC,WAAW,QAAQ,KAAK,MAAM,QAAQ,KAAK,MAAM;AAC/C,YAAM,iBAAiB,KAAK,eAAe,YAAY,GAAG;AAC1D,UAAI,gBAAgB;AAClB,cAAM,WAAW,eAAe,uBAAuB,qBAAqB;AAC5E,YAAI,SAAS,QAAQ;AACnB,4BAAkB,SAAS,SAAS;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,WAAW,mBAAmB,OAAO,gBAAgB,UAAU,YAAY;AAChG,sBAAgB,MAAM;AAAA,IACxB;AAAA,EACF;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,KAAK,kBAAkB,KAAK,KAAK,QAAQ,IAAI;AAAA,EACtD;AAAA,EACA,kBAAkB,KAAK,UAAU;AAC/B,QAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AACjC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,WAAO,SAAS,UAAU,OAAK;AAC7B,YAAMA,MAAK,KAAK,YAAY,CAAC;AAC7B,aAAOA,QAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,CAAC,KAAK;AAAA,EACf;AAkGF;AAhGI,wBAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,yBAAwB;AAC3D;AAGA,wBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,EAC9B,WAAW,SAAS,6BAA6B,IAAI,KAAK;AACxD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,mBAAmB,CAAC;AAAA,IACrC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,IACjE;AAAA,EACF;AAAA,EACA,WAAW,CAAC,GAAG,gBAAgB;AAAA,EAC/B,UAAU;AAAA,EACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,SAAS,IAAI,SAAS,EAAE,UAAU,IAAI,UAAU;AAAA,IACjE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,IACf,UAAU;AAAA,IACV,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,uBAAuB;AAAA,EACzB;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,YAAY;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,iBAAiB,GAAG,WAAW,YAAY,aAAa,iBAAiB,GAAG,CAAC,GAAG,cAAc,cAAc,gBAAgB,OAAO,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,UAAU,cAAc,cAAc,gBAAgB,aAAa,GAAG,CAAC,GAAG,aAAa,cAAc,QAAQ,SAAS,GAAG,CAAC,0BAA0B,EAAE,GAAG,CAAC,QAAQ,OAAO,GAAG,aAAa,cAAc,QAAQ,SAAS,GAAG,CAAC,QAAQ,OAAO,YAAY,MAAM,GAAG,cAAc,YAAY,QAAQ,YAAY,aAAa,aAAa,aAAa,WAAW,YAAY,cAAc,cAAc,WAAW,aAAa,OAAO,SAAS,YAAY,YAAY,YAAY,gBAAgB,cAAc,aAAa,uBAAuB,GAAG,CAAC,YAAY,IAAI,GAAG,WAAW,YAAY,WAAW,GAAG,CAAC,GAAG,eAAe,cAAc,SAAS,aAAa,eAAe,WAAW,mBAAmB,wBAAwB,OAAO,YAAY,YAAY,YAAY,UAAU,GAAG,CAAC,GAAG,kBAAkB,eAAe,cAAc,aAAa,eAAe,WAAW,mBAAmB,wBAAwB,OAAO,YAAY,YAAY,YAAY,UAAU,GAAG,CAAC,GAAG,kBAAkB,wBAAwB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,UAAU,cAAc,cAAc,cAAc,CAAC;AAAA,EACr2C,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,GAAG;AACtB,MAAG,oBAAoB,GAAG,+CAA+C,GAAG,GAAG,OAAO,CAAC;AACvF,MAAG,oBAAoB,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC;AAChG,MAAG,oBAAoB,GAAG,+CAA+C,GAAG,CAAC;AAC7E,MAAG,oBAAoB,GAAG,+CAA+C,GAAG,GAAG,sBAAsB,CAAC;AAAA,IACxG;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,mBAAmB,IAAI,EAAE;AAC9C,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,0BAA0B,CAAC,IAAI,YAAY,CAAC,IAAI,kBAAkB,CAAC,IAAI,cAAc,IAAI,EAAE;AAChH,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,KAAK,SAAS,IAAI,EAAE;AACzC,MAAG,UAAU;AACb,MAAG,cAAc,EAAE,IAAI,QAAQ,OAAO,OAAO,IAAI,KAAK,WAAW,CAAC,IAAI,oBAAoB,CAAC,IAAI,wBAAwB,IAAI,EAAE;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,cAAc,CAAC,+BAA+B,mBAAmB,8BAA8B,8BAA8B,kCAAkC,2BAA2B,oBAAoB,kBAAkB,yBAAyB;AAAA,EACzP,QAAQ,CAAC,2gBAA2gB;AAAA,EACphB,iBAAiB;AACnB,CAAC;AA1uBL,IAAM,yBAAN;AAAA,CA6uBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4KV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,+BAA+B,mBAAmB,8BAA8B,8BAA8B,kCAAkC,2BAA2B,oBAAoB,kBAAkB,yBAAyB;AAAA,MACpP,QAAQ,CAAC,sXAAsX;AAAA,IACjY,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,QAAQ,KAAK,SAAS;AAAA,EAC7B;AAAA,EACA,WAAW;AACT,UAAM,QAAQ,KAAK,SAAS,cAAc,KAAK;AAC/C,UAAM,MAAM,aAAa;AACzB,UAAM,MAAM,QAAQ;AACpB,SAAK,SAAS,KAAK,YAAY,KAAK;AACpC,UAAM,gBAAgB,MAAM;AAC5B,UAAM,MAAM,WAAW;AACvB,UAAM,QAAQ,KAAK,SAAS,cAAc,KAAK;AAC/C,UAAM,MAAM,QAAQ;AACpB,UAAM,YAAY,KAAK;AACvB,UAAM,kBAAkB,MAAM;AAC9B,SAAK,SAAS,KAAK,YAAY,KAAK;AACpC,WAAO,gBAAgB;AAAA,EACzB;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAiB;AACpD;AAGA,iBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,iBAAgB;AAAA,EACzB,YAAY;AACd,CAAC;AA7BL,IAAM,kBAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,WAAW,OAAO,KAAK;AAC5B,SAAK,iBAAiB,OAAO,KAAK;AAAA,EACpC;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,UAAU,iBAAiB;AAEjC,QAAI,CAAC,KAAK,gBAAgB,WAAW,MAAM,WAAW,GAAG;AACvD;AAAA,IACF;AAEA,UAAM,SAAS,MAAM;AACrB,QAAI,OAAO,UAAU,SAAS,eAAe,GAAG;AAC9C;AAAA,IACF;AACA,SAAK,SAAS,IAAI,IAAI;AACtB,SAAK,eAAe,IAAI,KAAK;AAC7B,UAAM,UAAU,UAAU,UAAU,UAAU,YAAY,UAAU;AACpE,SAAK,eAAe,QAAQ,UAAU,MAAM,KAAK,SAAS,CAAC;AAC3D,SAAK,UAAU,WAAW,MAAM;AAC9B,WAAK,eAAe,IAAI,IAAI;AAC5B,WAAK,eAAe,KAAK;AAAA,QACvB;AAAA,QACA,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,GAAG,KAAK,QAAQ;AAAA,EAClB;AAAA,EACA,WAAW;AACT,iBAAa,KAAK,OAAO;AACzB,SAAK,eAAe,IAAI,KAAK;AAC7B,SAAK,SAAS,IAAI,KAAK;AACvB,SAAK,qBAAqB;AAC1B,SAAK,aAAa,KAAK;AAAA,MACrB,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,OAAO;AACzB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAkCF;AAhCI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,EAClC,UAAU;AAAA,EACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,cAAc,SAAS,iDAAiD,QAAQ;AAC5F,eAAO,IAAI,YAAY,MAAM;AAAA,MAC/B,CAAC,EAAE,aAAa,SAAS,gDAAgD,QAAQ;AAC/E,eAAO,IAAI,YAAY,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,SAAS,IAAI,SAAS,CAAC,EAAE,aAAa,IAAI,eAAe,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,IAClE,YAAY;AAAA,IACZ,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,EACvD;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,cAAc;AAAA,EAChB;AACF,CAAC;AAlFL,IAAM,qBAAN;AAAA,CAqFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,YAAY,UAAU,SAAS;AACtC,MAAI,aAAa,SAAS,QAAQ;AAChC,QAAI,YAAY,cAAc,KAAK;AACjC,aAAO,cAAc;AAAA,IACvB,OAAO;AACL,aAAO,cAAc;AAAA,IACvB;AAAA,EACF,OAAO;AACL,QAAI,CAAC,SAAS;AACZ,aAAO,cAAc;AAAA,IACvB,WAAW,YAAY,cAAc,KAAK;AACxC,aAAO,cAAc;AAAA,IACvB,WAAW,YAAY,cAAc,MAAM;AACzC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;AAKA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,MAAM,QAAQ,OAAO,MAAM,aAAa;AAC1C,QAAI;AAAA,EACN;AACA,MAAI,MAAM,QAAQ,OAAO,MAAM,aAAa;AAC1C,QAAI;AAAA,EACN;AACA,MAAI,aAAa,QAAQ,aAAa,MAAM;AAC1C,QAAI,IAAI,GAAG;AACT,aAAO;AAAA,IACT;AACA,QAAI,IAAI,GAAG;AACT,aAAO;AAAA,IACT;AAAA,EACF,WAAW,MAAM,WAAW,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,MAAM,WAAW,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;AAEvF,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AAEZ,QAAI,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG;AACrC,aAAO;AAAA,IACT;AACA,QAAI,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,QAAI,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAKA,SAAS,SAAS,MAAM,SAAS,MAAM;AACrC,MAAI,CAAC,MAAM;AACT,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,SAAS;AACrC,WAAO,CAAC,GAAG,IAAI;AAAA,EACjB;AACA,QAAM,OAAO,CAAC,GAAG,IAAI;AACrB,QAAM,OAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ;AACxC,QAAI,IAAI,UAAU;AAChB,UAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IACtB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAGL,QAAM,aAAa,KAAK,IAAI,SAAO;AACjC,UAAM,OAAO,IAAI;AACjB,WAAO;AAAA,MACL;AAAA,MACA,KAAK,IAAI;AAAA,MACT,aAAa,cAAc,IAAI;AAAA,MAC/B,WAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EACF,CAAC;AACD,SAAO,KAAK,KAAK,CAAC,MAAM,SAAS;AAC/B,eAAW,aAAa,YAAY;AAElC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,YAAM,QAAQ,YAAY,MAAM,IAAI;AAQpC,YAAM,aAAa,UAAU,QAAQ,cAAc,OAAO,UAAU,UAAU,OAAO,OAAO,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,UAAU,OAAO,OAAO,MAAM,MAAM,UAAU,GAAG;AAErL,UAAI,eAAe,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,gBAAgB,aAAa,SAAS,MAAM,mBAAmB;AACtE,MAAI,mBAAmB;AACrB,kBAAc,SAAS,aAAa,SAAS,CAAC;AAAA,MAC5C,KAAK,kBAAkB;AAAA,MACvB,MAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AACA,SAAO,YAAY,IAAI,WAAU,iCAC5B,QAD4B;AAAA,IAE/B,OAAO,SAAS,MAAM,OAAO,SAAS,IAAI;AAAA,EAC5C,EAAE;AACJ;AACA,IAAM,gCAAN,MAAM,8BAA6B;AAAA,EACjC,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB;AACxB,SAAK,YAAY,kBAAkB;AAAA,EACrC;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AACf,SAAK,YAAY,SAAS;AAC1B,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AACd,SAAK,UAAU,KAAK,YAAY,GAAG;AACnC,SAAK,YAAY,UAAU,KAAK;AAChC,SAAK,YAAY,KAAK,cAAc,KAAK,OAAO;AAChD,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB;AACrB,QAAI,MAAM;AACV,QAAI,KAAK,OAAO,UAAU;AACxB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,OAAO,YAAY;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,KAAK,OAAO,aAAa;AAC3B,UAAI,OAAO,KAAK,OAAO,gBAAgB,UAAU;AAC/C,eAAO,MAAM,KAAK,OAAO;AAAA,MAC3B,WAAW,OAAO,KAAK,OAAO,gBAAgB,YAAY;AACxD,cAAM,MAAM,KAAK,OAAO,YAAY;AAAA,UAClC,QAAQ,KAAK;AAAA,QACf,CAAC;AACD,YAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAO,MAAM;AAAA,QACf,WAAW,OAAO,QAAQ,UAAU;AAClC,gBAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,qBAAW,KAAK,MAAM;AACpB,gBAAI,IAAI,CAAC,MAAM,MAAM;AACnB,qBAAO,IAAI,CAAC;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,aAAO,qBAAqB,OAAO;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO;AAET,WAAO,KAAK,OAAO,mBAAmB,SAAY,KAAK,OAAO,OAAO;AAAA,EACvE;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO,WAAW,IAAI;AAAA,EACpC;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,0BAA0B;AAC/B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,oBAAoB,IAAI,aAAa,KAAK;AAC/C,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,UAAU,OAAO,UAAU,EAAE;AAElC,SAAK,yBAAyB;AAC9B,SAAK,cAAc;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,QAAQ,MAAM,KAAK,OAAO;AAAA,MAC1B,iBAAiB,KAAK;AAAA,MACtB,UAAU,MAAM,KAAK,OAAO,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,cAAc,QAAQ;AACpB,SAAK,kBAAkB,KAAK;AAAA,MAC1B,OAAO;AAAA,MACP,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,QAAI,KAAK,OAAO,WAAW;AACzB,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,YAAY,KAAK,cAAc,KAAK,OAAO;AAEhD,QAAI,KAAK,SAAS;AAChB,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,SAAS,KAAK,QAAQ;AACxB,YAAM,OAAO,MAAM,KAAK,OAAK;AAC3B,eAAO,EAAE,SAAS,KAAK,OAAO;AAAA,MAChC,CAAC;AACD,UAAI,MAAM;AACR,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,OAAO,UAAU;AACzB;AAAA,IACF;AACA,SAAK;AACL,QAAI,WAAW,YAAY,KAAK,UAAU,KAAK,OAAO;AAEtD,QAAI,KAAK,2BAA2B,KAAK,2BAA2B,GAAG;AACrE,iBAAW;AACX,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,KAAK,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,CAAC,KAAK,YAAY,OAAO,UAAU;AACrC,aAAO;AAAA,IACT;AACA,QAAI,YAAY,cAAc,KAAK;AACjC,aAAO,qBAAqB,KAAK,qBAAqB,mBAAmB;AAAA,IAC3E,WAAW,YAAY,cAAc,MAAM;AACzC,aAAO,sBAAsB,KAAK,sBAAsB,qBAAqB;AAAA,IAC/E,OAAO;AACL,aAAO,YAAY,KAAK,iBAAiB,2BAA2B;AAAA,IACtE;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,UAAU,iBAAiB;AACjC,UAAM,eAAe,KAAK,QAAQ;AAClC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,qBAAqB,KAAK;AAC9B,UAAM,gBAAgB;AACtB,UAAM,UAAU,UAAU,UAAU,UAAU,YAAY,UAAU;AACpE,SAAK,eAAe,QAAQ,UAAU,MAAM,KAAK,UAAU,CAAC;AAC5D,UAAM,eAAe,UAAU,UAAU,UAAU,cAAc,WAAW,EAAE,KAAK,UAAY,OAAO,CAAC,EAAE,UAAU,OAAK,KAAK,KAAK,GAAG,cAAc,OAAO,CAAC;AAC3J,SAAK,aAAa,IAAI,YAAY;AAAA,EACpC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa,QAAQ;AAClD,WAAK,oBAAoB;AACzB,WAAK,OAAO,KAAK;AAAA,QACf,OAAO,KAAK,QAAQ;AAAA,QACpB,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,KAAK,OAAO,cAAc,kBAAkB;AAC1C,UAAM,YAAY,qBAAqB,KAAK,EAAE,UAAU;AACxD,UAAM,WAAW,eAAe;AAChC,SAAK,SAAS,KAAK;AAAA,MACjB,OAAO;AAAA,MACP,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAmFF;AAjFI,8BAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,+BAA8B;AACjE;AAGA,8BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,EACrC,WAAW,CAAC,GAAG,uBAAuB;AAAA,EACtC,UAAU;AAAA,EACV,cAAc,SAAS,0CAA0C,IAAI,KAAK;AACxE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,eAAe,SAAS,4DAA4D,QAAQ;AACxG,eAAO,IAAI,cAAc,MAAM;AAAA,MACjC,CAAC,EAAE,iBAAiB,SAAS,gEAAgE;AAC3F,eAAO,IAAI,MAAM;AAAA,MACnB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,YAAY,IAAI,QAAQ;AACzC,MAAG,YAAY,cAAc,IAAI,OAAO,UAAU,EAAE,SAAS,IAAI,IAAI;AACrE,MAAG,WAAW,IAAI,gBAAgB;AAClC,MAAG,YAAY,UAAU,IAAI,cAAc,IAAI,EAAE,aAAa,IAAI,UAAU,IAAI,EAAE,aAAa,IAAI,UAAU,IAAI,EAAE,SAAS,IAAI,OAAO,IAAI;AAAA,IAC7I;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,qCAAqC,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,QAAQ,YAAY,GAAG,UAAU,SAAS,GAAG,CAAC,GAAG,+BAA+B,aAAa,GAAG,OAAO,GAAG,CAAC,GAAG,iBAAiB,GAAG,aAAa,YAAY,CAAC;AAAA,EAC3W,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,MAAM,CAAC;AAC5F,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,SAAS,CAAC;AAC/F,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,QAAQ,CAAC;AACrK,MAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,MAAG,WAAW,SAAS,SAAS,8DAA8D;AAC5F,eAAO,IAAI,OAAO;AAAA,MACpB,CAAC;AACD,MAAG,aAAa,EAAE;AAClB,MAAG,oBAAoB,GAAG,qDAAqD,GAAG,GAAG,QAAQ,CAAC;AAAA,IAChG;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,OAAO,iBAAiB,IAAI,CAAC;AAClD,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,IAAI,SAAS;AAC3B,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,OAAO,aAAa,IAAI,EAAE;AAAA,IACjD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,QAAQ,CAAC,y5CAAy5C;AAAA,EACl6C,iBAAiB;AACnB,CAAC;AAjRL,IAAM,+BAAN;AAAA,CAoRC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmCV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,qBAAqB;AAAA,MACvB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,QAAQ,CAAC,0mCAA0mC;AAAA,IACrnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;AAAA,IAClC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,SAAS,OAAO,eAAe,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EACxD;AAAA,EACA,qBAAqB;AAEnB,SAAK,oBAAoB;AACzB,SAAK,WAAW,QAAQ,UAAU,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,EACvE;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ,OAAK;AAC3B,QAAE,UAAU,YAAY;AACxB,QAAE,SAAS,YAAY;AACvB,QAAE,QAAQ,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,UAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,eAAe,CAAC;AACpD,QAAI,OAAO;AACT,YAAM,YAAY,YAAU;AAC1B,oBAAY,MAAM;AAClB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,cAAc;AAChB,uBAAa,UAAU,UAAU,KAAK,YAAY,KAAK,IAAI,CAAC;AAC5D,uBAAa,SAAS,UAAU,KAAK,WAAW,KAAK,IAAI,CAAC;AAC1D,uBAAa,QAAQ,UAAU,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,QAC1D;AAAA,MACF;AACA,YAAM,cAAc,CAAC;AAAA,QACnB;AAAA,MACF,MAAM;AACJ,YAAI,eAAe;AACjB,wBAAc,UAAU,YAAY;AACpC,wBAAc,SAAS,YAAY;AACnC,wBAAc,QAAQ,YAAY;AAAA,QACpC;AAAA,MACF;AACA,YAAM,iBAAiB,SAAS;AAEhC,YAAM,mBAAmB,WAAW;AAAA,IACtC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,CAAC;AAClB,QAAI,IAAI;AACR,eAAW,WAAW,KAAK,WAAW,QAAQ,GAAG;AAC/C,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,SAAS,IAAI,WAAW,SAAS,GAAG,CAAC;AAClD,WAAK,UAAU,QAAQ,UAAU,IAAI,IAAI;AAAA,QACvC;AAAA,QACA,OAAO,OAAO,SAAS,IAAI,YAAY,SAAS,GAAG,CAAC;AAAA,QACpD,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,KAAK,UAAU,MAAM,IAAI;AACzC,UAAM,SAAS,KAAK,SAAS,OAAO,KAAK;AACzC,QAAI,QAAQ;AACV,UAAI,KAAK,sBAAsB,OAAO,GAAG;AACvC,aAAK,cAAc,KAAK;AAAA,UACtB,WAAW,KAAK;AAAA,UAChB,UAAU,OAAO;AAAA,UACjB,cAAc,QAAQ;AAAA,QACxB,CAAC;AACD,aAAK,oBAAoB,OAAO;AAAA,MAClC;AAAA,IACF,WAAW,KAAK,sBAAsB,QAAQ,OAAO;AACnD,WAAK,cAAc,KAAK;AAAA,QACtB,WAAW,KAAK;AAAA,QAChB,cAAc,QAAQ;AAAA,MACxB,CAAC;AACD,WAAK,oBAAoB,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,KAAK,UAAU,MAAM,IAAI;AACzC,UAAM,SAAS,KAAK,SAAS,OAAO,KAAK;AACzC,QAAI,QAAQ;AACV,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW,QAAQ;AAAA,QACnB,UAAU,OAAO;AAAA,QACjB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,SAAK,oBAAoB;AACzB,YAAQ,MAAM,OAAO;AAAA,EACvB;AAAA,EACA,SAAS,OAAO,OAAO;AACrB,QAAI,IAAI;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,qBAAqB,KAAK;AAC9B,UAAM,UAAU,KAAK,SAAS,kBAAkB,SAAS,OAAO;AAChE,eAAWA,OAAM,KAAK,WAAW;AAE/B,YAAM,MAAM,KAAK,UAAUA,GAAE;AAE7B,UAAI,MAAM,SAASA,OAAM,QAAQ,KAAK,QAAM,OAAO,IAAI,OAAO,GAAG;AAC/D,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,WAAW,QAAQ,EAAE,OAAO,CAAC,KAAK,SAAS;AACrD,UAAI,KAAK,UAAU,IAAI,IAAI;AAC3B,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAyBF;AAvBI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,EACjC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,IACnD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa;AAAA,IAChE;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,eAAe;AAAA,EACjB;AACF,CAAC;AAtJL,IAAM,qBAAN;AAAA,CAyJC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,0BAA0B;AAC/B,SAAK,wBAAwB;AAC7B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,oBAAoB,IAAI,aAAa,KAAK;AAC/C,SAAK,qBAAqB;AAAA,MACxB,OAAO;AAAA,IACT;AACA,SAAK,gBAAgB;AAAA,MACnB,MAAM,CAAC;AAAA,MACP,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,IACV;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AACnB,eAAW,MAAM;AACf,UAAI,KAAK,UAAU;AACjB,cAAM,WAAW,aAAa,KAAK,QAAQ;AAC3C,aAAK,qBAAqB,kBAAkB,UAAU,KAAK,QAAQ;AACnE,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,QAAI,QAAQ,QAAQ;AAClB,WAAK,gBAAgB,GAAG,GAAG;AAAA,IAC7B,OAAO;AACL,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAChB,UAAM,YAAY,aAAa,GAAG;AAClC,SAAK,gBAAgB,gBAAgB,GAAG;AACxC,eAAW,MAAM;AACf,WAAK,qBAAqB,kBAAkB,WAAW,GAAG;AAC1D,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,uBAAuB;AACjC,WAAK,cAAc,QAAQ,KAAK,kBAAkB,OAAO;AACzD,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,GAAG,cAAc;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,WAAW;AACjB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,eAAe;AAAA,IACb;AAAA,EACF,GAAG;AACD,SAAK,kBAAkB;AAGvB,eAAW,MAAM;AAGf,YAAM,SAAS,KAAK,SAAS,KAAK,OAAK,EAAE,SAAS,MAAM,IAAI;AAC5D,UAAI,UAAU,cAAc,QAAQ;AAClC,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,GAAG,CAAC;AAAA,EACN;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,KAAK,YAAY;AACnB,YAAM,QAAQ,KAAK,wBAAwB,KAAK,aAAa,KAAK,gBAAgB,QAAQ,KAAK;AAC/F,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,OAAO,KAAK,KAAK,gBAAgB,OAAO,MAAM,CAAC;AAAA,EACtD;AAAA,EACA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,SAAS,KAAK,KAAK,gBAAgB,OAAO,MAAM,CAAC;AAAA,EACxD;AAAA,EACA,gBAAgB,OAAO,QAAQ;AAC7B,QAAI,OAAO,YAAY,SAAS,OAAO,UAAU;AAC/C,cAAQ,OAAO;AAAA,IACjB,WAAW,OAAO,YAAY,SAAS,OAAO,UAAU;AACtD,cAAQ,OAAO;AAAA,IACjB;AACA,WAAO;AAAA,MACL;AAAA,MACA,WAAW,OAAO;AAAA,MAClB,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,SAAS,KAAK,UAAU,MAAM,QAAQ;AAC5C,WAAO,WAAW;AAClB,WAAO,sBAAsB;AAC7B,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,aAAa,cAAc,GAAG;AAChC,YAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,gBAAU,WAAW;AACrB,gBAAU,sBAAsB;AAAA,IAClC;AACA,QAAI,YAAY,aAAa,GAAG;AAC9B,YAAM,YAAY,KAAK,UAAU,QAAQ;AACzC,gBAAU,WAAW;AACrB,UAAI,iBAAiB,UAAU;AAC7B,kBAAU,sBAAsB;AAAA,UAC9B,OAAO,gBAAgB,OAAO,eAAe,WAAW,kBAAkB,cAAc;AAAA,QAC1F;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,kBAAkB,KAAK,cAAc,CAAC,EAAE,QAAQ;AACtD,QAAI,QAAQ,iBAAiB;AAC3B,aAAO,KAAK,cAAc,CAAC,EAAE,QAAQ,KAAK;AAAA,IAC5C;AACA,UAAM,oBAAoB,KAAK,cAAc,CAAC,EAAE,QAAQ;AACxD,QAAI,QAAQ,kBAAkB,mBAAmB;AAC/C,aAAO,KAAK,cAAc,CAAC,EAAE,QAAQ,QAAQ,eAAe;AAAA,IAC9D;AACA,WAAO,KAAK,cAAc,CAAC,EAAE,QAAQ,QAAQ,kBAAkB,iBAAiB;AAAA,EAClF;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AAED,QAAI,OAAO,UAAU;AACnB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,aAAa,QAAQ,WAAW,QAAQ;AAC3D,SAAK,KAAK,KAAK;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,QAAQ,WAAW,UAAU;AACxC,QAAI,MAAM;AACV,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,QAAQ,CAAC;AAAA,IAChB;AACA,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,GAAG,MAAM;AACrC,UAAI,mBACC;AAEL,UAAI,EAAE,SAAS,OAAO,MAAM;AAC1B,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,aAAa,QAAW;AAC1B,YAAM,OAAO,KAAK,CAAC;AAAA,IACrB,WAAW,WAAW;AACpB,YAAM,GAAG,EAAE,MAAM;AAAA,IACnB,OAAO;AACL,UAAI,KAAK,aAAa,SAAS,QAAQ;AACrC,cAAM,OAAO,GAAG,KAAK,MAAM,MAAM;AAAA,MACnC;AACA,YAAM,KAAK;AAAA,QACT,KAAK;AAAA,QACL,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AACjB,SAAK,cAAc,OAAO,KAAK,kBAAkB,MAAM;AACvD,SAAK,cAAc,SAAS,KAAK,kBAAkB,QAAQ;AAC3D,SAAK,cAAc,QAAQ,KAAK,kBAAkB,OAAO;AACzD,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,GAAG,cAAc;AAAA,IACxB;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,SAAS,KAAK;AACpB,QAAI,UAAU,UAAU;AACtB,aAAO;AAAA,QACL,WAAW,cAAc,KAAK,UAAU,EAAE;AAAA,QAC1C,OAAO,GAAG,OAAO,KAAK,CAAC;AAAA,QACvB,YAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,GAAG,OAAO,KAAK,CAAC;AAAA,IACzB;AAAA,EACF;AAsEF;AApEI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,WAAW,CAAC,GAAG,kBAAkB;AAAA,EACjC,UAAU;AAAA,EACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,UAAU,IAAI,YAAY,EAAE,SAAS,IAAI,WAAW;AAAA,IACrE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,mBAAmB;AAAA,EACrB;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,QAAQ,OAAO,aAAa,IAAI,GAAG,0BAA0B,GAAG,WAAW,eAAe,GAAG,CAAC,GAAG,uBAAuB,GAAG,WAAW,SAAS,GAAG,CAAC,QAAQ,gBAAgB,cAAc,IAAI,aAAa,IAAI,GAAG,cAAc,gBAAgB,SAAS,SAAS,aAAa,mBAAmB,gBAAgB,YAAY,wBAAwB,uBAAuB,UAAU,YAAY,SAAS,iBAAiB,qBAAqB,sBAAsB,iBAAiB,mBAAmB,yBAAyB,GAAG,CAAC,QAAQ,gBAAgB,cAAc,IAAI,aAAa,IAAI,GAAG,UAAU,YAAY,kBAAkB,gBAAgB,QAAQ,UAAU,qBAAqB,cAAc,gBAAgB,SAAS,SAAS,aAAa,mBAAmB,gBAAgB,YAAY,wBAAwB,uBAAuB,UAAU,YAAY,SAAS,iBAAiB,qBAAqB,sBAAsB,iBAAiB,mBAAmB,yBAAyB,CAAC;AAAA,EACn+B,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,WAAW,SAAS,yDAAyD,QAAQ;AACjG,eAAO,IAAI,kBAAkB,MAAM;AAAA,MACrC,CAAC,EAAE,iBAAiB,SAAS,+DAA+D,QAAQ;AAClG,eAAO,IAAI,gBAAgB,MAAM;AAAA,MACnC,CAAC;AACD,MAAG,iBAAiB,GAAG,yCAAyC,GAAG,GAAG,MAAM,MAAM,UAAU;AAC5F,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,SAAS,IAAI,mBAAmB,OAAO,IAAI;AAC1D,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,aAAa;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,oBAAoB,SAAS,8BAA8B,oBAAoB,oBAAoB,OAAO;AAAA,EACzH,QAAQ,CAAC,6iBAA6iB;AAAA,EACtjB,iBAAiB;AACnB,CAAC;AA3SL,IAAM,2BAAN;AAAA,CA8SC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,oBAAoB,SAAS,8BAA8B,oBAAoB,oBAAoB,OAAO;AAAA,MACpH,QAAQ,CAAC,+TAA+T;AAAA,IAC1U,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,YAAU,WAAW,CAAC;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,WAAW;AACf,WAAS,QAAQ;AACf,eAAW,QAAQ,YAAY,QAAQ,IAAI,CAAC,oBAAI,KAAK;AACrD,cAAU;AACV,aAAS,KAAK,MAAM,SAAS,IAAI;AAAA,EACnC;AACA,SAAO,WAAY;AACjB,UAAM,MAAM,CAAC,oBAAI,KAAK;AACtB,QAAI,CAAC,YAAY,QAAQ,YAAY,OAAO;AAC1C,iBAAW;AAAA,IACb;AACA,UAAM,YAAY,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO;AACP,QAAI,aAAa,GAAG;AAClB,mBAAa,OAAO;AACpB,gBAAU;AACV,iBAAW;AACX,eAAS,KAAK,MAAM,SAAS,IAAI;AAAA,IACnC,WAAW,CAAC,WAAW,QAAQ,aAAa,OAAO;AACjD,gBAAU,WAAW,OAAO,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AASA,SAAS,aAAa,UAAU,SAAS;AACvC,SAAO,SAAS,eAAe,QAAQ,KAAK,YAAY;AACtD,WAAO;AAAA,MACL,cAAc;AAAA,MACd,YAAY,WAAW;AAAA,MACvB,KAAK,SAAS,SAAS;AACrB,eAAO,eAAe,MAAM,KAAK;AAAA,UAC/B,cAAc;AAAA,UACd,YAAY,WAAW;AAAA,UACvB,OAAO,SAAS,WAAW,OAAO,UAAU,OAAO;AAAA,QACrD,CAAC;AACD,eAAO,OAAO,GAAG;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,gBAAgB;AACpB,aAAW,KAAK,SAAS;AACvB,qBAAiB,EAAE,YAAY;AAAA,EACjC;AACA,SAAO;AACT;AAKA,SAAS,mBAAmB,YAAY,eAAe;AACrD,QAAM,eAAe,iBAAiB,UAAU;AAChD,QAAM,gBAAgB,iBAAiB,UAAU;AACjD,QAAM,cAAc,aAAa,UAAU;AAC3C,MAAI,iBAAiB,eAAe;AAClC,iBAAa,aAAa,eAAe,aAAa;AAAA,EACxD;AACF;AAIA,SAAS,aAAa,aAAa,UAAU,eAAe;AAE1D,aAAW,UAAU,OAAO,OAAO,WAAW,EAAE,KAAK,GAAG;AACtD,QAAI,OAAO,YAAY;AAErB,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,OAAO,eAAe;AACzB,kBAAY,OAAO;AACnB,uBAAiB,OAAO,WAAW,OAAO,WAAW;AAAA,IACvD,OAAO;AACL,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,QAAM,cAAc,CAAC;AACrB,MAAI,iBAAiB;AAErB,KAAG;AACD,UAAM,oBAAoB,iBAAiB;AAC3C,qBAAiB;AACjB,eAAW,UAAU,OAAO,OAAO,WAAW,EAAE,KAAK,GAAG;AAEtD,UAAI,OAAO,iBAAiB,CAAC,YAAY,OAAO,IAAI,GAAG;AACrD,cAAM,WAAW,OAAO,QAAQ,OAAO,WAAW;AAClD,YAAI,OAAO,aAAa,UAAa,WAAW,OAAO,UAAU;AAC/D,4BAAkB,WAAW,OAAO;AACpC,iBAAO,QAAQ,OAAO;AACtB,sBAAY,OAAO,IAAI,IAAI;AAAA,QAC7B,OAAO;AACL,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,mBAAmB;AAE5B,QAAM,UAAU,OAAO,OAAO,WAAW,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC;AACnF,QAAM,qBAAqB,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,IAAI,OAAO,CAAC;AAC1E,QAAM,QAAQ,WAAW;AACzB,MAAI,UAAU,GAAG;AACf;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ,OAAO,OAAK,EAAE,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,GAAG;AACxF,QAAI,QAAQ,MAAM,CAAC,IAAI,YAAY,IAAI,QAAQ,SAAS,IAAI,aAAa,QAAQ,MAAM,CAAC,IAAI,YAAY,IAAI,QAAQ,SAAS,IAAI,WAAW;AAC1I,UAAI,SAAS;AACb;AAAA,IACF;AAAA,EACF;AACF;AAoBA,SAAS,sBAAsB,YAAY,eAAe,UAAU,YAAY,kBAAkB,KAAK,sBAAsB,GAAG;AAC9H,QAAM,kBAAkB,WAAW,MAAM,WAAW,GAAG,WAAW,MAAM,EAAE,OAAO,OAAK,EAAE,kBAAkB,KAAK;AAC/G,aAAW,UAAU,iBAAiB;AACpC,QAAI,CAAC,OAAO,YAAY;AACtB,aAAO,aAAa,OAAO;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,yBAAyB;AAC7B,MAAI,gBAAgB;AACpB,MAAI,eAAe,gBAAgB,YAAY,eAAe;AAC9D,MAAI,iBAAiB,gBAAgB;AACrC,QAAM,wBAAwB;AAC9B,QAAM,mBAAmB,CAAC;AAC1B,QAAM,sBAAsB;AAE5B,KAAG;AACD,6BAAyB,iBAAiB,gBAAgB;AAC1D,oBAAgB,gBAAgB;AAChC,eAAW,UAAU,iBAAiB;AAEpC,UAAI,iBAAiB,cAAc,0BAA0B,KAAK,qBAAqB;AACrF,eAAO,QAAQ,OAAO,cAAc,OAAO,SAAS;AAAA,MACtD,OAAO;AACL,cAAM,WAAW,OAAO,SAAS,mBAAmB;AACpD,YAAI,OAAO,YAAY,UAAU,OAAO,UAAU;AAChD,iBAAO,QAAQ,OAAO;AACtB,2BAAiB,KAAK,MAAM;AAAA,QAC9B,WAAW,OAAO,YAAY,UAAU,OAAO,UAAU;AACvD,iBAAO,QAAQ,OAAO;AACtB,2BAAiB,KAAK,MAAM;AAAA,QAC9B,OAAO;AACL,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AACA,aAAO,QAAQ,KAAK,IAAI,GAAG,OAAO,KAAK;AAAA,IACzC;AACA,mBAAe,gBAAgB,YAAY,eAAe;AAC1D,qBAAiB,gBAAgB;AACjC,2BAAuB,iBAAiB,gBAAgB;AAAA,EAC1D,SAAS,iBAAiB,uBAAuB,gBAAgB,WAAW;AAE5E,aAAW,UAAU,iBAAiB;AACpC,WAAO,aAAa;AAAA,EACtB;AACF;AAIA,SAAS,uBAAuB,iBAAiB,kBAAkB;AACjE,aAAW,UAAU,kBAAkB;AACrC,UAAM,QAAQ,gBAAgB,QAAQ,MAAM;AAC5C,oBAAgB,OAAO,OAAO,CAAC;AAAA,EACjC;AACF;AAIA,SAAS,gBAAgB,YAAY,kBAAkB,KAAK;AAC1D,MAAI,eAAe;AACnB,aAAW,UAAU,YAAY;AAC/B,oBAAgB,OAAO,SAAS;AAAA,EAClC;AACA,SAAO;AACT;AACA,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,YAAY,OAAO,oBAAoB;AAAA,MAC1C,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,YAAY,CAAC;AAAA,EACtC;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ,KAAK,UAAU;AAAA,EAC9B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AACd,SAAK,QAAQ,KAAK,UAAU;AAAA,EAC9B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ,KAAK,UAAU;AAAA,EAC9B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,UAAM,QAAQ,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI;AAClE,WAAO,KAAK,IAAI,SAAS,GAAG,CAAC;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,UAAU;AACR,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA,EACA,WAAW;AACT,SAAK,WAAW,KAAK,OAAO,CAAC;AAAA,EAC/B;AAAA,EACA,WAAW;AACT,SAAK,WAAW,KAAK,OAAO,CAAC;AAAA,EAC/B;AAAA,EACA,WAAW,MAAM;AACf,QAAI,OAAO,KAAK,QAAQ,KAAK,cAAc,SAAS,KAAK,MAAM;AAC7D,WAAK,OAAO;AACZ,WAAK,OAAO,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU,MAAM;AACd,UAAM,QAAQ,CAAC;AACf,QAAI,YAAY;AAChB,QAAI,UAAU,KAAK;AACnB,UAAM,UAAU;AAChB,UAAM,aAAa,UAAU,KAAK;AAClC,WAAO,QAAQ,KAAK;AACpB,QAAI,YAAY;AACd,kBAAY,OAAO,KAAK,MAAM,UAAU,CAAC;AACzC,gBAAU,OAAO,KAAK,MAAM,UAAU,CAAC;AACvC,UAAI,YAAY,GAAG;AACjB,oBAAY;AACZ,kBAAU,KAAK,IAAI,YAAY,UAAU,GAAG,KAAK,UAAU;AAAA,MAC7D,WAAW,UAAU,KAAK,YAAY;AACpC,oBAAY,KAAK,IAAI,KAAK,aAAa,UAAU,GAAG,CAAC;AACrD,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF;AACA,aAAS,MAAM,WAAW,OAAO,SAAS,OAAO;AAC/C,YAAM,KAAK;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAuFF;AArFI,yBAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,0BAAyB;AAC5D;AAGA,yBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,EAC/B,WAAW,CAAC,GAAG,iBAAiB;AAAA,EAChC,QAAQ;AAAA,IACN,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC;AAAA,EAC9F,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,QAAI,KAAK,GAAG;AACV,MAAG,kBAAkB,GAAG,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;AACnD,MAAG,cAAc,SAAS,SAAS,sDAAsD;AACvF,eAAO,IAAI,WAAW,CAAC;AAAA,MACzB,CAAC;AACD,MAAG,aAAa,GAAG,GAAG;AACtB,MAAG,gBAAgB,EAAE;AACrB,MAAG,kBAAkB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;AACvC,MAAG,cAAc,SAAS,SAAS,sDAAsD;AACvF,eAAO,IAAI,SAAS;AAAA,MACtB,CAAC;AACD,MAAG,aAAa,GAAG,GAAG;AACtB,MAAG,gBAAgB,EAAE;AACrB,MAAG,iBAAiB,GAAG,wCAAwC,GAAG,GAAG,MAAM,GAAG,UAAU;AACxF,MAAG,kBAAkB,GAAG,IAAI,EAAE,IAAI,KAAK,CAAC;AACxC,MAAG,cAAc,SAAS,SAAS,uDAAuD;AACxF,eAAO,IAAI,SAAS;AAAA,MACtB,CAAC;AACD,MAAG,aAAa,IAAI,GAAG;AACvB,MAAG,gBAAgB,EAAE;AACrB,MAAG,kBAAkB,IAAI,IAAI,EAAE,IAAI,KAAK,CAAC;AACzC,MAAG,cAAc,SAAS,SAAS,uDAAuD;AACxF,eAAO,IAAI,WAAW,IAAI,UAAU;AAAA,MACtC,CAAC;AACD,MAAG,aAAa,IAAI,GAAG;AACvB,MAAG,gBAAgB,EAAE,EAAE;AAAA,IACzB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,YAAY,YAAY,CAAC,IAAI,YAAY,CAAC;AAC7C,MAAG,UAAU;AACb,MAAG,YAAY,cAAc,IAAI,SAAS,wBAAwB,kBAAkB;AACpF,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,qBAAqB,qBAAqB;AAC5D,MAAG,UAAU;AACb,MAAG,YAAY,YAAY,CAAC,IAAI,YAAY,CAAC;AAC7C,MAAG,UAAU;AACb,MAAG,YAAY,cAAc,IAAI,SAAS,2BAA2B,qBAAqB;AAC1F,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,sBAAsB,qBAAqB;AAC7D,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,KAAK;AACvB,MAAG,UAAU,CAAC;AACd,MAAG,YAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;AACzC,MAAG,UAAU;AACb,MAAG,YAAY,cAAc,IAAI,SAAS,uBAAuB,iBAAiB;AAClF,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,uBAAuB,sBAAsB;AAC/D,MAAG,UAAU;AACb,MAAG,YAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;AACzC,MAAG,UAAU;AACb,MAAG,YAAY,cAAc,IAAI,SAAS,uBAAuB,iBAAiB;AAClF,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,iBAAiB,qBAAqB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,otBAAotB;AAAA,EAC7tB,iBAAiB;AACnB,CAAC;AAxKL,IAAM,0BAAN;AAAA,CA2KC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,0TAA0T;AAAA,IACrU,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,OAAO,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,WAAW,KAAK,WAAW;AAAA,EACzC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS;AAAA,EACvB;AAiDF;AA/CI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,WAAW,CAAC,GAAG,kBAAkB;AAAA,EACjC,QAAQ;AAAA,IACN,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,sBAAsB,uBAAuB,qBAAqB,iBAAiB,QAAQ,QAAQ,OAAO,GAAG,CAAC,GAAG,UAAU,sBAAsB,uBAAuB,qBAAqB,iBAAiB,QAAQ,QAAQ,OAAO,CAAC;AAAA,EACvW,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,oBAAoB,GAAG,iDAAiD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,iDAAiD,GAAG,CAAC;AAClJ,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,UAAU,IAAI,cAAc,IAAI;AAC/C,MAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,IAAI,eAAe,CAAC;AACzE,MAAG,UAAU;AACb,MAAG,eAAe,IAAI,kBAAkB,OAAO,OAAO,IAAI,eAAe,YAAY,IAAI,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,cAAc,CAAC,SAAS,kBAAkB,uBAAuB;AAAA,EACjE,QAAQ,CAAC,6QAA6Q;AAAA,EACtR,iBAAiB;AACnB,CAAC;AAzDL,IAAM,2BAAN;AAAA,CA4DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwCV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,SAAS,kBAAkB,uBAAuB;AAAA,MAC5D,QAAQ,CAAC,sLAAsL;AAAA,IACjM,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAaH,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,UAAU,OAAO,UAAU;AAChC,SAAK,OAAO,OAAO,MAAM;AACzB,SAAK,YAAY;AACjB,SAAK,UAAU,IAAI,aAAa;AAAA,EAClC;AAAA,EACA,WAAW;AACT,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,OAAO;AAAA,EAC3B;AAAA,EACA,qBAAqB;AAEnB,SAAK,KAAK,IAAI,MAAM;AAClB,WAAK,YAAY;AACjB,WAAK,QAAQ,KAAK,IAAI;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,QAAQ,MAAM;AAElB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,QAAQ;AACjB,UAAI,gBAAgB,aAAa;AAC/B,qBAAa,KAAK,OAAO;AACzB,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,qBAAa,KAAK,OAAO;AACzB,aAAK,KAAK,kBAAkB,MAAM;AAChC,eAAK,UAAU,WAAW,MAAM,MAAM,GAAG,EAAE;AAAA,QAC7C,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,UAAU,WAAW,MAAM,MAAM,CAAC;AAAA,EACzC;AAqBF;AAnBI,qBAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,sBAAqB;AACxD;AAGA,qBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EAC1C,UAAU;AAAA,EACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,WAAW,IAAI,SAAS;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF,CAAC;AAzDL,IAAM,sBAAN;AAAA,CA4DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,sBAAqB;AAwB3B;AAtBI,sBAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AAGA,sBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,QAAQ,eAAe,GAAG,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,EACpF,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,QAAI,KAAK,GAAG;AACV,MAAG,kBAAkB,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,MAAG,aAAa,GAAG,OAAO,CAAC;AAC3B,MAAG,gBAAgB,EAAE;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAtBL,IAAM,uBAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,SAAS,UAAU,KAAK;AAEtB,QAAM,IAAI,QAAQ,kBAAkB,GAAG;AAEvC,QAAM,IAAI,QAAQ,qBAAqB,KAAK;AAE5C,QAAM,IAAI,QAAQ,4BAA4B,EAAE,EAAE,KAAK,EAAE,YAAY;AAErE,QAAM,IAAI,QAAQ,wBAAwB,SAAU,GAAG,GAAG,GAAG;AAC3D,WAAO,EAAE,KAAK,IAAI,EAAE,YAAY;AAAA,EAClC,CAAC;AACD,SAAO;AACT;AAKA,SAAS,YAAY,KAAK;AACxB,SAAO,IAAI,QAAQ,YAAY,WAAS,IAAI,KAAK,EAAE,EAAE,QAAQ,MAAM,WAAS,MAAM,YAAY,CAAC;AACjG;AAMA,SAAS,KAAK;AACZ,UAAQ,UAAU,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE;AAChF;AACA,SAAS,iBAAiB,SAAS,qBAAqB,KAAK;AAC3D,MAAI,gBAAgB;AAEpB,SAAO,QAAQ,IAAI,YAAU;AAC3B,UAAM,OAAO,OAAO,SAAS,OAAO,OAAO,UAAU,OAAO,IAAI,IAAI;AAIpE,UAAM,eAAe,CAAC,CAAC,OAAO,gBAAgB,CAAC;AAC/C,oBAAgB,iBAAiB;AAEjC,WAAO,iCACF,SADE;AAAA,MAEL,MAAM,GAAG;AAAA,MACT,eAAe,cAAc,IAAI;AAAA,MACjC;AAAA,MACA,MAAM,OAAO,SAAS,OAAO,YAAY,OAAO,IAAI,CAAC,IAAI;AAAA,MACzD,YAAY,OAAO,cAAc;AAAA,MACjC,UAAU,OAAO,YAAY;AAAA,MAC7B,YAAY,OAAO,cAAc;AAAA,MACjC,WAAW,OAAO,aAAa;AAAA,MAC/B,eAAe,OAAO,iBAAiB;AAAA,MACvC,OAAO,OAAO,SAAS;AAAA,MACvB;AAAA;AAAA,MAEA,gBAAgB,OAAO;AAAA,MACvB,cAAc,OAAO;AAAA,MACrB,iBAAiB,OAAO;AAAA,MACxB,mBAAmB,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,UAAU,QAAQ,UAAU;AACrC;AACA,IAAM,uBAAuB,IAAI,eAAe,sBAAsB;AAMtE,SAAS,2BAA2B,WAAW;AAC7C,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AACA,IAAM,sBAAN,MAAM,oBAAmB;AAAA;AAAA;AAAA;AAAA,EAIvB,IAAI,KAAK,KAAK;AACZ,SAAK,QAAQ,OAAO,CAAC;AAErB,SAAK,UAAU,KAAK,CAAC,CAAC;AACtB,QAAI,KAAK;AACP,WAAK,gBAAgB,CAAC,GAAG,GAAG;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK;AACP,WAAK,eAAe;AACpB,UAAI,KAAK,cAAc;AAErB,aAAK,cAAc,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ,KAAK;AACf,QAAI,KAAK;AACP,WAAK,mBAAmB,iBAAiB,KAAK,KAAK,mBAAmB;AACtE,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAEd,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAEd,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO,KAAK;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,KAAK,WAAW,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,sBAAsB,KAAK;AAC7B,SAAK,yBAAyB;AAC9B,QAAI,OAAO,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAElD,WAAK,OAAO,CAAC,GAAG,KAAK,MAAM,MAAS;AAAA,IACtC;AAAA,EACF;AAAA,EACA,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,gBAAgB;AAClB,UAAM,eAAe,KAAK;AAC1B,WAAO,OAAO,iBAAiB,WAAW,iBAAiB,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,sBAAsB;AACxB,WAAO,KAAK,kBAAkB,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,kBAAkB;AACpB,WAAO,KAAK,kBAAkB,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,oBAAoB;AACtB,WAAO,KAAK,kBAAkB,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,mBAAmB;AACrB,WAAO,KAAK,kBAAkB,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,wBAAwB;AAC1B,WAAO,KAAK,kBAAkB,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,kBAAkB;AACpB,QAAI,kBAAkB,KAAK,QAAQ,KAAK,YAAY,KAAK,SAAS,WAAW,KAAK,KAAK;AACvF,QAAI,KAAK,iBAAiB,KAAK,qBAAqB;AAClD,YAAM,UAAU,KAAK,cAAc;AACnC,YAAM,aAAa,QAAQ,EAAE,OAAO,QAAQ,EAAE;AAC9C,wBAAkB,KAAK,SAAS,WAAW;AAAA,IAC7C;AACA,WAAO,KAAK,YAAY,KAAK,QAAQ,KAAK,KAAK,WAAW,KAAK;AAAA,EACjE;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,gBAAgB,OAAO,sBAAsB;AAAA,MAChD,UAAU;AAAA,IACZ,CAAC;AAAA,IAED,OAAO,iBAAiB;AAAA,MACtB,UAAU;AAAA,IACZ,CAAC;AAMD,SAAK,WAAW,CAAC;AAIjB,SAAK,aAAa;AAOlB,SAAK,oBAAoB;AAIzB,SAAK,aAAa;AAKlB,SAAK,YAAY;AAKjB,SAAK,aAAa,WAAW;AAK7B,SAAK,eAAe;AAKpB,SAAK,eAAe;AAKpB,SAAK,iBAAiB;AAKtB,SAAK,kBAAkB;AAKvB,SAAK,mBAAmB;AAKxB,SAAK,cAAc;AAKnB,SAAK,cAAc;AAInB,SAAK,WAAW,SAAS;AAKzB,SAAK,QAAQ,CAAC;AAId,SAAK,aAAa,CAAC;AAkBnB,SAAK,WAAW,CAAC;AAMjB,SAAK,wBAAwB;AAK7B,SAAK,sBAAsB;AAI3B,SAAK,iBAAiB;AAItB,SAAK,aAAa;AAIlB,SAAK,gBAAgB;AAIrB,SAAK,kBAAkB;AAKvB,SAAK,eAAe;AAMpB,SAAK,0BAA0B;AAI/B,SAAK,SAAS,IAAI,aAAa;AAI/B,SAAK,WAAW,IAAI,aAAa;AAIjC,SAAK,SAAS,IAAI,aAAa;AAI/B,SAAK,OAAO,IAAI,aAAa;AAI7B,SAAK,OAAO,IAAI,aAAa;AAI7B,SAAK,UAAU,IAAI,aAAa;AAIhC,SAAK,SAAS,IAAI,aAAa;AAM/B,SAAK,mBAAmB,IAAI,aAAa,KAAK;AAI9C,SAAK,aAAa,IAAI,aAAa;AAMnC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,UAAU,OAAO,UAAU,EAAE;AAClC,SAAK,YAAY,OAAO,eAAe,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO;AACzD,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,QAAQ,CAAC;AACd,SAAK,gBAAgB,CAAC;AACtB,SAAK,iBAAiB,CAAC;AACvB,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAK7B,SAAK,eAAe,OAAO,KAAK;AAOhC,SAAK,cAAc,OAAK;AACtB,UAAI,KAAK,cAAc;AAGrB,eAAO,EAAE,OAAO;AAAA,MAClB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,eAAe;AACtB,UAAI,KAAK,cAAc,UAAU;AAC/B,aAAK,WAAW,mBACX,KAAK,cAAc;AAAA,MAE1B;AACA,UAAI,KAAK,cAAc,YAAY;AACjC,aAAK,aAAa,mBACb,KAAK,cAAc;AAAA,MAE1B;AACA,WAAK,eAAe,KAAK,cAAc,gBAAgB,KAAK;AAC5D,WAAK,eAAe,KAAK,cAAc,gBAAgB,KAAK;AAC5D,WAAK,YAAY,KAAK,cAAc,aAAa,KAAK;AACtD,WAAK,sBAAsB,KAAK,cAAc,sBAAsB;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAIT,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAGhB,QAAI,OAAO,0BAA0B,aAAa;AAChD;AAAA,IACF;AACA,0BAAsB,MAAM;AAC1B,WAAK,YAAY;AAEjB,UAAI,KAAK,kBAAkB,KAAK,YAAY;AAC1C,aAAK,KAAK,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,QAAQ;AAC/B,WAAK,iBAAiB,KAAK,eAAe;AAAA,IAC5C;AACA,SAAK,eAAe,KAAK,KAAK,gBAAgB,QAAQ,UAAU,OAAK,KAAK,iBAAiB,CAAC,CAAC,CAAC;AAC9F,SAAK,4BAA4B;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,KAAK;AACpB,QAAI,KAAK;AACP,UAAI,IAAI,QAAQ;AACd,aAAK,mBAAmB,iBAAiB,KAAK,KAAK,mBAAmB;AACtE,aAAK,mBAAmB;AACxB,YAAI,CAAC,KAAK,mBAAmB,KAAK,MAAM,QAAQ;AAC9C,eAAK,iBAAiB;AAAA,QACxB;AACA,aAAK,GAAG,aAAa;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,eAAe,SAAS;AAEnC,UAAM,MAAM,oBAAI,IAAI;AACpB,QAAI,IAAI;AACR,kBAAc,QAAQ,UAAQ;AAC5B,UAAI,CAAC,MAAM;AAET;AAAA,MACF;AACA,YAAM,MAAM,KAAK,OAAO;AACxB,YAAM,QAAQ,IAAI,IAAI,GAAG;AACzB,UAAI,CAAC,OAAO;AACV,YAAI,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,MACrB,OAAO;AACL,cAAM,KAAK,IAAI;AAAA,MACjB;AACA;AAAA,IACF,CAAC;AACD,UAAM,WAAW,CAAC,KAAK,WAAW;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AAEA,WAAO,MAAM,KAAK,KAAK,OAAK,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACV,UAAM,aAAa,KAAK,UAAU,KAAK,KAAK,IAAI;AAChD,QAAI,cAAc,KAAK,iBAAiB;AAEtC,UAAI,CAAC,KAAK,yBAAyB,CAAC,KAAK,mBAAmB,KAAK,kBAAkB;AACjF,aAAK,iBAAiB;AAAA,MACxB,OAAO;AACL,aAAK,gBAAgB,CAAC,GAAG,KAAK,IAAI;AAAA,MACpC;AAEA,WAAK,gBAAgB,mBAAmB,KAAK,eAAe,sBAAsB,KAAK,gBAAgB,GAAG,sBAAsB,KAAK,cAAc,CAAC;AACpJ,UAAI,KAAK,cAAc;AAErB,aAAK,cAAc,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAAA,MACpE;AACA,UAAI,YAAY;AACd,uBAAe,MAAM;AACnB,eAAK,aAAa,IAAI,IAAI;AAC1B,eAAK,YAAY;AACjB,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH;AACA,WAAK,iBAAiB;AACtB,WAAK,GAAG,aAAa;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAU,KAAK,kBAAkB,WAAW,IAAI,aAAa,KAAK,YAAY;AAC/F,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,WAAW,CAAC,OAAO;AACtB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,aAAa;AACtC,SAAK,wBAAwB,aAAa,eAAe,aAAa;AACtE,QAAI,KAAK,cAAc,KAAK,mBAAmB;AAC7C,cAAQ,SAAS,KAAK,yBAAyB,CAAC,KAAK,aAAa,IAAI,KAAK,gBAAgB,QAAQ;AAAA,IACrG;AACA,QAAI,KAAK,eAAe,WAAW,OAAO;AACxC,4BAAsB,SAAS,OAAO,UAAU,YAAY,KAAK,qBAAqB,KAAK,gBAAgB,KAAK;AAAA,IAClH,WAAW,KAAK,eAAe,WAAW,MAAM;AAC9C,yBAAmB,SAAS,KAAK;AAAA,IACnC;AACA,QAAI,KAAK,iBAAiB,KAAK,cAAc,kBAAkB,UAAU,OAAO;AAC9E,WAAK,cAAc,UAAU,CAAC,GAAG,KAAK,gBAAgB;AACtD,WAAK,cAAc,GAAG,aAAa;AAAA,IACrC;AACA,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,mBAAmB,UAAU,OAAO;AACnF,WAAK,gBAAgB,UAAU,CAAC,GAAG,KAAK,gBAAgB;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,UAAM,OAAO,KAAK,QAAQ,sBAAsB;AAChD,SAAK,cAAc,KAAK,MAAM,KAAK,KAAK;AACxC,QAAI,KAAK,YAAY;AACnB,UAAI,SAAS,KAAK;AAClB,UAAI,KAAK,cAAc;AACrB,iBAAS,SAAS,KAAK;AAAA,MACzB;AACA,UAAI,KAAK,cAAc;AACrB,iBAAS,SAAS,KAAK;AAAA,MACzB;AACA,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,SAAK,WAAW,KAAK,aAAa;AAClC,SAAK,WAAW,KAAK,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ;AAIjB,QAAI,KAAK,kBAAkB,CAAC,KAAK,gBAAgB;AAC/C;AAAA,IACF;AACA,SAAK,SAAS;AACd,QAAI,CAAC,MAAM,KAAK,MAAM,GAAG;AACvB,WAAK,KAAK,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,UAAU,KAAK;AAAA,QACf,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAClB,SAAK,WAAW,MAAM;AACtB,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAClB,SAAK,SAAS,MAAM,OAAO;AAC3B,SAAK,cAAc,cAAc,KAAK,MAAM;AAC5C,SAAK,KAAK,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,WAAW,CAAC;AACjB,WAAK,OAAO,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAIb,QAAI,KAAK,cAAc,KAAK,gBAAgB;AAC1C,YAAM,OAAO,KAAK,KAAK,KAAK,aAAa,KAAK,SAAS;AACvD,aAAO,KAAK,IAAI,MAAM,CAAC;AAAA,IACzB;AAEA,QAAI,KAAK,UAAU,QAAW;AAC5B,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,KAAK,MAAM;AACb,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AACxB,UAAI,KAAK,aAAa;AACpB,eAAO,KAAK,YAAY;AAAA,MAC1B,WAAW,KAAK,oBAAoB,QAAQ,KAAK,kBAAkB,MAAM;AACvE,eAAO,KAAK,cAAc;AAAA,MAC5B,OAAO;AACL,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAAA,IAClB;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,iBAAiB,KAAK;AAAA,MACzB;AAAA,MACA,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,iBAAiB,KAAK;AAAA,MACzB;AAAA,MACA,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AAED,QAAI,WAAW,QAAW;AACxB;AAAA,IACF;AACA,UAAM,MAAM,KAAK,iBAAiB,QAAQ,MAAM;AAChD,UAAM,OAAO,KAAK,iBAAiB,IAAI,SAAQ,mBAC1C,IACH;AACF,SAAK,GAAG,EAAE,QAAQ;AAGlB,SAAK,GAAG,EAAE,aAAa;AACvB,SAAK,mBAAmB,MAAM,GAAG;AACjC,SAAK,mBAAmB;AACxB,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,WAAW,QAAW;AACxB;AAAA,IACF;AACA,WAAO,QAAQ;AACf,WAAO,aAAa;AACpB,UAAM,MAAM,KAAK,iBAAiB,QAAQ,MAAM;AAChD,SAAK,mBAAmB,KAAK,kBAAkB,GAAG;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,KAAK,iBAAiB,IAAI,OAAM,mBACxC,EACH;AACF,QAAI,KAAK,aAAa;AACpB,YAAM,UAAU,KAAK,QAAQ;AAC7B,WAAK,QAAQ,IAAI;AACjB,WAAK,SAAS,IAAI;AAAA,IACpB,OAAO;AACL,UAAI,WAAW,WAAW;AACxB,cAAM,WAAW,KAAK,SAAS;AAC/B,iBAAS,IAAI,WAAW,IAAI,UAAU,KAAK;AACzC,eAAK,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QACtB;AACA,aAAK,QAAQ,IAAI;AAAA,MACnB,OAAO;AACL,cAAM,WAAW,KAAK,SAAS;AAC/B,iBAAS,IAAI,WAAW,IAAI,UAAU,KAAK;AACzC,eAAK,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QACtB;AACA,aAAK,QAAQ,IAAI;AAAA,MACnB;AAAA,IACF;AACA,SAAK,mBAAmB;AACxB,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAElB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,WAAW,CAAC;AACjB,WAAK,OAAO,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AACA,SAAK,QAAQ,MAAM;AAGnB,QAAI,KAAK,oBAAoB,OAAO;AAElC,WAAK,iBAAiB;AAAA,IACxB;AAEA,SAAK,gBAAgB,mBAAmB,KAAK,eAAe,sBAAsB,KAAK,gBAAgB,GAAG,sBAAsB,KAAK,cAAc,CAAC;AAEpJ,SAAK,SAAS;AACd,SAAK,cAAc,cAAc,KAAK,MAAM;AAE5C,SAAK,KAAK,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd,CAAC;AACD,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,iBAAiB,KAAK,qBAAqB;AAElD,YAAM,QAAQ,KAAK,cAAc,QAAQ,EAAE;AAC3C,YAAM,OAAO,KAAK,cAAc,QAAQ,EAAE;AAC1C,YAAM,cAAc,KAAK,SAAS,WAAW,OAAO;AAEpD,WAAK,WAAW,CAAC;AAEjB,UAAI,CAAC,aAAa;AAChB,aAAK,SAAS,KAAK,GAAG,KAAK,cAAc,MAAM,OAAO,IAAI,EAAE,OAAO,SAAO,CAAC,CAAC,GAAG,CAAC;AAAA,MAClF;AAAA,IACF,OAAO;AACL,UAAI;AACJ,UAAI,KAAK,iBAAiB;AACxB,uBAAe,KAAK,KAAK,OAAO,SAAO,OAAO,CAAC,KAAK,gBAAgB,GAAG,CAAC;AAAA,MAC1E,OAAO;AACL,uBAAe,KAAK,KAAK,OAAO,SAAO,CAAC,CAAC,GAAG;AAAA,MAC9C;AAEA,YAAM,cAAc,KAAK,SAAS,WAAW,aAAa;AAE1D,WAAK,WAAW,CAAC;AAEjB,UAAI,CAAC,aAAa;AAChB,aAAK,SAAS,KAAK,GAAG,YAAY;AAAA,MACpC;AAAA,IACF;AACA,SAAK,OAAO,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAClB,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAClB,UAAM,MAAM,MAAM;AAElB,UAAM,WAAW,KAAK,MAAM,UAAU,OAAK,KAAK,EAAE,KAAK,cAAc,MAAM,MAAM,IAAI,KAAK,cAAc,CAAC;AACzG,SAAK,WAAW,KAAK;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,kBAAgB,aAAa,YAAY,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAC5B,SAAK,eAAe,KAAK,KAAK,qBAAqB,oBAAoB,UAAU,MAAM;AACrF,UAAI,KAAK,iBAAiB;AACxB,aAAK,gBAAgB,gBAAgB;AAAA,MACvC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,mBAAmB;AAEjB,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,OAAO,QAAQ;AACtC,WAAK,gBAAgB,KAAK;AAE1B,UAAI,KAAK,oBAAoB,KAAK,gBAAgB;AAChD,aAAK,gBAAgB,mBAAmB,KAAK,eAAe,sBAAsB,KAAK,gBAAgB,GAAG,sBAAsB,KAAK,cAAc,CAAC;AAAA,MACtJ;AAAA,IACF;AACA,QAAI,KAAK,eAAe,KAAK,YAAY,QAAQ;AAC/C,YAAM,oBAAoB,KAAK,OAAO,KAAK,iBAAe,YAAY,SAAS,KAAK,YAAY;AAChG,WAAK,cAAc,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAClE,WAAK,cAAc,gBAAgB,KAAK,aAAa,KAAK,kBAAkB,KAAK,OAAO,iBAAiB;AACzG,WAAK,gBAAgB,CAAC,GAAG,KAAK,aAAa;AAAA,IAC7C,OAAO;AACL,WAAK,gBAAgB,SAAS,KAAK,eAAe,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACrF;AAAA,EACF;AAgKF;AA9JI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,EAC7B,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,6BAA6B,CAAC;AAC1D,MAAG,eAAe,UAAU,+BAA+B,CAAC;AAC5D,MAAG,eAAe,UAAU,0BAA0B,CAAC;AACvD,MAAG,eAAe,UAAU,0BAA0B,GAAG,WAAW;AACpE,MAAG,eAAe,UAAU,0BAA0B,CAAC;AAAA,IACzD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAC7D,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AAAA,IACrE;AAAA,EACF;AAAA,EACA,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,wBAAwB,CAAC;AACxC,MAAG,YAAY,0BAA0B,CAAC;AAC1C,MAAG,YAAY,wBAAwB,GAAG,UAAU;AAAA,IACtD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,IACpE;AAAA,EACF;AAAA,EACA,WAAW,CAAC,GAAG,eAAe;AAAA,EAC9B,UAAU;AAAA,EACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,UAAU,SAAS,+CAA+C;AAC9E,eAAO,IAAI,eAAe;AAAA,MAC5B,GAAM,eAAe;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,aAAa,EAAE,aAAa,IAAI,UAAU,EAAE,mBAAmB,IAAI,YAAY,EAAE,eAAe,IAAI,aAAa,EAAE,eAAe,IAAI,WAAW,EAAE,cAAc,IAAI,YAAY,EAAE,sBAAsB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,eAAe,EAAE,oBAAoB,IAAI,iBAAiB,EAAE,mBAAmB,IAAI,gBAAgB,EAAE,yBAAyB,IAAI,qBAAqB;AAAA,IACpb;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,sBAAsB;AAAA,IACtB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,IACjF,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,eAAe;AAAA,IACjE,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,eAAe;AAAA,IACjE,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,IACxE,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,IAC3E,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,IAC5C,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,IAC5C,QAAQ,CAAC,GAAG,UAAU,UAAU,eAAe;AAAA,IAC/C,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,IAC9E,uBAAuB,CAAC,GAAG,yBAAyB,yBAAyB,gBAAgB;AAAA,IAC7F,eAAe;AAAA,IACf,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IAC/D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IAC/D,UAAU;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,uBAAuB,CAAC,GAAG,yBAAyB,yBAAyB,gBAAgB;AAAA,IAC7F,aAAa;AAAA,IACb,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,IACvF,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,IACxE,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,eAAe;AAAA,IACpE,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,IAClE,yBAAyB,CAAC,GAAG,2BAA2B,2BAA2B,gBAAgB;AAAA,IACnG,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB;AAAA,EACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG,oBAAoB,CAAC,CAAC;AAAA,EACzB,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,sBAAsB,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,QAAQ,YAAY,GAAG,SAAS,YAAY,cAAc,cAAc,WAAW,kBAAkB,WAAW,gBAAgB,eAAe,wBAAwB,qBAAqB,sBAAsB,iBAAiB,mBAAmB,iBAAiB,yBAAyB,yBAAyB,GAAG,CAAC,YAAY,KAAK,QAAQ,YAAY,GAAG,QAAQ,YAAY,kBAAkB,UAAU,UAAU,cAAc,eAAe,eAAe,QAAQ,yBAAyB,cAAc,cAAc,kBAAkB,oBAAoB,yBAAyB,kBAAkB,aAAa,YAAY,UAAU,eAAe,WAAW,YAAY,WAAW,aAAa,eAAe,YAAY,cAAc,cAAc,iBAAiB,eAAe,YAAY,eAAe,gBAAgB,cAAc,iBAAiB,mBAAmB,yBAAyB,mBAAmB,gBAAgB,iBAAiB,gBAAgB,GAAG,CAAC,GAAG,YAAY,YAAY,UAAU,gBAAgB,kBAAkB,gBAAgB,sBAAsB,uBAAuB,qBAAqB,iBAAiB,mBAAmB,eAAe,GAAG,CAAC,QAAQ,YAAY,GAAG,QAAQ,UAAU,YAAY,WAAW,UAAU,qBAAqB,SAAS,YAAY,cAAc,cAAc,WAAW,kBAAkB,WAAW,gBAAgB,eAAe,wBAAwB,qBAAqB,sBAAsB,iBAAiB,mBAAmB,iBAAiB,yBAAyB,yBAAyB,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,QAAQ,GAAG,aAAa,GAAG,WAAW,GAAG,CAAC,GAAG,QAAQ,YAAY,YAAY,UAAU,gBAAgB,kBAAkB,gBAAgB,sBAAsB,uBAAuB,qBAAqB,iBAAiB,mBAAmB,eAAe,CAAC;AAAA,EACn4D,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,GAAG;AACtB,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,WAAW,SAAS,qDAAqD;AACrF,eAAO,IAAI,YAAY;AAAA,MACzB,CAAC;AACD,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,oBAAoB,GAAG,2CAA2C,GAAG,IAAI,oBAAoB,CAAC;AACjG,MAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,MAAG,WAAW,QAAQ,SAAS,2DAA2D,QAAQ;AAChG,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,YAAY,SAAS,+DAA+D,QAAQ;AAC7F,eAAO,IAAI,SAAS,KAAK,MAAM;AAAA,MACjC,CAAC,EAAE,kBAAkB,SAAS,qEAAqE,QAAQ;AACzG,eAAO,IAAI,iBAAiB,MAAM;AAAA,MACpC,CAAC,EAAE,UAAU,SAAS,6DAA6D,QAAQ;AACzF,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC,EAAE,UAAU,SAAS,6DAA6D,QAAQ;AACzF,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC,EAAE,cAAc,SAAS,iEAAiE,QAAQ;AACjG,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC,CAAC;AACD,MAAG,aAAa,GAAG,GAAG,CAAC,eAAe,uBAAuB,GAAG,CAAC,IAAI,qBAAqB,EAAE,CAAC,GAAG,kDAAkD,GAAG,CAAC;AACtJ,MAAG,aAAa,GAAG,GAAG,CAAC,eAAe,mBAAmB,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,kDAAkD,GAAG,CAAC;AAC9I,MAAG,aAAa,EAAE;AAClB,MAAG,oBAAoB,GAAG,2CAA2C,GAAG,IAAI,oBAAoB,CAAC;AACjG,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,CAAC;AACd,MAAG,cAAc,IAAI,eAAe,IAAI,EAAE;AAC1C,MAAG,UAAU;AACb,MAAG,WAAW,eAAe,IAAI,WAAW,EAAE,eAAe,IAAI,WAAW,EAAE,QAAQ,IAAI,aAAa,EAAE,yBAAyB,IAAI,qBAAqB,EAAE,cAAc,IAAI,UAAU,EAAE,cAAc,IAAI,UAAU,EAAE,kBAAkB,IAAI,cAAc,EAAE,oBAAoB,IAAI,gBAAgB,EAAE,yBAAyB,IAAI,qBAAqB,EAAE,kBAAkB,IAAI,cAAc,EAAE,aAAa,IAAI,SAAS,EAAE,YAAY,IAAI,QAAQ,EAAE,UAAU,IAAI,MAAM,EAAE,eAAe,IAAI,WAAW,EAAE,WAAW,IAAI,gBAAgB,EAAE,YAAY,IAAI,QAAQ,EAAE,WAAW,IAAI,QAAQ,EAAE,aAAa,IAAI,SAAS,EAAE,eAAe,IAAI,WAAW,EAAE,YAAY,IAAI,QAAQ,EAAE,cAAc,IAAI,WAAW,EAAE,cAAc,IAAI,UAAU,EAAE,iBAAiB,IAAI,aAAa,EAAE,eAAe,IAAI,WAAW,EAAE,YAAY,IAAI,QAAQ,EAAE,eAAe,IAAI,WAAW,EAAE,gBAAgB,IAAI,YAAY,EAAE,cAAc,IAAI,UAAU,EAAE,iBAAiB,IAAI,aAAa,EAAE,mBAAmB,IAAI,eAAe,EAAE,yBAAyB,IAAI,qBAAqB,EAAE,mBAAmB,IAAI,eAAe,EAAE,gBAAgB,IAAI,YAAY,EAAE,iBAAiB,IAAI,aAAa,EAAE,kBAAkB,IAAI,cAAc;AACtqC,MAAG,UAAU,CAAC;AACd,MAAG,cAAc,IAAI,eAAe,IAAI,EAAE;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,cAAc,CAAC,qBAAqB,0BAA0B,wBAAwB,0BAA0B,oBAAoB;AAAA,EACpI,QAAQ,CAAC,kHAAkH;AAAA,EAC3H,iBAAiB;AACnB,CAAC;AA1jCL,IAAM,qBAAN;AA6jCA,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,mBAAmB,WAAW,kBAAkB,IAAI;AAAA,CACjF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG,oBAAoB;AAAA,MACvB,SAAS,CAAC,qBAAqB,0BAA0B,wBAAwB,0BAA0B,oBAAoB;AAAA,MAC/H,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,yGAAyG;AAAA,IACpH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAaH,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,WAAW,MAAM,OAAO;AAAA,MAC3B,WAAW;AAAA,IACb,CAAC;AACD,WAAO,MAAM;AACX,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,UAAM,cAAc,KAAK,YAAY;AACrC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,UAAM,KAAK,YAAY,iBAAiB,GAAG,CAAC,EAAE,QAAQ,WAAS;AAC7D,YAAM,aAAa,YAAY,EAAE;AAAA,IACnC,CAAC;AAAA,EACH;AAeF;AAbI,qBAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,sBAAqB;AACxD;AAGA,qBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,EACnC,QAAQ;AAAA,IACN,UAAU,CAAC,GAAG,UAAU;AAAA,EAC1B;AACF,CAAC;AAjCL,IAAM,sBAAN;AAAA,CAoCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,sBAAN,MAAM,oBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,OAAO,QAAQ,eAAe;AAC5B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,2BAA2B,aAAa,CAAC;AAAA,IACvD;AAAA,EACF;AAgBF;AAdI,oBAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,qBAAoB;AACvD;AAGA,oBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,kCAAkC,oBAAoB,0BAA0B,6BAA6B,+BAA+B,qCAAqC,gCAAgC,8BAA8B,mCAAmC,+BAA+B,0BAA0B,uCAAuC,qBAAqB,0BAA0B,wBAAwB;AAAA,EACnc,SAAS,CAAC,oBAAoB,6BAA6B,+BAA+B,qCAAqC,0BAA0B,gCAAgC,8BAA8B,mCAAmC,+BAA+B,kCAAkC,0BAA0B,uCAAuC,qBAAqB,0BAA0B,wBAAwB;AACrc,CAAC;AAGD,oBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAxBrD,IAAM,qBAAN;AAAA,CA2BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kCAAkC,oBAAoB,0BAA0B,6BAA6B,+BAA+B,qCAAqC,gCAAgC,8BAA8B,mCAAmC,+BAA+B,0BAA0B,uCAAuC,qBAAqB,0BAA0B,wBAAwB;AAAA,MACnc,SAAS,CAAC,oBAAoB,6BAA6B,+BAA+B,qCAAqC,0BAA0B,gCAAgC,8BAA8B,mCAAmC,+BAA+B,kCAAkC,0BAA0B,uCAAuC,qBAAqB,0BAA0B,wBAAwB;AAAA,IACrc,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["Keys", "SortDirection", "SortType", "ColumnMode", "ContextmenuType", "SelectionType", "id"]}