{"version": 3, "sources": ["../../../../../../node_modules/zone.js/fesm2015/zone-error.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @fileoverview\n * @suppress {globalThis,undefinedVars}\n */\nfunction patchError(Zone) {\n    Zone.__load_patch('Error', (global, Zone, api) => {\n        /*\n         * This code patches Error so that:\n         *   - It ignores un-needed stack frames.\n         *   - It Shows the associated Zone for reach frame.\n         */\n        const zoneJsInternalStackFramesSymbol = api.symbol('zoneJsInternalStackFrames');\n        const NativeError = (global[api.symbol('Error')] = global['Error']);\n        // Store the frames which should be removed from the stack frames\n        const zoneJsInternalStackFrames = {};\n        // We must find the frame where Error was created, otherwise we assume we don't understand stack\n        let zoneAwareFrame1;\n        let zoneAwareFrame2;\n        let zoneAwareFrame1WithoutNew;\n        let zoneAwareFrame2WithoutNew;\n        let zoneAwareFrame3WithoutNew;\n        global['Error'] = ZoneAwareError;\n        const stackRewrite = 'stackRewrite';\n        const zoneJsInternalStackFramesPolicy = global['__Zone_Error_BlacklistedStackFrames_policy'] ||\n            global['__Zone_Error_ZoneJsInternalStackFrames_policy'] ||\n            'default';\n        function buildZoneFrameNames(zoneFrame) {\n            let zoneFrameName = { zoneName: zoneFrame.zone.name };\n            let result = zoneFrameName;\n            while (zoneFrame.parent) {\n                zoneFrame = zoneFrame.parent;\n                const parentZoneFrameName = { zoneName: zoneFrame.zone.name };\n                zoneFrameName.parent = parentZoneFrameName;\n                zoneFrameName = parentZoneFrameName;\n            }\n            return result;\n        }\n        function buildZoneAwareStackFrames(originalStack, zoneFrame, isZoneFrame = true) {\n            let frames = originalStack.split('\\n');\n            let i = 0;\n            // Find the first frame\n            while (!(frames[i] === zoneAwareFrame1 ||\n                frames[i] === zoneAwareFrame2 ||\n                frames[i] === zoneAwareFrame1WithoutNew ||\n                frames[i] === zoneAwareFrame2WithoutNew ||\n                frames[i] === zoneAwareFrame3WithoutNew) &&\n                i < frames.length) {\n                i++;\n            }\n            for (; i < frames.length && zoneFrame; i++) {\n                let frame = frames[i];\n                if (frame.trim()) {\n                    switch (zoneJsInternalStackFrames[frame]) {\n                        case 0 /* FrameType.zoneJsInternal */:\n                            frames.splice(i, 1);\n                            i--;\n                            break;\n                        case 1 /* FrameType.transition */:\n                            if (zoneFrame.parent) {\n                                // This is the special frame where zone changed. Print and process it accordingly\n                                zoneFrame = zoneFrame.parent;\n                            }\n                            else {\n                                zoneFrame = null;\n                            }\n                            frames.splice(i, 1);\n                            i--;\n                            break;\n                        default:\n                            frames[i] += isZoneFrame\n                                ? ` [${zoneFrame.zone.name}]`\n                                : ` [${zoneFrame.zoneName}]`;\n                    }\n                }\n            }\n            return frames.join('\\n');\n        }\n        /**\n         * This is ZoneAwareError which processes the stack frame and cleans up extra frames as well as\n         * adds zone information to it.\n         */\n        function ZoneAwareError() {\n            // We always have to return native error otherwise the browser console will not work.\n            let error = NativeError.apply(this, arguments);\n            // Save original stack trace\n            const originalStack = (error['originalStack'] = error.stack);\n            // Process the stack trace and rewrite the frames.\n            if (ZoneAwareError[stackRewrite] && originalStack) {\n                let zoneFrame = api.currentZoneFrame();\n                if (zoneJsInternalStackFramesPolicy === 'lazy') {\n                    // don't handle stack trace now\n                    error[api.symbol('zoneFrameNames')] = buildZoneFrameNames(zoneFrame);\n                }\n                else if (zoneJsInternalStackFramesPolicy === 'default') {\n                    try {\n                        error.stack = error.zoneAwareStack = buildZoneAwareStackFrames(originalStack, zoneFrame);\n                    }\n                    catch (e) {\n                        // ignore as some browsers don't allow overriding of stack\n                    }\n                }\n            }\n            if (this instanceof NativeError && this.constructor != NativeError) {\n                // We got called with a `new` operator AND we are subclass of ZoneAwareError\n                // in that case we have to copy all of our properties to `this`.\n                Object.keys(error)\n                    .concat('stack', 'message', 'cause')\n                    .forEach((key) => {\n                    const value = error[key];\n                    if (value !== undefined) {\n                        try {\n                            this[key] = value;\n                        }\n                        catch (e) {\n                            // ignore the assignment in case it is a setter and it throws.\n                        }\n                    }\n                });\n                return this;\n            }\n            return error;\n        }\n        // Copy the prototype so that instanceof operator works as expected\n        ZoneAwareError.prototype = NativeError.prototype;\n        ZoneAwareError[zoneJsInternalStackFramesSymbol] = zoneJsInternalStackFrames;\n        ZoneAwareError[stackRewrite] = false;\n        const zoneAwareStackSymbol = api.symbol('zoneAwareStack');\n        // try to define zoneAwareStack property when zoneJsInternal frames policy is delay\n        if (zoneJsInternalStackFramesPolicy === 'lazy') {\n            Object.defineProperty(ZoneAwareError.prototype, 'zoneAwareStack', {\n                configurable: true,\n                enumerable: true,\n                get: function () {\n                    if (!this[zoneAwareStackSymbol]) {\n                        this[zoneAwareStackSymbol] = buildZoneAwareStackFrames(this.originalStack, this[api.symbol('zoneFrameNames')], false);\n                    }\n                    return this[zoneAwareStackSymbol];\n                },\n                set: function (newStack) {\n                    this.originalStack = newStack;\n                    this[zoneAwareStackSymbol] = buildZoneAwareStackFrames(this.originalStack, this[api.symbol('zoneFrameNames')], false);\n                },\n            });\n        }\n        // those properties need special handling\n        const specialPropertyNames = ['stackTraceLimit', 'captureStackTrace', 'prepareStackTrace'];\n        // those properties of NativeError should be set to ZoneAwareError\n        const nativeErrorProperties = Object.keys(NativeError);\n        if (nativeErrorProperties) {\n            nativeErrorProperties.forEach((prop) => {\n                if (specialPropertyNames.filter((sp) => sp === prop).length === 0) {\n                    Object.defineProperty(ZoneAwareError, prop, {\n                        get: function () {\n                            return NativeError[prop];\n                        },\n                        set: function (value) {\n                            NativeError[prop] = value;\n                        },\n                    });\n                }\n            });\n        }\n        if (NativeError.hasOwnProperty('stackTraceLimit')) {\n            // Extend default stack limit as we will be removing few frames.\n            NativeError.stackTraceLimit = Math.max(NativeError.stackTraceLimit, 15);\n            // make sure that ZoneAwareError has the same property which forwards to NativeError.\n            Object.defineProperty(ZoneAwareError, 'stackTraceLimit', {\n                get: function () {\n                    return NativeError.stackTraceLimit;\n                },\n                set: function (value) {\n                    return (NativeError.stackTraceLimit = value);\n                },\n            });\n        }\n        if (NativeError.hasOwnProperty('captureStackTrace')) {\n            Object.defineProperty(ZoneAwareError, 'captureStackTrace', {\n                // add named function here because we need to remove this\n                // stack frame when prepareStackTrace below\n                value: function zoneCaptureStackTrace(targetObject, constructorOpt) {\n                    NativeError.captureStackTrace(targetObject, constructorOpt);\n                },\n            });\n        }\n        const ZONE_CAPTURESTACKTRACE = 'zoneCaptureStackTrace';\n        Object.defineProperty(ZoneAwareError, 'prepareStackTrace', {\n            get: function () {\n                return NativeError.prepareStackTrace;\n            },\n            set: function (value) {\n                if (!value || typeof value !== 'function') {\n                    return (NativeError.prepareStackTrace = value);\n                }\n                return (NativeError.prepareStackTrace = function (error, structuredStackTrace) {\n                    // remove additional stack information from ZoneAwareError.captureStackTrace\n                    if (structuredStackTrace) {\n                        for (let i = 0; i < structuredStackTrace.length; i++) {\n                            const st = structuredStackTrace[i];\n                            // remove the first function which name is zoneCaptureStackTrace\n                            if (st.getFunctionName() === ZONE_CAPTURESTACKTRACE) {\n                                structuredStackTrace.splice(i, 1);\n                                break;\n                            }\n                        }\n                    }\n                    return value.call(this, error, structuredStackTrace);\n                });\n            },\n        });\n        if (zoneJsInternalStackFramesPolicy === 'disable') {\n            // don't need to run detectZone to populate zoneJs internal stack frames\n            return;\n        }\n        // Now we need to populate the `zoneJsInternalStackFrames` as well as find the\n        // run/runGuarded/runTask frames. This is done by creating a detect zone and then threading\n        // the execution through all of the above methods so that we can look at the stack trace and\n        // find the frames of interest.\n        let detectZone = Zone.current.fork({\n            name: 'detect',\n            onHandleError: function (parentZD, current, target, error) {\n                if (error.originalStack && Error === ZoneAwareError) {\n                    let frames = error.originalStack.split(/\\n/);\n                    let runFrame = false, runGuardedFrame = false, runTaskFrame = false;\n                    while (frames.length) {\n                        let frame = frames.shift();\n                        // On safari it is possible to have stack frame with no line number.\n                        // This check makes sure that we don't filter frames on name only (must have\n                        // line number or exact equals to `ZoneAwareError`)\n                        if (/:\\d+:\\d+/.test(frame) || frame === 'ZoneAwareError') {\n                            // Get rid of the path so that we don't accidentally find function name in path.\n                            // In chrome the separator is `(` and `@` in FF and safari\n                            // Chrome: at Zone.run (zone.js:100)\n                            // Chrome: at Zone.run (http://localhost:9876/base/build/lib/zone.js:100:24)\n                            // FireFox: Zone.prototype.run@http://localhost:9876/base/build/lib/zone.js:101:24\n                            // Safari: run@http://localhost:9876/base/build/lib/zone.js:101:24\n                            let fnName = frame.split('(')[0].split('@')[0];\n                            let frameType = 1 /* FrameType.transition */;\n                            if (fnName.indexOf('ZoneAwareError') !== -1) {\n                                if (fnName.indexOf('new ZoneAwareError') !== -1) {\n                                    zoneAwareFrame1 = frame;\n                                    zoneAwareFrame2 = frame.replace('new ZoneAwareError', 'new Error.ZoneAwareError');\n                                }\n                                else {\n                                    zoneAwareFrame1WithoutNew = frame;\n                                    zoneAwareFrame2WithoutNew = frame.replace('Error.', '');\n                                    if (frame.indexOf('Error.ZoneAwareError') === -1) {\n                                        zoneAwareFrame3WithoutNew = frame.replace('ZoneAwareError', 'Error.ZoneAwareError');\n                                    }\n                                }\n                                zoneJsInternalStackFrames[zoneAwareFrame2] = 0 /* FrameType.zoneJsInternal */;\n                            }\n                            if (fnName.indexOf('runGuarded') !== -1) {\n                                runGuardedFrame = true;\n                            }\n                            else if (fnName.indexOf('runTask') !== -1) {\n                                runTaskFrame = true;\n                            }\n                            else if (fnName.indexOf('run') !== -1) {\n                                runFrame = true;\n                            }\n                            else {\n                                frameType = 0 /* FrameType.zoneJsInternal */;\n                            }\n                            zoneJsInternalStackFrames[frame] = frameType;\n                            // Once we find all of the frames we can stop looking.\n                            if (runFrame && runGuardedFrame && runTaskFrame) {\n                                ZoneAwareError[stackRewrite] = true;\n                                break;\n                            }\n                        }\n                    }\n                }\n                return false;\n            },\n        });\n        // carefully constructor a stack frame which contains all of the frames of interest which\n        // need to be detected and marked as an internal zoneJs frame.\n        const childDetectZone = detectZone.fork({\n            name: 'child',\n            onScheduleTask: function (delegate, curr, target, task) {\n                return delegate.scheduleTask(target, task);\n            },\n            onInvokeTask: function (delegate, curr, target, task, applyThis, applyArgs) {\n                return delegate.invokeTask(target, task, applyThis, applyArgs);\n            },\n            onCancelTask: function (delegate, curr, target, task) {\n                return delegate.cancelTask(target, task);\n            },\n            onInvoke: function (delegate, curr, target, callback, applyThis, applyArgs, source) {\n                return delegate.invoke(target, callback, applyThis, applyArgs, source);\n            },\n        });\n        // we need to detect all zone related frames, it will\n        // exceed default stackTraceLimit, so we set it to\n        // larger number here, and restore it after detect finish.\n        // We cast through any so we don't need to depend on nodejs typings.\n        const originalStackTraceLimit = Error.stackTraceLimit;\n        Error.stackTraceLimit = 100;\n        // we schedule event/micro/macro task, and invoke them\n        // when onSchedule, so we can get all stack traces for\n        // all kinds of tasks with one error thrown.\n        childDetectZone.run(() => {\n            childDetectZone.runGuarded(() => {\n                const fakeTransitionTo = () => { };\n                childDetectZone.scheduleEventTask(zoneJsInternalStackFramesSymbol, () => {\n                    childDetectZone.scheduleMacroTask(zoneJsInternalStackFramesSymbol, () => {\n                        childDetectZone.scheduleMicroTask(zoneJsInternalStackFramesSymbol, () => {\n                            throw new Error();\n                        }, undefined, (t) => {\n                            t._transitionTo = fakeTransitionTo;\n                            t.invoke();\n                        });\n                        childDetectZone.scheduleMicroTask(zoneJsInternalStackFramesSymbol, () => {\n                            throw Error();\n                        }, undefined, (t) => {\n                            t._transitionTo = fakeTransitionTo;\n                            t.invoke();\n                        });\n                    }, undefined, (t) => {\n                        t._transitionTo = fakeTransitionTo;\n                        t.invoke();\n                    }, () => { });\n                }, undefined, (t) => {\n                    t._transitionTo = fakeTransitionTo;\n                    t.invoke();\n                }, () => { });\n            });\n        });\n        Error.stackTraceLimit = originalStackTraceLimit;\n    });\n}\n\npatchError(Zone);\n"], "mappings": ";AAUA,SAAS,WAAWA,OAAM;AACtB,EAAAA,MAAK,aAAa,SAAS,CAAC,QAAQA,OAAM,QAAQ;AAM9C,UAAM,kCAAkC,IAAI,OAAO,2BAA2B;AAC9E,UAAM,cAAe,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,OAAO,OAAO;AAEjE,UAAM,4BAA4B,CAAC;AAEnC,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,WAAO,OAAO,IAAI;AAClB,UAAM,eAAe;AACrB,UAAM,kCAAkC,OAAO,4CAA4C,KACvF,OAAO,+CAA+C,KACtD;AACJ,aAAS,oBAAoB,WAAW;AACpC,UAAI,gBAAgB,EAAE,UAAU,UAAU,KAAK,KAAK;AACpD,UAAI,SAAS;AACb,aAAO,UAAU,QAAQ;AACrB,oBAAY,UAAU;AACtB,cAAM,sBAAsB,EAAE,UAAU,UAAU,KAAK,KAAK;AAC5D,sBAAc,SAAS;AACvB,wBAAgB;AAAA,MACpB;AACA,aAAO;AAAA,IACX;AACA,aAAS,0BAA0B,eAAe,WAAW,cAAc,MAAM;AAC7E,UAAI,SAAS,cAAc,MAAM,IAAI;AACrC,UAAI,IAAI;AAER,aAAO,EAAE,OAAO,CAAC,MAAM,mBACnB,OAAO,CAAC,MAAM,mBACd,OAAO,CAAC,MAAM,6BACd,OAAO,CAAC,MAAM,6BACd,OAAO,CAAC,MAAM,8BACd,IAAI,OAAO,QAAQ;AACnB;AAAA,MACJ;AACA,aAAO,IAAI,OAAO,UAAU,WAAW,KAAK;AACxC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,KAAK,GAAG;AACd,kBAAQ,0BAA0B,KAAK,GAAG;AAAA,YACtC,KAAK;AACD,qBAAO,OAAO,GAAG,CAAC;AAClB;AACA;AAAA,YACJ,KAAK;AACD,kBAAI,UAAU,QAAQ;AAElB,4BAAY,UAAU;AAAA,cAC1B,OACK;AACD,4BAAY;AAAA,cAChB;AACA,qBAAO,OAAO,GAAG,CAAC;AAClB;AACA;AAAA,YACJ;AACI,qBAAO,CAAC,KAAK,cACP,KAAK,UAAU,KAAK,IAAI,MACxB,KAAK,UAAU,QAAQ;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,OAAO,KAAK,IAAI;AAAA,IAC3B;AAKA,aAAS,iBAAiB;AAEtB,UAAI,QAAQ,YAAY,MAAM,MAAM,SAAS;AAE7C,YAAM,gBAAiB,MAAM,eAAe,IAAI,MAAM;AAEtD,UAAI,eAAe,YAAY,KAAK,eAAe;AAC/C,YAAI,YAAY,IAAI,iBAAiB;AACrC,YAAI,oCAAoC,QAAQ;AAE5C,gBAAM,IAAI,OAAO,gBAAgB,CAAC,IAAI,oBAAoB,SAAS;AAAA,QACvE,WACS,oCAAoC,WAAW;AACpD,cAAI;AACA,kBAAM,QAAQ,MAAM,iBAAiB,0BAA0B,eAAe,SAAS;AAAA,UAC3F,SACO,GAAG;AAAA,UAEV;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,gBAAgB,eAAe,KAAK,eAAe,aAAa;AAGhE,eAAO,KAAK,KAAK,EACZ,OAAO,SAAS,WAAW,OAAO,EAClC,QAAQ,CAAC,QAAQ;AAClB,gBAAM,QAAQ,MAAM,GAAG;AACvB,cAAI,UAAU,QAAW;AACrB,gBAAI;AACA,mBAAK,GAAG,IAAI;AAAA,YAChB,SACO,GAAG;AAAA,YAEV;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAEA,mBAAe,YAAY,YAAY;AACvC,mBAAe,+BAA+B,IAAI;AAClD,mBAAe,YAAY,IAAI;AAC/B,UAAM,uBAAuB,IAAI,OAAO,gBAAgB;AAExD,QAAI,oCAAoC,QAAQ;AAC5C,aAAO,eAAe,eAAe,WAAW,kBAAkB;AAAA,QAC9D,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,KAAK,WAAY;AACb,cAAI,CAAC,KAAK,oBAAoB,GAAG;AAC7B,iBAAK,oBAAoB,IAAI,0BAA0B,KAAK,eAAe,KAAK,IAAI,OAAO,gBAAgB,CAAC,GAAG,KAAK;AAAA,UACxH;AACA,iBAAO,KAAK,oBAAoB;AAAA,QACpC;AAAA,QACA,KAAK,SAAU,UAAU;AACrB,eAAK,gBAAgB;AACrB,eAAK,oBAAoB,IAAI,0BAA0B,KAAK,eAAe,KAAK,IAAI,OAAO,gBAAgB,CAAC,GAAG,KAAK;AAAA,QACxH;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,UAAM,uBAAuB,CAAC,mBAAmB,qBAAqB,mBAAmB;AAEzF,UAAM,wBAAwB,OAAO,KAAK,WAAW;AACrD,QAAI,uBAAuB;AACvB,4BAAsB,QAAQ,CAAC,SAAS;AACpC,YAAI,qBAAqB,OAAO,CAAC,OAAO,OAAO,IAAI,EAAE,WAAW,GAAG;AAC/D,iBAAO,eAAe,gBAAgB,MAAM;AAAA,YACxC,KAAK,WAAY;AACb,qBAAO,YAAY,IAAI;AAAA,YAC3B;AAAA,YACA,KAAK,SAAU,OAAO;AAClB,0BAAY,IAAI,IAAI;AAAA,YACxB;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,YAAY,eAAe,iBAAiB,GAAG;AAE/C,kBAAY,kBAAkB,KAAK,IAAI,YAAY,iBAAiB,EAAE;AAEtE,aAAO,eAAe,gBAAgB,mBAAmB;AAAA,QACrD,KAAK,WAAY;AACb,iBAAO,YAAY;AAAA,QACvB;AAAA,QACA,KAAK,SAAU,OAAO;AAClB,iBAAQ,YAAY,kBAAkB;AAAA,QAC1C;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,YAAY,eAAe,mBAAmB,GAAG;AACjD,aAAO,eAAe,gBAAgB,qBAAqB;AAAA;AAAA;AAAA,QAGvD,OAAO,SAAS,sBAAsB,cAAc,gBAAgB;AAChE,sBAAY,kBAAkB,cAAc,cAAc;AAAA,QAC9D;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,yBAAyB;AAC/B,WAAO,eAAe,gBAAgB,qBAAqB;AAAA,MACvD,KAAK,WAAY;AACb,eAAO,YAAY;AAAA,MACvB;AAAA,MACA,KAAK,SAAU,OAAO;AAClB,YAAI,CAAC,SAAS,OAAO,UAAU,YAAY;AACvC,iBAAQ,YAAY,oBAAoB;AAAA,QAC5C;AACA,eAAQ,YAAY,oBAAoB,SAAU,OAAO,sBAAsB;AAE3E,cAAI,sBAAsB;AACtB,qBAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AAClD,oBAAM,KAAK,qBAAqB,CAAC;AAEjC,kBAAI,GAAG,gBAAgB,MAAM,wBAAwB;AACjD,qCAAqB,OAAO,GAAG,CAAC;AAChC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO,MAAM,KAAK,MAAM,OAAO,oBAAoB;AAAA,QACvD;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,oCAAoC,WAAW;AAE/C;AAAA,IACJ;AAKA,QAAI,aAAaA,MAAK,QAAQ,KAAK;AAAA,MAC/B,MAAM;AAAA,MACN,eAAe,SAAU,UAAU,SAAS,QAAQ,OAAO;AACvD,YAAI,MAAM,iBAAiB,UAAU,gBAAgB;AACjD,cAAI,SAAS,MAAM,cAAc,MAAM,IAAI;AAC3C,cAAI,WAAW,OAAO,kBAAkB,OAAO,eAAe;AAC9D,iBAAO,OAAO,QAAQ;AAClB,gBAAI,QAAQ,OAAO,MAAM;AAIzB,gBAAI,WAAW,KAAK,KAAK,KAAK,UAAU,kBAAkB;AAOtD,kBAAI,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAC7C,kBAAI,YAAY;AAChB,kBAAI,OAAO,QAAQ,gBAAgB,MAAM,IAAI;AACzC,oBAAI,OAAO,QAAQ,oBAAoB,MAAM,IAAI;AAC7C,oCAAkB;AAClB,oCAAkB,MAAM,QAAQ,sBAAsB,0BAA0B;AAAA,gBACpF,OACK;AACD,8CAA4B;AAC5B,8CAA4B,MAAM,QAAQ,UAAU,EAAE;AACtD,sBAAI,MAAM,QAAQ,sBAAsB,MAAM,IAAI;AAC9C,gDAA4B,MAAM,QAAQ,kBAAkB,sBAAsB;AAAA,kBACtF;AAAA,gBACJ;AACA,0CAA0B,eAAe,IAAI;AAAA,cACjD;AACA,kBAAI,OAAO,QAAQ,YAAY,MAAM,IAAI;AACrC,kCAAkB;AAAA,cACtB,WACS,OAAO,QAAQ,SAAS,MAAM,IAAI;AACvC,+BAAe;AAAA,cACnB,WACS,OAAO,QAAQ,KAAK,MAAM,IAAI;AACnC,2BAAW;AAAA,cACf,OACK;AACD,4BAAY;AAAA,cAChB;AACA,wCAA0B,KAAK,IAAI;AAEnC,kBAAI,YAAY,mBAAmB,cAAc;AAC7C,+BAAe,YAAY,IAAI;AAC/B;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAGD,UAAM,kBAAkB,WAAW,KAAK;AAAA,MACpC,MAAM;AAAA,MACN,gBAAgB,SAAU,UAAU,MAAM,QAAQ,MAAM;AACpD,eAAO,SAAS,aAAa,QAAQ,IAAI;AAAA,MAC7C;AAAA,MACA,cAAc,SAAU,UAAU,MAAM,QAAQ,MAAM,WAAW,WAAW;AACxE,eAAO,SAAS,WAAW,QAAQ,MAAM,WAAW,SAAS;AAAA,MACjE;AAAA,MACA,cAAc,SAAU,UAAU,MAAM,QAAQ,MAAM;AAClD,eAAO,SAAS,WAAW,QAAQ,IAAI;AAAA,MAC3C;AAAA,MACA,UAAU,SAAU,UAAU,MAAM,QAAQ,UAAU,WAAW,WAAW,QAAQ;AAChF,eAAO,SAAS,OAAO,QAAQ,UAAU,WAAW,WAAW,MAAM;AAAA,MACzE;AAAA,IACJ,CAAC;AAKD,UAAM,0BAA0B,MAAM;AACtC,UAAM,kBAAkB;AAIxB,oBAAgB,IAAI,MAAM;AACtB,sBAAgB,WAAW,MAAM;AAC7B,cAAM,mBAAmB,MAAM;AAAA,QAAE;AACjC,wBAAgB,kBAAkB,iCAAiC,MAAM;AACrE,0BAAgB,kBAAkB,iCAAiC,MAAM;AACrE,4BAAgB,kBAAkB,iCAAiC,MAAM;AACrE,oBAAM,IAAI,MAAM;AAAA,YACpB,GAAG,QAAW,CAAC,MAAM;AACjB,gBAAE,gBAAgB;AAClB,gBAAE,OAAO;AAAA,YACb,CAAC;AACD,4BAAgB,kBAAkB,iCAAiC,MAAM;AACrE,oBAAM,MAAM;AAAA,YAChB,GAAG,QAAW,CAAC,MAAM;AACjB,gBAAE,gBAAgB;AAClB,gBAAE,OAAO;AAAA,YACb,CAAC;AAAA,UACL,GAAG,QAAW,CAAC,MAAM;AACjB,cAAE,gBAAgB;AAClB,cAAE,OAAO;AAAA,UACb,GAAG,MAAM;AAAA,UAAE,CAAC;AAAA,QAChB,GAAG,QAAW,CAAC,MAAM;AACjB,YAAE,gBAAgB;AAClB,YAAE,OAAO;AAAA,QACb,GAAG,MAAM;AAAA,QAAE,CAAC;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AACD,UAAM,kBAAkB;AAAA,EAC5B,CAAC;AACL;AAEA,WAAW,IAAI;", "names": ["Zone"]}