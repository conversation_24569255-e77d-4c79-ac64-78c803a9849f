{"version": 3, "sources": ["../../../../../../node_modules/@ngrx/operators/fesm2022/ngrx-operators.mjs"], "sourcesContent": ["import { of, EMPTY } from 'rxjs';\nimport { concatMap, withLatestFrom, map, catchError, tap, finalize } from 'rxjs/operators';\n\n/**\n * `concatLatestFrom` combines the source value\n * and the last available value from a lazily evaluated Observable\n * in a new array\n *\n * @usageNotes\n *\n * Select the active customer from the NgRx Store\n *\n * ```ts\n * import { concatLatestFrom } from '@ngrx/operators';\n * import * as fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  concatLatestFrom(() => this.store.select(fromCustomers.selectActiveCustomer))\n * )\n * ```\n *\n * Select a customer from the NgRx Store by its id that is available on the action\n *\n * ```ts\n * import { concatLatestFrom } from '@ngrx/operators';\n * import * fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  concatLatestFrom((action) => this.store.select(fromCustomers.selectCustomer(action.customerId)))\n * )\n * ```\n */\nfunction concatLatestFrom(observablesFactory) {\n    return concatMap((value) => {\n        const observables = observablesFactory(value);\n        const observablesAsArray = Array.isArray(observables)\n            ? observables\n            : [observables];\n        return of(value).pipe(withLatestFrom(...observablesAsArray));\n    });\n}\n\n/**\n * `mapResponse` is a map operator with included error handling.\n * It is similar to `tapResponse`, but allows to map the response as well.\n *\n * The main use case is for NgRx Effects which requires an action to be dispatched.\n *\n * @usageNotes\n * ```ts\n * export const loadAllUsers = createEffect((\n *   actions$ = inject(Actions),\n *   usersService = inject(UsersService)\n * ) => {\n *   return actions$.pipe(\n *     ofType(UsersPageActions.opened),\n *     exhaustMap(() => {\n *       return usersService.getAll().pipe(\n *         mapResponse({\n *           next: (users) => UsersApiActions.usersLoadedSuccess({ users }),\n *           error: (error) => UsersApiActions.usersLoadedFailure({ error }),\n *         })\n *       );\n *     })\n *   );\n * });\n * ```\n */\nfunction mapResponse(observer) {\n    return (source$) => source$.pipe(map((value) => observer.next(value)), catchError((error) => of(observer.error(error))));\n}\n\n/**\n * Handles the response in ComponentStore effects in a safe way, without\n * additional boilerplate. It enforces that the error case is handled and\n * that the effect would still be running should an error occur.\n *\n * Takes optional callbacks for `complete` and `finalize`.\n *\n * @usageNotes\n *\n * ```ts\n * readonly loadUsers = rxMethod<void>(\n *   pipe(\n *     tap(() => this.isLoading.set(true)),\n *     exhaustMap(() =>\n *       this.usersService.getAll().pipe(\n *         tapResponse({\n *           next: (users) => this.users.set(users),\n *           error: (error: HttpErrorResponse) => this.logError(error.message),\n *           finalize: () => this.isLoading.set(false),\n *         })\n *       )\n *     )\n *   )\n * );\n * ```\n */\nfunction tapResponse(observerOrNext, error, complete) {\n    const observer = typeof observerOrNext === 'function'\n        ? {\n            next: observerOrNext,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            error: error,\n            complete,\n        }\n        : observerOrNext;\n    return (source) => source.pipe(tap({ next: observer.next, complete: observer.complete }), catchError((error) => {\n        observer.error(error);\n        return EMPTY;\n    }), observer.finalize ? finalize(observer.finalize) : (source$) => source$);\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { concatLatestFrom, mapResponse, tapResponse };\n\n"], "mappings": ";;;;;;;;;;;;;;;;AAgCA,SAAS,iBAAiB,oBAAoB;AAC1C,SAAO,UAAU,CAAC,UAAU;AACxB,UAAM,cAAc,mBAAmB,KAAK;AAC5C,UAAM,qBAAqB,MAAM,QAAQ,WAAW,IAC9C,cACA,CAAC,WAAW;AAClB,WAAO,GAAG,KAAK,EAAE,KAAK,eAAe,GAAG,kBAAkB,CAAC;AAAA,EAC/D,CAAC;AACL;AA4BA,SAAS,YAAY,UAAU;AAC3B,SAAO,CAAC,YAAY,QAAQ,KAAK,IAAI,CAAC,UAAU,SAAS,KAAK,KAAK,CAAC,GAAG,WAAW,CAAC,UAAU,GAAG,SAAS,MAAM,KAAK,CAAC,CAAC,CAAC;AAC3H;AA4BA,SAAS,YAAY,gBAAgB,OAAO,UAAU;AAClD,QAAM,WAAW,OAAO,mBAAmB,aACrC;AAAA,IACE,MAAM;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,EACJ,IACE;AACN,SAAO,CAAC,WAAW,OAAO,KAAK,IAAI,EAAE,MAAM,SAAS,MAAM,UAAU,SAAS,SAAS,CAAC,GAAG,WAAW,CAACA,WAAU;AAC5G,aAAS,MAAMA,MAAK;AACpB,WAAO;AAAA,EACX,CAAC,GAAG,SAAS,WAAW,SAAS,SAAS,QAAQ,IAAI,CAAC,YAAY,OAAO;AAC9E;", "names": ["error"]}